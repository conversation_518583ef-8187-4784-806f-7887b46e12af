# Sherry

일단 임시로 구글은 Dodo 계정 쓰고있음 만약 로긍니해야한다면 dodo에게 구글 이메일전달 부탁
http://*************:31003/

```ts
const config = {
	goodsOptions: [
		{
			goodsOptionUuid: "0192f588-12e5-7ed3-bc96-9ebd040d2f61",
			count: 1,
		},
		{
			goodsOptionUuid: "0192f592-bfd7-7ed0-876f-e40d2a88ed10",
			count: 2,
		},
		{
			goodsOptionUuid: "0192f593-24df-7330-9393-27b796922939",
			count: 1,
		},
	],
	payMethod: "transfer",
	change: false,
	address: {
		realName: "string",
		mobileNumber: "string",
		road: "string",
		detail: "string",
		postalCode: "string",
		comment: "string",
	},
};
const aa = 299792;

const url =
	"http://localhost:3000/order/test_view?" +
	`goodsOptions=${JSON.stringify(config.goodsOptions)}` +
	`&payMethod=${config.payMethod}` +
	`&address=${JSON.stringify(config.address)}`;

console.log(url);

```

```
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```