{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "es2021",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./src",
    "incremental": true,
    "strict": true,
    "skipLibCheck": true,
    "strictNullChecks": true,
    "noImplicitAny": true,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": false,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "allowJs": true,
    "paths": {
      "@config/*": ["config/*"],
      "@common/*": ["common/*"],
      "@provider/*": ["provider/*"],
      "@module/*": ["module/*"]
    }
  },
  "watchOptions": {
    // Use native file system events for files and directories
    "watchFile": "priorityPollingInterval",
    "watchDirectory": "dynamicprioritypolling",
    // Poll files for updates more frequently
    // when they're updated a lot.
    "fallbackPolling": "dynamicPriority",
    // Don't coalesce watch notification
    "synchronousWatchDirectory": true,
    // Finally, two additional settings for reducing the amount of possible
    // files to track  work from these directories
    "excludeDirectories": ["**/node_modules", "dist"]
  }
}
