import 'reflect-metadata';

process.env = {
  ...process.env,
  APP_NAME: 'sherry',
  NODE_ENV: 'test',
  SERVICE_NAME: 'sherry',

  JWT_ISSUER: 'localhost',
  PII_ENCRYPTION_KEY: 'secretkey',

  DB_HOSTNAME: 'db',
  DB_PORT: '5433',
  PORT: '3000',
  TZ: 'Asia/Seoul',

  DATABASE_URL:
    'postgresql://postgres:postgres@localhost:5433/postgres?schema=public',

  LOG_BUCKET_NAME: 'type-your-bucket-name',

  ACCESS_KEY_ID: 'minio',
  SECRET_ACCESS_KEY: 'miniopass',
  FILE_SERVER_HOST: 'minio',
  FILE_SERVER_PORT: '9000',
  FILE_EXTERNAL_SERVER_HOST: 'external_host',
  FILE_EXTERNAL_SERVER_PORT: '443',

  GOOGLE_CLIENT_ID:
    '172635801873-ucncga5n009p1hpmjff13ae8biehfvla.apps.googleusercontent.com',
  GOOGLE_CLIENT_SECRET: '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',
  APPLE_CLIENT_ID: 'test',
  APPLE_PRIVATE_KEY: 'test',

  ACCESS_TOKEN_SECRET_EXPIRE: '10800',
  REFRESH_TOKEN_SECRET_EXPIRE: '2592000',
};
