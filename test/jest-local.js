require('dotenv').config({
  path: 'development.local.env',
});

module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '..',
  testEnvironment: 'node',
  testRegex: '.spec.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  moduleNameMapper: {
    '^@config/(.*)$': '<rootDir>/src/config/$1',
    '^@common/(.*)$': '<rootDir>/src/common/$1',
    '^@module/(.*)$': '<rootDir>/src/module/$1',
    '^@provider/(.*)$': '<rootDir>/src/provider/$1',
  },
  setupFiles: ['<rootDir>/test/jest-local.env.ts'],
  preset: 'ts-jest',
  clearMocks: true,
};
