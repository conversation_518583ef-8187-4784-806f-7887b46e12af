{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "..", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "moduleDirectories": ["node_modules", "src"], "moduleNameMapper": {"^@config/(.*)$": "<rootDir>/src/config/$1", "^@common/(.*)$": "<rootDir>/src/common/$1", "^@module/(.*)$": "<rootDir>/src/module/$1", "^@provider/(.*)$": "<rootDir>/src/provider/$1"}, "setupFiles": ["<rootDir>/test/jest-local.js"]}