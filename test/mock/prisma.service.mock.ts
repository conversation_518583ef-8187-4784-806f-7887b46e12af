// import { PrismaClient } from '@prisma/client';
// import { DeepMockProxy, mockDeep } from 'jest-mock-extended';
// import { PrismaService } from '@provider/database/prisma.service';
// import { Provider } from '@nestjs/common';
//
// type Context = {
//   prisma: PrismaClient;
// };
//
// type MockContext = {
//   prisma: DeepMockProxy<PrismaClient>;
// };
//
// const createMockContext = (): MockContext => {
//   return {
//     prisma: mockDeep<PrismaClient>(),
//   };
// };
//
// export const mockPrismaService = (): Provider => {
//   const ctx = createMockContext() as unknown as Context;
//   return { provide: PrismaService, useValue: ctx.prisma };
// };
