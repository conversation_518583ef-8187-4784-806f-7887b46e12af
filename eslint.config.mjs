// eslint.config.mjs - ESLint 9 플랫 설정
import { FlatCompat } from '@eslint/eslintrc';
import js from '@eslint/js';
import prettier from 'eslint-plugin-prettier';
import sonarjs from 'eslint-plugin-sonarjs';
import globals from 'globals';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
// TypeScript 타입 문법 제거 (MJS 파일에서는 사용할 수 없음)

// ESM에서 __dirname 획득하기
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 레거시 설정 지원을 위한 호환성 레이어
const compat = new FlatCompat({
  baseDirectory: __dirname,
});

// 타입 주석만 사용 (import 구문 없이)
/** @type {import('eslint').Linter.Config[]} */
const config = [
  // 기본 ESLint 권장 설정
  js.configs.recommended,

  // typescript-eslint 설정 (FlatCompat 사용)
  ...compat.extends('plugin:@typescript-eslint/recommended'),

  // 기본 ESLint 규칙
  {
    files: ['**/*.{js,mjs,cjs,ts}'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      parserOptions: {
        project: './tsconfig.json',
      },
      globals: {
        ...globals.node,
        ...globals.jest,
      },
    },
    plugins: {
      prettier,
      sonarjs,
    },
    rules: {
      // 기본 규칙
      'no-console': 'warn',
      'no-unused-vars': 'off', // typescript-eslint/no-unused-vars로 대체
      'arrow-parens': ['error', 'always'],
      'comma-dangle': ['error', 'always-multiline'],
      quotes: ['error', 'single', { avoidEscape: true }],
      semi: ['error', 'always'],
      eqeqeq: 'error',
      'no-var': 'error',
      curly: ['error', 'all'],
      'space-before-blocks': 'error',
      'arrow-body-style': ['error', 'as-needed'], // 불필요한 변수 할당 후 반환 방지

      // TypeScript 규칙
      '@typescript-eslint/no-unused-vars': [
        'warn',
        { argsIgnorePattern: '^_' },
      ],
      '@typescript-eslint/interface-name-prefix': 'off',
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      '@typescript-eslint/no-explicit-any': 'off',

      // 프로젝트 특정 규칙
      'require-jsdoc': 'off',
      'new-cap': 'off',
      'linebreak-style': 'off',
      'no-return-await': 'warn',
      'object-curly-spacing': 'off',
      indent: 'off',
      'max-len': 'off',

      // SonarJS 규칙
      'sonarjs/cognitive-complexity': 'warn',
      'sonarjs/no-duplicate-string': 'warn',
      'sonarjs/prefer-immediate-return': 'error',

      // Prettier 규칙
      'prettier/prettier': [
        'error',
        {
          singleQuote: true,
          trailingComma: 'all',
          printWidth: 80,
        },
      ],
    },
  },

  // 테스트 파일 규칙 완화
  {
    files: ['**/*.spec.ts', '**/*.test.ts', '**/test/**'],
    rules: {
      'sonarjs/no-duplicate-string': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
    },
  },
];

export default config;
