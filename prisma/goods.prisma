/// 캐릭터
model Character {
  characterUuid       String                @id @default(uuid(7)) @map("character_uuid") @db.Uuid /// 캐릭터 uuid
  sourceUuid          String                @map("source_uuid") @db.Uuid /// 작품 uuid
  name                String /// 캐릭터명
  image               String? /// 캐릭터 이미지
  source              Source                @relation(fields: [sourceUuid], references: [sourceUuid], onDelete: NoAction, onUpdate: NoAction)
  characterSynonym    CharacterSynonym[]
  goodsCharacterPivot GoodsCharacterPivot[]

  @@map("character")
}

/// 캐릭터 동의어
model CharacterSynonym {
  characterUuid String @map("character_uuid") @db.Uuid
  synonym       String /// 동의어

  character Character @relation(fields: [characterUuid], references: [characterUuid], onDelete: Cascade, onUpdate: NoAction)

  @@unique([characterUuid, synonym])
  @@map("character_synonym")
}

/// 작품
model Source {
  sourceUuid String  @id @default(uuid(7)) @map("source_uuid") @db.Uuid
  name       String /// 작품이름 예) 신비아파트
  image      String? /// 작품 이미지

  character        Character[]
  goodsSourcePivot GoodsSourcePivot[]
  sourceSynonym    SourceSynonym[]
  boothSourcePivot BoothSourcePivot[]

  @@map("source")
}

/// 작품 동의어
model SourceSynonym {
  sourceUuid String @map("source_uuid") @db.Uuid /// 작품 
  synonym    String /// 동의어

  source Source @relation(fields: [sourceUuid], references: [sourceUuid], onDelete: Cascade, onUpdate: NoAction)

  @@unique([sourceUuid, synonym])
  @@map("source_synonym")
}

/// 굿즈 종류
model GoodsCategory {
  goodsCategoryUuid    String                 @id @default(uuid(7)) @map("goods_category_uuid") @db.Uuid
  name                 String /// 종류명 예시) 아크릴스탠드 등...
  goods                Goods[]
  goodsCategorySynonym GoodsCategorySynonym[]

  @@map("goods_category")
}

/// 굿즈 종류 동의어
model GoodsCategorySynonym {
  goodsCategoryUuid String @map("goods_category_uuid") @db.Uuid
  synonym           String /// 동의어

  goodsCategory GoodsCategory @relation(fields: [goodsCategoryUuid], references: [goodsCategoryUuid], onDelete: Cascade, onUpdate: NoAction)

  @@unique([goodsCategoryUuid, synonym])
  @@map("goods_category_synonym")
}

/// 굿즈
model Goods {
  goodsUuid          String                   @id @default(uuid(7)) @map("goods_uuid") @db.Uuid
  goodsCategoryUuid  String                   @map("goods_category_uuid") @db.Uuid
  name               String /// 굿즈 이름
  price              Int /// 굿즈 가격
  quantity           Int                      @map("quantity") /// 재고
  boothGoodsQuantity Int                      @default(0) @map("booth_goods_quantity") /// 부스로 나간 재고
  imageUrls          String[]                 @map("image_urls") /// 굿즈 이미지들 첫번째 이미지가 대표이미지
  isSumUp            Boolean                  @map("is_sum_up") /// 합산여부
  descriptions       String[] /// 굿즈 설명 배열값이 주소 = 이미지 , !주소 = 리치텍스트
  currency           GoodsCurrencyEnum        @default(KRW) /// 화폐단위
  favorite           Boolean                  @default(false) // 굿즈 즐겨찾기 여부
  colorTag           String? // POS 작가 지정 색상 태그 (선택사항)
  deletedAt          DateTime?                @map("deleted_at") @db.Timestamptz(6) /// 삭제일시
  isActivated        Boolean                  @default(false) @map("is_activated") /// 활성화 여부
  sellingMethods     GoodsSellingMethodEnum[] @default([]) @map("selling_methods") /// 판매방법
  deliveryFee        Int?                     @map("delivery_fee") /// 배송비

  goodsCategory       GoodsCategory         @relation(fields: [goodsCategoryUuid], references: [goodsCategoryUuid], onDelete: NoAction, onUpdate: NoAction)
  goodsCharacterPivot GoodsCharacterPivot[]
  goodsBoothPivot     GoodsBoothPivot[]
  goodsSourcePivot    GoodsSourcePivot[]
  goodsStats          GoodsStats?
  goodsUser           GoodsUserRatio[]
  GoodsSetPivot       GoodsSetPivot[]

  @@map("goods")
}

/// 굿즈 캐릭터 피봇
model GoodsCharacterPivot {
  characterUuid String    @map("character_uuid") @db.Uuid
  goodsUuid     String    @map("goods_uuid") @db.Uuid
  character     Character @relation(fields: [characterUuid], references: [characterUuid], onDelete: NoAction, onUpdate: NoAction)
  goods         Goods     @relation(fields: [goodsUuid], references: [goodsUuid], onDelete: Cascade, onUpdate: NoAction)

  @@id([characterUuid, goodsUuid])
  @@map("goods_character_pivot")
}

/// 굿즈 부스 피봇
model GoodsBoothPivot {
  goodsUuid       String                   @map("goods_uuid") @db.Uuid
  boothUuid       String                   @map("booth_uuid") @db.Uuid
  orderedCount    Int                      @default(0) @map("ordered_count") /// 판매된 수량
  quantity        Int                      @map("quantity") /// 재고
  advanceCount    Int                      @default(0) @map("advance_count") /// 선입금 수량
  prepareCount    Int                      @default(0) @map("prepare_count") /// 사전결제 수량 (결제 중간에 트레킹을 위한 )
  infoAreas       Int[]                    @map("info_areas")
  deletedAt       DateTime?                @map("deleted_at") @db.Timestamptz(6) /// 삭제일시
  sellingMethods  GoodsSellingMethodEnum[] @default([]) @map("selling_methods") /// 판매방법

  booth Booth @relation(fields: [boothUuid], references: [boothUuid], onDelete: NoAction, onUpdate: NoAction)
  goods Goods @relation(fields: [goodsUuid], references: [goodsUuid], onDelete: Cascade, onUpdate: NoAction)

  @@id([goodsUuid, boothUuid])
  @@map("goods_booth_pivot")
}

/// 굿즈 작품 피봇
model GoodsSourcePivot {
  originUuid String @map("origin_uuid") @db.Uuid
  goodsUuid  String @map("goods_uuid") @db.Uuid
  goods      Goods  @relation(fields: [goodsUuid], references: [goodsUuid], onDelete: Cascade, onUpdate: NoAction)
  source     Source @relation(fields: [originUuid], references: [sourceUuid], onDelete: NoAction, onUpdate: NoAction)

  @@id([originUuid, goodsUuid])
  @@map("goods_source_pivot")
}

/// 굿즈 통계
model GoodsStats {
  goodsUuid  String   @id @map("goods_uuid") @db.Uuid
  viewCount  Int      @default(0) @map("view_count")
  salesCount Int      @default(0) @map("sales_count")
  updatedAt  DateTime @default(now()) @updatedAt() @map("updated_at") @db.Timestamptz
  revenue    BigInt   @default(0) /// 총 수익
  netProfit  BigInt   @default(0) /// 순 이익

  goods Goods @relation(fields: [goodsUuid], references: [goodsUuid], onDelete: Cascade, onUpdate: NoAction)

  @@map("goods_stats")
}

/// 굿즈 제작진
model GoodsUserRatio {
  goodsUuid String @map("goods_uuid") @db.Uuid
  userUuid  String @map("user_uuid") @db.Uuid
  ratio     Int    @db.SmallInt /// 배분 배율 0~100
  goods     Goods  @relation(fields: [goodsUuid], references: [goodsUuid], onDelete: Cascade, onUpdate: NoAction)
  user      User   @relation(fields: [userUuid], references: [userUuid], onDelete: NoAction, onUpdate: NoAction)

  @@id([goodsUuid, userUuid])
  @@map("goods_user")
}

/// 굿즈 세트
model GoodsSet {
  goodsSetUuid  String          @id @default(uuid(7)) @map("goods_set_uuid") @db.Uuid
  name          String /// 세트 이름
  price         Int /// 세트 가격
  GoodsSetPivot GoodsSetPivot[]

  @@map("goods_set")
}

/// 굿즈 세트 피봇
model GoodsSetPivot {
  goodsSetUuid String @map("goods_set_uuid") @db.Uuid
  goodsUuid    String @map("goods_uuid") @db.Uuid
  amount       Int /// 세트에 포함된 굿즈 수량

  goodsSet GoodsSet @relation(fields: [goodsSetUuid], references: [goodsSetUuid], onDelete: Cascade, onUpdate: NoAction)
  goods    Goods    @relation(fields: [goodsUuid], references: [goodsUuid], onDelete: Cascade, onUpdate: NoAction)

  @@id([goodsSetUuid, goodsUuid])
  @@map("goods_set_pivot")
}
