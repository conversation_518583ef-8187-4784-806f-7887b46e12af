# Sherry v0

> Generated by [`prisma-markdown`](https://github.com/samchon/prisma-markdown)

- [default](#default)

## default

```mermaid
erDiagram
"booth" {
  String booth_uuid PK
  String event_uuid FK
  String name
  DateTime participated_dates
  String location_texts
  String banner_image_url
  String info_blocks
  DateTime(6) created_at
  Json coordinates
  String color
  BoothTypeEnum type
  DateTime(6) deleted_at "nullable"
}
"booth_participated_user" {
  String booth_uuid FK
  String user_uuid FK
  DateTime(6) created_at
  Boolean is_admin "nullable"
}
"booth_source_pivot" {
  String booth_uuid FK
  String source_uuid FK
}
"event" {
  String event_uuid PK
  String place_uuid FK
  String name
  DateTime dates
  String address "nullable"
  String image
  String landing_url "nullable"
  String ticket_url "nullable"
  String map_image_url "nullable"
  DateTime(6) created_at
}
"location" {
  String location_uuid PK
  String name
  String road_address
  String detailed_address
  String postal_code
}
"character" {
  String character_uuid PK
  String source_uuid FK
  String name
  String image "nullable"
}
"character_synonym" {
  String character_uuid FK
  String synonym
}
"source" {
  String source_uuid PK
  String name
  String image "nullable"
}
"source_synonym" {
  String source_uuid FK
  String synonym
}
"goods_category" {
  String goods_category_uuid PK
  String name
}
"goods_category_synonym" {
  String goods_category_uuid FK
  String synonym
}
"goods" {
  String goods_uuid PK
  String goods_category_uuid FK
  String name
  Int price
  Int quantity
  Int booth_goods_quantity
  String image_urls
  Boolean is_sum_up
  String descriptions
  GoodsCurrencyEnum currency
  Boolean favorite
  String colorTag "nullable"
  DateTime(6) deleted_at "nullable"
  Boolean is_activated
  GoodsSellingMethodEnum selling_methods
  Int delivery_fee "nullable"
}
"goods_character_pivot" {
  String character_uuid FK
  String goods_uuid FK
}
"goods_booth_pivot" {
  String goods_uuid FK
  String booth_uuid FK
  Int ordered_count
  Int quantity
  Int advance_count
  Int prepare_count
  Int info_areas
  DateTime(6) deleted_at "nullable"
  GoodsSellingMethodEnum selling_methods
}
"goods_source_pivot" {
  String origin_uuid FK
  String goods_uuid FK
}
"goods_stats" {
  String goods_uuid PK,FK
  Int view_count
  Int sales_count
  DateTime updated_at
  BigInt revenue
  BigInt netProfit
}
"goods_user" {
  String goods_uuid FK
  String user_uuid FK
  Int ratio
}
"goods_set" {
  String goods_set_uuid PK
  String name
  Int price
}
"goods_set_pivot" {
  String goods_set_uuid FK
  String goods_uuid FK
  Int amount
}
"order" {
  String order_uuid PK
  String link_key "nullable"
  String user_uuid FK "nullable"
  String seller_uuid FK "nullable"
  String order_title
  Int total_goods_price
  Int total_vat_price
  Int total_delivery_price "nullable"
  PayMethodEnum pay_method
  String order_status
  Int refunded_total_goods_price "nullable"
  Int refunded_total_vat_price "nullable"
  Int refunded_total_delivery_price "nullable"
  String(16) card_type "nullable"
  String card_number "nullable"
  DateTime(6) created_at
  DateTime(6) updated_at "nullable"
}
"delivery_info" {
  String delivery_info_uuid PK
  String order_uuid FK
  String recipient_name
  String recipient_phone
  String recipient_address
  String postal_code
  String comment "nullable"
  String tracking_number "nullable"
  String delivery_company_name "nullable"
  DateTime(6) created_at
}
"ordered_goods" {
  String ordered_goods_uuid PK
  String order_uuid FK
  String delivery_info_uuid FK "nullable"
  String seller_uuid
  Int delivery_status "nullable"
  String goods_order_status
  Int goods_count
  String goods_uuid
  String goods_name
  Int goods_price
  Int vat_price
  Int ratio
  String booth_uuid "nullable"
  String booth_name "nullable"
  Int refunded_goods_price "nullable"
  Int refunded_vat_price "nullable"
  Int exchange_status "nullable"
  DateTime(6) exchange_request_date "nullable"
  String exchange_reason "nullable"
  DateTime(6) exchange_confirmation_date "nullable"
  DateTime(6) cancellation_request_date "nullable"
  String cancellation_reason "nullable"
  DateTime(6) cancellation_confirmation_date "nullable"
  DateTime(6) created_at
  DateTime(6) updated_at "nullable"
}
"sales_order" {
  String order_uuid FK
  String user_uuid FK
  String order_title
  Int total_goods_price
  Int total_vat_price
  Int total_delivery_price "nullable"
  PayMethodEnum pay_method
  String order_status
  DateTime(6) payout "nullable"
  Int refunded_total_goods_price "nullable"
  Int refunded_total_vat_price "nullable"
  Int refunded_total_delivery_price "nullable"
  String(16) card_type "nullable"
  String card_number "nullable"
  DateTime(6) created_at
  DateTime(6) updated_at "nullable"
}
"prepare_order" {
  String order_uuid PK
  String link_key "nullable"
  String user_uuid FK "nullable"
  String order_title
  Int total_goods_price
  Int total_vat_price
  Int total_delivery_price "nullable"
  PayMethodEnum pay_method
  String order_status
  DateTime(6) created_at
}
"prepare_delivery_info" {
  String delivery_info_uuid PK
  String order_uuid FK
  String recipient_name
  String recipient_phone
  String recipient_address
  String postal_code
  String comment "nullable"
  String tracking_number "nullable"
  String delivery_company_name "nullable"
  DateTime(6) created_at
}
"prepare_ordered_goods" {
  String ordered_goods_uuid PK
  String order_uuid FK
  String seller_uuid
  String delivery_info_uuid FK "nullable"
  Int delivery_status "nullable"
  String goods_order_status
  Int goods_count
  String goods_uuid
  String goods_name
  Int goods_price
  Int vat_price
  Int ratio
  String booth_uuid "nullable"
  String booth_name "nullable"
  DateTime(6) created_at
  DateTime(6) updated_at "nullable"
}
"prepare_sales_order" {
  String order_uuid
  String user_uuid FK
  String order_title
  Int total_goods_price
  Int total_vat_price
  Int total_delivery_price "nullable"
  PayMethodEnum pay_method
  String order_status
  Int refunded_total_goods_price "nullable"
  Int refunded_total_vat_price "nullable"
  Int refunded_total_delivery_price "nullable"
  String(16) card_type "nullable"
  String card_number "nullable"
  DateTime(6) created_at
  DateTime(6) updated_at "nullable"
}
"static" {
  Int static_id PK
  String content
}
"ticket" {
  String ticket_uuid PK
  String user_uuid FK
  Int used_day
  Int usable_day
  DateTime expired_at
}
"ticket_history" {
  String ticket_history_uuid PK
  String ticket_uuid FK
  String booth_uuid FK
  DateTime used_date
}
"prepare_ticket" {
  String ticket_uuid PK
  String user_uuid FK
  DateTime used_dates
  Int usable_day
  DateTime created_at
  DateTime expired_at
}
"sns_oauth" {
  String user_id FK
  Int sns_platform_id FK
  String handle "nullable"
  String sns_uid
  String refresh_token "nullable"
}
"sns_platform" {
  Int sns_platform_id PK
  SnsPlatformNameEnum name
}
"user" {
  String user_uuid PK
  String handle UK
  String profile_img "nullable"
  String nickname
  String explain_text "nullable"
  String real_name "nullable"
  String email "nullable"
  DateTime(6) created_at
}
"user_synonym" {
  String user_uuid FK
  String synonym
}
"bank_account" {
  String bank_account_uuid PK
  String user_uuid FK
  String bank_code FK
  String account_number
  String account_holder
  Boolean is_settlement
}
"kakao_pay" {
  String user_uuid PK,FK
  String kakao_uid
}
"bank" {
  String bank_code PK
  String name
}
"booth" }o--|| "event" : event
"booth_participated_user" }o--|| "booth" : booth
"booth_participated_user" }o--|| "user" : user
"booth_source_pivot" }o--|| "booth" : booth
"booth_source_pivot" }o--|| "source" : source
"event" }o--|| "location" : location
"character" }o--|| "source" : source
"character_synonym" }o--|| "character" : character
"source_synonym" }o--|| "source" : source
"goods_category_synonym" }o--|| "goods_category" : goodsCategory
"goods" }o--|| "goods_category" : goodsCategory
"goods_character_pivot" }o--|| "character" : character
"goods_character_pivot" }o--|| "goods" : goods
"goods_booth_pivot" }o--|| "booth" : booth
"goods_booth_pivot" }o--|| "goods" : goods
"goods_source_pivot" }o--|| "goods" : goods
"goods_source_pivot" }o--|| "source" : source
"goods_stats" |o--|| "goods" : goods
"goods_user" }o--|| "goods" : goods
"goods_user" }o--|| "user" : user
"goods_set_pivot" }o--|| "goods_set" : goodsSet
"goods_set_pivot" }o--|| "goods" : goods
"order" }o--o| "user" : user
"order" }o--o| "user" : seller
"delivery_info" }o--|| "order" : order
"ordered_goods" }o--|| "order" : order
"ordered_goods" }o--|| "sales_order" : salesOrder
"ordered_goods" }o--o| "delivery_info" : deliveryInfo
"sales_order" }o--|| "user" : user
"sales_order" }o--|| "order" : order
"prepare_order" }o--o| "user" : user
"prepare_delivery_info" }o--|| "prepare_order" : order
"prepare_ordered_goods" }o--|| "prepare_order" : order
"prepare_ordered_goods" }o--|| "prepare_sales_order" : prepareSalesOrder
"prepare_ordered_goods" }o--o| "prepare_delivery_info" : PrepareDeliveryInfo
"prepare_sales_order" }o--|| "user" : user
"ticket" }o--|| "user" : user
"ticket_history" }o--|| "ticket" : ticket
"ticket_history" }o--|| "booth" : booth
"prepare_ticket" }o--|| "user" : user
"sns_oauth" }o--|| "sns_platform" : snsPlatform
"sns_oauth" }o--|| "user" : user
"user_synonym" }o--|| "user" : user
"bank_account" }o--|| "user" : user
"bank_account" }o--|| "bank" : bank
"kakao_pay" |o--|| "user" : user
```

### `booth`

Properties as follows:

- `booth_uuid`:
- `event_uuid`: 이벤트 uuid
- `name`: 부스 이름
- `participated_dates`: 행사 참여 일시들
- `location_texts`: 부스 위치 문자
- `banner_image_url`: 부스 현수막 이미지 url
- `info_blocks`: 부스 인포 블록 배열
- `created_at`: 부스 생성 일시
- `coordinates`: 행사 지도상에 표시할 부스 위치를 표현하기 위해 필요한 좌표. 예시) [[1,2], [2.3]]
- `color`: node-vibrant 로 뽑은 부스 색상
- `type`: 부스 타입
- `deleted_at`: 삭제일시

### `booth_participated_user`

Properties as follows:

- `booth_uuid`: 부스 uuid
- `user_uuid`: 유저 uuid
- `created_at`:
- `is_admin`: 부스 관리자 여부 이후에 빠질수있으나 일단 냅둠

### `booth_source_pivot`

Properties as follows:

- `booth_uuid`: 부스 uuid
- `source_uuid`: 소스 uuid

### `event`

행사

Properties as follows:

- `event_uuid`: 행사 uuid
- `place_uuid`: 장소 uuid
- `name`: 행사 이름
- `dates`: 행사 날짜
- `address`: 행사 장소명
- `image`: 행사 대표 이미지
- `landing_url`: 행사 홈페이지 주소
- `ticket_url`: 행사 입장권 구매 ㅣㄹㅇ크 주소
- `map_image_url`: 행사 지도(부스 위치 파악용)가 담긴 이미지 주소
- `created_at`:

### `location`

행사 장소

Properties as follows:

- `location_uuid`:
- `name`: 장소명
- `road_address`: 도로명 주소
- `detailed_address`: 상세 주소
- `postal_code`: 우편번호

### `character`

캐릭터

Properties as follows:

- `character_uuid`: 캐릭터 uuid
- `source_uuid`: 작품 uuid
- `name`: 캐릭터명
- `image`: 캐릭터 이미지

### `character_synonym`

캐릭터 동의어

Properties as follows:

- `character_uuid`:
- `synonym`: 동의어

### `source`

작품

Properties as follows:

- `source_uuid`:
- `name`: 작품이름 예) 신비아파트
- `image`: 작품 이미지

### `source_synonym`

작품 동의어

Properties as follows:

- `source_uuid`: 작품
- `synonym`: 동의어

### `goods_category`

굿즈 종류

Properties as follows:

- `goods_category_uuid`:
- `name`: 종류명 예시) 아크릴스탠드 등...

### `goods_category_synonym`

굿즈 종류 동의어

Properties as follows:

- `goods_category_uuid`:
- `synonym`: 동의어

### `goods`

굿즈

Properties as follows:

- `goods_uuid`:
- `goods_category_uuid`:
- `name`: 굿즈 이름
- `price`: 굿즈 가격
- `quantity`: 재고
- `booth_goods_quantity`: 부스로 나간 재고
- `image_urls`: 굿즈 이미지들 첫번째 이미지가 대표이미지
- `is_sum_up`: 합산여부
- `descriptions`: 굿즈 설명 배열값이 주소 = 이미지 , !주소 = 리치텍스트
- `currency`: 화폐단위
- `favorite`:
- `colorTag`:
- `deleted_at`: 삭제일시
- `is_activated`: 활성화 여부
- `selling_methods`: 판매방법
- `delivery_fee`: 배송비

### `goods_character_pivot`

굿즈 캐릭터 피봇

Properties as follows:

- `character_uuid`:
- `goods_uuid`:

### `goods_booth_pivot`

굿즈 부스 피봇

Properties as follows:

- `goods_uuid`:
- `booth_uuid`:
- `ordered_count`: 판매된 수량
- `quantity`: 재고
- `advance_count`: 선입금 수량
- `prepare_count`: 사전결제 수량 (결제 중간에 트레킹을 위한 )
- `info_areas`:
- `deleted_at`: 삭제일시
- `selling_methods`: 판매방법

### `goods_source_pivot`

굿즈 작품 피봇

Properties as follows:

- `origin_uuid`:
- `goods_uuid`:

### `goods_stats`

굿즈 통계

Properties as follows:

- `goods_uuid`:
- `view_count`:
- `sales_count`:
- `updated_at`:
- `revenue`: 총 수익
- `netProfit`: 순 이익

### `goods_user`

굿즈 제작진

Properties as follows:

- `goods_uuid`:
- `user_uuid`:
- `ratio`: 배분 배율 0~100

### `goods_set`

굿즈 세트

Properties as follows:

- `goods_set_uuid`:
- `name`: 세트 이름
- `price`: 세트 가격

### `goods_set_pivot`

굿즈 세트 피봇

Properties as follows:

- `goods_set_uuid`:
- `goods_uuid`:
- `amount`: 세트에 포함된 굿즈 수량

### `order`

Properties as follows:

- `order_uuid`:
- `link_key`: 페이플 link 결제 key
- `user_uuid`: 유저 uuid 비회원인 경우 null
- `seller_uuid`: 판매자 uuid 온라인 판매의 경우 null
- `order_title`: Pg 전송용 타이틀
- `total_goods_price`: 총 굿즈 가격
- `total_vat_price`: 총 부가세 가격
- `total_delivery_price`: 총 배송비 가격 null 이면 배송상품이 없음
- `pay_method`: 결제 방법
- `order_status`: 결제 상태
- `refunded_total_goods_price`: 환불관련
- `refunded_total_vat_price`:
- `refunded_total_delivery_price`:
- `card_type`: 결제 정보 관련
- `card_number`:
- `created_at`: 시간관련
- `updated_at`:

### `delivery_info`

Properties as follows:

- `delivery_info_uuid`:
- `order_uuid`: 주문 uuid
- `recipient_name`: 수령인 이름
- `recipient_phone`: 수령인 전화번호
- `recipient_address`: 수령인 주소
- `postal_code`: 배송지 우편번호
- `comment`: 배송지 요청사항
- `tracking_number`: 택배사 운송장 번호
- `delivery_company_name`: 택배사 이름
- `created_at`:

### `ordered_goods`

Properties as follows:

- `ordered_goods_uuid`:
- `order_uuid`: 주문 uuid
- `delivery_info_uuid`: 배송 정보 uuid
- `seller_uuid`: 판매자 uuid
- `delivery_status`: 배송상태
- `goods_order_status`: 주문 상태
- `goods_count`: 주문 수량
- `goods_uuid`: 굿즈 uuid
- `goods_name`: 굿즈 이름 (결제 당시 캐싱용)
- `goods_price`: 굿즈 가격 (결제 당시 캐싱용)
- `vat_price`: 굿즈 부가세 (결제 당시 캐싱용)
- `ratio`: 배분 비율 0~100 ( 결제 당시 캐싱용 )
- `booth_uuid`: 어떤 부스에서 구매했는지 확인용
- `booth_name`: 부스 이름 (결제 당시 캐싱용)
- `refunded_goods_price`: 환불관련
- `refunded_vat_price`:
- `exchange_status`:
- `exchange_request_date`:
- `exchange_reason`:
- `exchange_confirmation_date`:
- `cancellation_request_date`:
- `cancellation_reason`:
- `cancellation_confirmation_date`:
- `created_at`: 시간 관련
- `updated_at`:

### `sales_order`

Properties as follows:

- `order_uuid`:
- `user_uuid`: 유저 uuid 비회원인 경우 null
- `order_title`: Pg 전송용 타이틀
- `total_goods_price`: 총 굿즈 가격
- `total_vat_price`: 총 부가세 가격
- `total_delivery_price`: 총 배송비 가격 null 이면 배송상품이 없음
- `pay_method`: 결제 방법
- `order_status`: 결제 상태
- `payout`: 정산일시 정산하지 않았다면 null
- `refunded_total_goods_price`: 환불관련
- `refunded_total_vat_price`:
- `refunded_total_delivery_price`:
- `card_type`: 결제 정보 관련
- `card_number`:
- `created_at`: 시간관련
- `updated_at`:

### `prepare_order`

Properties as follows:

- `order_uuid`:
- `link_key`: 페이플 link 결제 key
- `user_uuid`: 유저 uuid 비회원인 경우 null
- `order_title`: Pg 전송용 타이틀
- `total_goods_price`: 총 굿즈 가격
- `total_vat_price`: 총 부가세 가격
- `total_delivery_price`: 총 배송비 가격 null 이면 배송상품이 없음
- `pay_method`: 결제 방법
- `order_status`: 결제 상태
- `created_at`:

### `prepare_delivery_info`

Properties as follows:

- `delivery_info_uuid`:
- `order_uuid`: 주문 uuid
- `recipient_name`: 수령인 이름
- `recipient_phone`: 수령인 전화번호
- `recipient_address`: 수령인 주소
- `postal_code`: 배송지 우편번호
- `comment`: 배송지 요청사항
- `tracking_number`: 택배사 운송장 번호
- `delivery_company_name`: 택배사 이름
- `created_at`:

### `prepare_ordered_goods`

Properties as follows:

- `ordered_goods_uuid`:
- `order_uuid`: 주문 uuid
- `seller_uuid`: 판매자 uuid
- `delivery_info_uuid`: 배송 정보 uuid
- `delivery_status`: 배송상태
- `goods_order_status`: 주문 상태
- `goods_count`: 주문 수량
- `goods_uuid`: 굿즈 uuid
- `goods_name`: 굿즈 이름 (결제 당시 캐싱용)
- `goods_price`: 굿즈 가격 (결제 당시 캐싱용)
- `vat_price`: 굿즈 부가세 (결제 당시 캐싱용)
- `ratio`: 배분 비율 0~100 ( 결제 당시 캐싱용 )
- `booth_uuid`: 어떤 부스에서 구매했는지 확인용
- `booth_name`: 부스 이름 (결제 당시 캐싱용)
- `created_at`: 시간 관련
- `updated_at`:

### `prepare_sales_order`

Properties as follows:

- `order_uuid`:
- `user_uuid`: 판매자 uuid
- `order_title`: Pg 전송용 타이틀
- `total_goods_price`: 총 굿즈 가격
- `total_vat_price`: 총 부가세 가격
- `total_delivery_price`: 총 배송비 가격 null 이면 배송상품이 없음
- `pay_method`: 결제 방법
- `order_status`: 결제 상태
- `refunded_total_goods_price`: 환불관련
- `refunded_total_vat_price`:
- `refunded_total_delivery_price`:
- `card_type`: 결제 정보 관련
- `card_number`:
- `created_at`: 시간관련
- `updated_at`:

### `static`

단순 정적 데이터 약관 동의 등등...

Properties as follows:

- `static_id`: 0: 이용 약관, 1: 개인정보 처리 방침
- `content`:

### `ticket`

이용권

Properties as follows:

- `ticket_uuid`: 이용권 사용 내역 UUID
- `user_uuid`: 유저의 UUID
- `used_day`: 사용된 일 수
- `usable_day`: 이 이용권으로 사용 가능한 일 수
- `expired_at`: 이용권 유통기한 5년

### `ticket_history`

이용권 사용 내역

Properties as follows:

- `ticket_history_uuid`: 이용권 사용 내역 UUID
- `ticket_uuid`: 이용권 UUID
- `booth_uuid`: 부스 UUID
- `used_date`: 적용된 날짜

### `prepare_ticket`

이용권 사전구매

Properties as follows:

- `ticket_uuid`: 이용권 사용 내역 UUID
- `user_uuid`: 유저의 UUID
- `used_dates`: 사용하기로 지정한 기간
- `usable_day`: 이 이용권으로 사용 가능한 일 수
- `created_at`: 해당 이용권 구매일
- `expired_at`: 이용권 유통기한 5년

### `sns_oauth`

sns 연동정보

Properties as follows:

- `user_id`:
- `sns_platform_id`:
- `handle`:
- `sns_uid`:
- `refresh_token`: refresh token

### `sns_platform`

sns 플랫폼

Properties as follows:

- `sns_platform_id`:
- `name`:

### `user`

유저

Properties as follows:

- `user_uuid`:
- `handle`: 쉐리 핸들
- `profile_img`: 프로필 이미지
- `nickname`: 닉네임
- `explain_text`: 자기 소개
- `real_name`: 실명
- `email`: 이메일
- `created_at`:

### `user_synonym`

유저 동의어

Properties as follows:

- `user_uuid`:
- `synonym`: 동의어

### `bank_account`

은행 계좌

Properties as follows:

- `bank_account_uuid`:
- `user_uuid`:
- `bank_code`: 은행 코드
- `account_number`: 계좌 번호
- `account_holder`: 예금주
- `is_settlement`: 정산 계좌 여부

### `kakao_pay`

카카오페이 정보

Properties as follows:

- `user_uuid`:
- `kakao_uid`: 카카오 uid

### `bank`

은행 정보

Properties as follows:

- `bank_code`: 은행 코드
- `name`: 은행 이름
