generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "debian-openssl-3.0.x", "linux-musl-arm64-openssl-3.0.x", "linux-musl-openssl-3.0.x"]
}

generator markdown {
  provider = "prisma-markdown"
  title    = "Sherry v0"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum GoodsCurrencyEnum {
  KRW @map("krw") // 한국 원
  JPY @map("jpy") // 일본 엔
  USD @map("usd") // 미국 달러
  EUR @map("eur") // 유로
  GBP @map("gbp") // 영국 파운드
  CNY @map("cny") // 중국 위안
  AUD @map("aud") // 호주 달러
  CAD @map("cad") // 캐나다 달러
  INR @map("inr") // 인도 루피
  BRL @map("brl") // 브라질 레알
  ZAR @map("zar") // 남아프리카 랜드

  @@map("goods_currency_enum")
}

enum PayMethodEnum {
  card /// 카드 결제
  account /// 쉐리 이용 계좌이체
  directTransfer /// 직접 계좌이체
  cash /// 현금 결제
  share /// 무료 나눔 처리

  @@map("pay_method_enum")
}

enum SnsPlatformNameEnum {
  google
  apple
  x
  instagram
  etc

  @@map("sns_platform_name_enum")
}

enum PaymentStatusEnum {
  ORDER_COMPLETE       @map("order_complete") /// 주문 완료
  ORDER_CANCEL_REQUEST @map("order_cancel_request") /// 주문 취소 신청
  EXCHANGE_REQUEST     @map("exchange_request") /// 교환 신청
  ORDER_CANCEL         @map("order_cancel") /// 주문 취소
  PARTIAL_ORDER_CANCEL @map("partial_order_cancel") /// 부분 주문 취소
  PURCHASE_CONFIRMED   @map("purchase_confirmed") /// 구매 확정

  @@map("payment_status_enum")
}

enum RefundReasonEnum {
  CHANGE_COLOR_SIZE        @map("change_color_size")
  ORDER_MISTAKES           @map("order_mistakes")
  DAMAGE_AND_DEFECTS       @map("damage_and_defects")
  MISDELIVERIES_AND_DELAYS @map("misdeliveries_and_delays")

  @@map("refund_reason_enum")
}

enum RefundStatusEnum {
  REQUEST  @map("request")
  COMPLETE @map("complete")
  REJECTED @map("rejected")

  @@map("refund_status_enum")
}

enum DeliveryStatusEnum {
  PREPARING  @map("preparing")
  REQUESTED  @map("requested")
  IN_TRANSIT @map("in_transit")
  ON_THE_WAY @map("on_the_way")
  DELIVERED  @map("delivered")

  @@map("delivery_status_enum")
}

enum DeliveryTypeEnum {
  DEFAULT  @map("default")
  REFUND   @map("refund")
  EXCHANGE @map("exchange")

  @@map("delivery_type_enum")
}

enum GoodsSellingMethodEnum {
  PRE_ORDER @map("pre_order")
  ONLINE @map("online")
  IN_PLACE @map("in_place")
}

enum BoothTypeEnum {
  ONLINE @map("online")
  OFFLINE @map("offline")
}