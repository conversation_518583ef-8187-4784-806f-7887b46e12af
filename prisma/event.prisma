/// 행사
model Event {
  eventUuid   String     @id @default(uuid(7)) @map("event_uuid") @db.Uuid /// 행사 uuid
  placeUuid   String     @map("place_uuid") @db.Uuid /// 장소 uuid
  name        String     @map("name") /// 행사 이름
  dates       DateTime[] @map("dates") @db.Timestamptz /// 행사 날짜
  address     String?    @map("address") /// 행사 장소명
  image       String     @map("image") /// 행사 대표 이미지
  landingUrl  String?    @map("landing_url") /// 행사 홈페이지 주소
  ticketUrl   String?    @map("ticket_url") /// 행사 입장권 구매 ㅣㄹㅇ크 주소
  mapImageUrl String?    @map("map_image_url") /// 행사 지도(부스 위치 파악용)가 담긴 이미지 주소
  createdAt   DateTime   @map("created_at") @db.Timestamptz(6)

  booth    Booth[]
  location Location @relation(fields: [placeUuid], references: [locationUuid], onDelete: NoAction, onUpdate: NoAction)

  @@map("event")
}

/// 행사 장소
model Location {
  locationUuid    String @id @default(uuid(7)) @map("location_uuid") @db.Uuid
  name            String @map("name") /// 장소명
  roadAddress     String @map("road_address") /// 도로명 주소 
  detailedAddress String @map("detailed_address") /// 상세 주소
  postalCode      String @map("postal_code") /// 우편번호

  event Event[]

  @@map("location")
}
