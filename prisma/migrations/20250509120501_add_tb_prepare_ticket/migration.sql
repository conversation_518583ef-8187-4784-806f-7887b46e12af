-- CreateTable
CREATE TABLE "prepare_ticket" (
    "ticket_uuid" UUID NOT NULL,
    "user_uuid" UUID NOT NULL,
    "used_dates" TIMESTAMP(3)[],
    "usable_day" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expired_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "prepare_ticket_pkey" PRIMARY KEY ("ticket_uuid")
);

-- AddForeignKey
ALTER TABLE "prepare_ticket" ADD CONSTRAINT "prepare_ticket_user_uuid_fkey" FOREIGN KEY ("user_uuid") REFERENCES "user"("user_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;
