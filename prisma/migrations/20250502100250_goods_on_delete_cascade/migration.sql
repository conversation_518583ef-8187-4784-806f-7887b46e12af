-- DropFore<PERSON><PERSON>ey
ALTER TABLE "goods_booth_pivot" DROP CONSTRAINT "goods_booth_pivot_goods_uuid_fkey";

-- DropForeignKey
ALTER TABLE "goods_character_pivot" DROP CONSTRAINT "goods_character_pivot_goods_uuid_fkey";

-- DropForeignKey
ALTER TABLE "goods_source_pivot" DROP CONSTRAINT "goods_source_pivot_goods_uuid_fkey";

-- DropForeignKey
ALTER TABLE "goods_stats" DROP CONSTRAINT "goods_stats_goods_uuid_fkey";

-- DropForeignKey
ALTER TABLE "goods_user" DROP CONSTRAINT "goods_user_goods_uuid_fkey";

-- AddForeignKey
ALTER TABLE "goods_character_pivot" ADD CONSTRAINT "goods_character_pivot_goods_uuid_fkey" FOREIGN KEY ("goods_uuid") REFERENCES "goods"("goods_uuid") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_booth_pivot" ADD CONSTRAINT "goods_booth_pivot_goods_uuid_fkey" FOREIGN KEY ("goods_uuid") REFERENCES "goods"("goods_uuid") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_source_pivot" ADD CONSTRAINT "goods_source_pivot_goods_uuid_fkey" FOREIGN KEY ("goods_uuid") REFERENCES "goods"("goods_uuid") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_stats" ADD CONSTRAINT "goods_stats_goods_uuid_fkey" FOREIGN KEY ("goods_uuid") REFERENCES "goods"("goods_uuid") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_user" ADD CONSTRAINT "goods_user_goods_uuid_fkey" FOREIGN KEY ("goods_uuid") REFERENCES "goods"("goods_uuid") ON DELETE CASCADE ON UPDATE NO ACTION;
