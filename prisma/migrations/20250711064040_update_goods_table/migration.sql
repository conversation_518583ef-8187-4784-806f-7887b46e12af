-- DropForeignKey
ALTER TABLE "sns_oauth" DROP CONSTRAINT "sns_oauth_user_id_fkey";

-- AlterTable
ALTER TABLE "goods" RENAME COLUMN "sales_count" TO "quantity";

-- AlterTable
ALTER TABLE "goods" ADD COLUMN "booth_goods_quantity" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "order" ADD COLUMN     "seller_uuid" UUID;

-- AddForeignKey
ALTER TABLE "order" ADD CONSTRAINT "order_seller_uuid_fkey" FOREIGN KEY ("seller_uuid") REFERENCES "user"("user_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "sns_oauth" ADD CONSTRAINT "sns_oauth_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user"("user_uuid") ON DELETE CASCADE ON UPDATE CASCADE;
