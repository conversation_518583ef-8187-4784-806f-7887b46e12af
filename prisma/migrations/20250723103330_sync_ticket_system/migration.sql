/*
  Warnings:

  - You are about to drop the column `created_at` on the `ticket` table. All the data in the column will be lost.
  - You are about to drop the column `used_dates` on the `ticket` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "ticket" DROP COLUMN "created_at",
DROP COLUMN "used_dates",
ADD COLUMN     "used_day" INTEGER NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE "ticket_history" (
    "ticket_history_uuid" UUID NOT NULL,
    "ticket_uuid" UUID NOT NULL,
    "booth_uuid" UUID NOT NULL,
    "used_date" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ticket_history_pkey" PRIMARY KEY ("ticket_history_uuid")
);

-- AddForeignKey
ALTER TABLE "ticket_history" ADD CONSTRAINT "ticket_history_ticket_uuid_fkey" FOREIGN KEY ("ticket_uuid") REFERENCES "ticket"("ticket_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ticket_history" ADD CONSTRAINT "ticket_history_booth_uuid_fkey" FOREIGN KEY ("booth_uuid") REFERENCES "booth"("booth_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;
