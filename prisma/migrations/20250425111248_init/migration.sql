-- CreateEnum
CREATE TYPE "goods_currency_enum" AS ENUM ('krw', 'jpy', 'usd');

-- CreateEnum
CREATE TYPE "pay_method_enum" AS ENUM ('card', 'account', 'directTransfer', 'cash');

-- CreateEnum
CREATE TYPE "sns_platform_name_enum" AS ENUM ('instagram', 'x', 'google', 'apple', 'etc');

-- CreateTable
CREATE TABLE "booth" (
    "booth_uuid" UUID NOT NULL,
    "event_uuid" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "participated_dates" TIMESTAMP[],
    "location_texts" TEXT[],
    "genres" TEXT[],
    "banner_image_url" TEXT NOT NULL,
    "info_blocks" TEXT[],
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "booth_pkey" PRIMARY KEY ("booth_uuid")
);

-- CreateTable
CREATE TABLE "booth_participated_user" (
    "booth_uuid" UUID NOT NULL,
    "user_uuid" UUID NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_admin" BOOLEAN,

    CONSTRAINT "booth_participated_user_pkey" PRIMARY KEY ("booth_uuid","user_uuid")
);

-- CreateTable
CREATE TABLE "event" (
    "event_uuid" UUID NOT NULL,
    "place_uuid" UUID NOT NULL,
    "event_name" TEXT NOT NULL,
    "event_date" TIMESTAMP[],
    "event_location" TEXT,
    "event_img" TEXT NOT NULL,
    "event_landing_url" TEXT,
    "event_ticket_url" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "event_pkey" PRIMARY KEY ("event_uuid")
);

-- CreateTable
CREATE TABLE "event_map" (
    "map_uuid" UUID NOT NULL,
    "booth_uuid" UUID NOT NULL,
    "event_uuid" UUID NOT NULL,
    "event_map_img" TEXT NOT NULL,
    "booth_name" TEXT NOT NULL,
    "booth_location_xy" JSONB[] DEFAULT ARRAY[]::JSONB[],
    "created_at" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "event_map_pkey" PRIMARY KEY ("map_uuid")
);

-- CreateTable
CREATE TABLE "location" (
    "location_uuid" UUID NOT NULL,
    "location_name" TEXT NOT NULL,
    "location_road_address" TEXT NOT NULL,
    "location_detailed_address" TEXT NOT NULL,
    "location_postal_code" TEXT NOT NULL,

    CONSTRAINT "location_pkey" PRIMARY KEY ("location_uuid")
);

-- CreateTable
CREATE TABLE "character" (
    "character_uuid" UUID NOT NULL,
    "source_uuid" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "image" TEXT,

    CONSTRAINT "character_pkey" PRIMARY KEY ("character_uuid")
);

-- CreateTable
CREATE TABLE "character_synonym" (
    "character_uuid" UUID NOT NULL,
    "synonym" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "source" (
    "source_uuid" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "image" TEXT,

    CONSTRAINT "source_pkey" PRIMARY KEY ("source_uuid")
);

-- CreateTable
CREATE TABLE "source_synonym" (
    "source_uuid" UUID NOT NULL,
    "synonym" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "goods_category" (
    "goods_category_uuid" UUID NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "goods_category_pkey" PRIMARY KEY ("goods_category_uuid")
);

-- CreateTable
CREATE TABLE "goods_category_synonym" (
    "goods_category_uuid" UUID NOT NULL,
    "synonym" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "goods" (
    "goods_uuid" UUID NOT NULL,
    "goods_category_uuid" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "price" INTEGER NOT NULL,
    "sales_count" INTEGER NOT NULL,
    "image_urls" TEXT[],
    "ship_price" INTEGER,
    "is_sum_up" BOOLEAN NOT NULL,
    "descriptions" TEXT[],
    "currency" "goods_currency_enum" NOT NULL DEFAULT 'krw',

    CONSTRAINT "goods_pkey" PRIMARY KEY ("goods_uuid")
);

-- CreateTable
CREATE TABLE "goods_character_pivot" (
    "character_uuid" UUID NOT NULL,
    "goods_uuid" UUID NOT NULL,

    CONSTRAINT "goods_character_pivot_pkey" PRIMARY KEY ("character_uuid","goods_uuid")
);

-- CreateTable
CREATE TABLE "goods_booth_pivot" (
    "goods_uuid" UUID NOT NULL,
    "booth_uuid" UUID NOT NULL,
    "ordered_count" INTEGER NOT NULL DEFAULT 0,
    "sales_count" INTEGER NOT NULL,
    "prepare_count" INTEGER NOT NULL DEFAULT 0,
    "info_areas" INTEGER[],

    CONSTRAINT "goods_booth_pivot_pkey" PRIMARY KEY ("goods_uuid","booth_uuid")
);

-- CreateTable
CREATE TABLE "goods_source_pivot" (
    "origin_uuid" UUID NOT NULL,
    "goods_uuid" UUID NOT NULL,

    CONSTRAINT "goods_source_pivot_pkey" PRIMARY KEY ("origin_uuid","goods_uuid")
);

-- CreateTable
CREATE TABLE "goods_stats" (
    "goods_uuid" UUID NOT NULL,
    "goods_total_view" INTEGER NOT NULL DEFAULT 0,
    "goods_total_buy" INTEGER NOT NULL DEFAULT 0,
    "goods_stat_date" TIMESTAMP(6) NOT NULL,

    CONSTRAINT "goods_stats_pkey" PRIMARY KEY ("goods_uuid")
);

-- CreateTable
CREATE TABLE "goods_user" (
    "goods_uuid" UUID NOT NULL,
    "user_uuid" UUID NOT NULL,
    "ratio" SMALLINT NOT NULL,

    CONSTRAINT "goods_user_pkey" PRIMARY KEY ("goods_uuid","user_uuid")
);

-- CreateTable
CREATE TABLE "order" (
    "order_uuid" UUID NOT NULL,
    "link_key" TEXT,
    "user_uuid" UUID,
    "order_title" TEXT NOT NULL,
    "total_goods_price" INTEGER NOT NULL,
    "total_vat_price" INTEGER NOT NULL,
    "total_delivery_price" INTEGER,
    "pay_method" "pay_method_enum" NOT NULL,
    "order_status" INTEGER NOT NULL,
    "refunded_total_goods_price" INTEGER,
    "refunded_total_vat_price" INTEGER,
    "refunded_total_delivery_price" INTEGER,
    "card_type" VARCHAR(16),
    "card_number" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6),

    CONSTRAINT "order_pkey" PRIMARY KEY ("order_uuid")
);

-- CreateTable
CREATE TABLE "ordered_goods" (
    "ordered_goods_uuid" UUID NOT NULL,
    "order_uuid" UUID NOT NULL,
    "delivery_status" INTEGER,
    "goods_order_status" INTEGER NOT NULL,
    "goods_count" INTEGER NOT NULL,
    "goods_uuid" UUID NOT NULL,
    "goods_name" TEXT NOT NULL,
    "goods_price" INTEGER NOT NULL,
    "vat_price" INTEGER NOT NULL,
    "booth_uuid" UUID,
    "refunded_goods_price" INTEGER,
    "refunded_vat_price" INTEGER,
    "exchange_status" INTEGER,
    "exchange_request_date" TIMESTAMP(6),
    "exchange_reason" TEXT,
    "exchange_confirmation_date" TIMESTAMP(6),
    "cancellation_request_date" TIMESTAMP(6),
    "cancellation_reason" TEXT,
    "cancellation_confirmation_date" TIMESTAMP(6),
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6),

    CONSTRAINT "ordered_goods_pkey" PRIMARY KEY ("ordered_goods_uuid")
);

-- CreateTable
CREATE TABLE "prepare_order" (
    "order_uuid" UUID NOT NULL,
    "link_key" TEXT,
    "user_uuid" UUID,
    "order_title" TEXT NOT NULL,
    "total_goods_price" INTEGER NOT NULL,
    "total_vat_price" INTEGER NOT NULL,
    "total_delivery_price" INTEGER,
    "pay_method" "pay_method_enum" NOT NULL,
    "order_status" INTEGER NOT NULL,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "prepare_order_pkey" PRIMARY KEY ("order_uuid")
);

-- CreateTable
CREATE TABLE "prepare_ordered_goods" (
    "ordered_goods_uuid" UUID NOT NULL,
    "order_uuid" UUID NOT NULL,
    "delivery_status" INTEGER,
    "goods_order_status" INTEGER NOT NULL,
    "goods_count" INTEGER NOT NULL,
    "goods_uuid" UUID NOT NULL,
    "goods_name" TEXT NOT NULL,
    "goods_price" INTEGER NOT NULL,
    "vat_price" INTEGER NOT NULL,
    "booth_uuid" UUID,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6),

    CONSTRAINT "prepare_ordered_goods_pkey" PRIMARY KEY ("ordered_goods_uuid")
);

-- CreateTable
CREATE TABLE "static" (
    "static_id" SMALLSERIAL NOT NULL,
    "content" TEXT NOT NULL,

    CONSTRAINT "static_pkey" PRIMARY KEY ("static_id")
);

-- CreateTable
CREATE TABLE "sns_oauth" (
    "user_id" UUID NOT NULL,
    "sns_platform_id" SMALLINT NOT NULL,
    "handle" TEXT,
    "sns_uid" TEXT NOT NULL,

    CONSTRAINT "sns_oauth_pkey" PRIMARY KEY ("user_id","sns_platform_id")
);

-- CreateTable
CREATE TABLE "sns_platform" (
    "sns_platform_id" SMALLSERIAL NOT NULL,
    "name" "sns_platform_name_enum" NOT NULL,

    CONSTRAINT "sns_platform_pkey" PRIMARY KEY ("sns_platform_id")
);

-- CreateTable
CREATE TABLE "user" (
    "user_uuid" UUID NOT NULL,
    "handle" TEXT NOT NULL,
    "profile_img" TEXT,
    "nickname" TEXT NOT NULL,
    "explain_text" TEXT,
    "real_name" TEXT,
    "email" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_pkey" PRIMARY KEY ("user_uuid")
);

-- CreateTable
CREATE TABLE "user_synonym" (
    "user_uuid" UUID NOT NULL,
    "synonym" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "character_synonym_character_uuid_synonym_key" ON "character_synonym"("character_uuid", "synonym");

-- CreateIndex
CREATE UNIQUE INDEX "source_synonym_source_uuid_synonym_key" ON "source_synonym"("source_uuid", "synonym");

-- CreateIndex
CREATE UNIQUE INDEX "goods_category_synonym_goods_category_uuid_synonym_key" ON "goods_category_synonym"("goods_category_uuid", "synonym");

-- CreateIndex
CREATE UNIQUE INDEX "user_handle_key" ON "user"("handle");

-- CreateIndex
CREATE UNIQUE INDEX "user_synonym_user_uuid_synonym_key" ON "user_synonym"("user_uuid", "synonym");

-- AddForeignKey
ALTER TABLE "booth" ADD CONSTRAINT "booth_event_uuid_fkey" FOREIGN KEY ("event_uuid") REFERENCES "event"("event_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "booth_participated_user" ADD CONSTRAINT "booth_participated_user_booth_uuid_fkey" FOREIGN KEY ("booth_uuid") REFERENCES "booth"("booth_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "booth_participated_user" ADD CONSTRAINT "booth_participated_user_user_uuid_fkey" FOREIGN KEY ("user_uuid") REFERENCES "user"("user_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "event" ADD CONSTRAINT "event_place_uuid_fkey" FOREIGN KEY ("place_uuid") REFERENCES "location"("location_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "event_map" ADD CONSTRAINT "event_map_booth_uuid_fkey" FOREIGN KEY ("booth_uuid") REFERENCES "booth"("booth_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "event_map" ADD CONSTRAINT "event_map_event_uuid_fkey" FOREIGN KEY ("event_uuid") REFERENCES "event"("event_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "character" ADD CONSTRAINT "character_source_uuid_fkey" FOREIGN KEY ("source_uuid") REFERENCES "source"("source_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "character_synonym" ADD CONSTRAINT "character_synonym_character_uuid_fkey" FOREIGN KEY ("character_uuid") REFERENCES "character"("character_uuid") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "source_synonym" ADD CONSTRAINT "source_synonym_source_uuid_fkey" FOREIGN KEY ("source_uuid") REFERENCES "source"("source_uuid") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_category_synonym" ADD CONSTRAINT "goods_category_synonym_goods_category_uuid_fkey" FOREIGN KEY ("goods_category_uuid") REFERENCES "goods_category"("goods_category_uuid") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods" ADD CONSTRAINT "goods_goods_category_uuid_fkey" FOREIGN KEY ("goods_category_uuid") REFERENCES "goods_category"("goods_category_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_character_pivot" ADD CONSTRAINT "goods_character_pivot_character_uuid_fkey" FOREIGN KEY ("character_uuid") REFERENCES "character"("character_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_character_pivot" ADD CONSTRAINT "goods_character_pivot_goods_uuid_fkey" FOREIGN KEY ("goods_uuid") REFERENCES "goods"("goods_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_booth_pivot" ADD CONSTRAINT "goods_booth_pivot_booth_uuid_fkey" FOREIGN KEY ("booth_uuid") REFERENCES "booth"("booth_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_booth_pivot" ADD CONSTRAINT "goods_booth_pivot_goods_uuid_fkey" FOREIGN KEY ("goods_uuid") REFERENCES "goods"("goods_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_source_pivot" ADD CONSTRAINT "goods_source_pivot_goods_uuid_fkey" FOREIGN KEY ("goods_uuid") REFERENCES "goods"("goods_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_source_pivot" ADD CONSTRAINT "goods_source_pivot_origin_uuid_fkey" FOREIGN KEY ("origin_uuid") REFERENCES "source"("source_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_stats" ADD CONSTRAINT "goods_stats_goods_uuid_fkey" FOREIGN KEY ("goods_uuid") REFERENCES "goods"("goods_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_user" ADD CONSTRAINT "goods_user_goods_uuid_fkey" FOREIGN KEY ("goods_uuid") REFERENCES "goods"("goods_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_user" ADD CONSTRAINT "goods_user_user_uuid_fkey" FOREIGN KEY ("user_uuid") REFERENCES "user"("user_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "order" ADD CONSTRAINT "order_user_uuid_fkey" FOREIGN KEY ("user_uuid") REFERENCES "user"("user_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ordered_goods" ADD CONSTRAINT "ordered_goods_order_uuid_fkey" FOREIGN KEY ("order_uuid") REFERENCES "order"("order_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "prepare_order" ADD CONSTRAINT "prepare_order_user_uuid_fkey" FOREIGN KEY ("user_uuid") REFERENCES "user"("user_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "prepare_ordered_goods" ADD CONSTRAINT "prepare_ordered_goods_order_uuid_fkey" FOREIGN KEY ("order_uuid") REFERENCES "prepare_order"("order_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "sns_oauth" ADD CONSTRAINT "sns_oauth_sns_platform_id_fkey" FOREIGN KEY ("sns_platform_id") REFERENCES "sns_platform"("sns_platform_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "sns_oauth" ADD CONSTRAINT "sns_oauth_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "user"("user_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "user_synonym" ADD CONSTRAINT "user_synonym_user_uuid_fkey" FOREIGN KEY ("user_uuid") REFERENCES "user"("user_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;
