-- CreateTable
CREATE TABLE "bank_account" (
    "bank_account_uuid" UUID NOT NULL,
    "user_uuid" UUID NOT NULL,
    "bank_code" TEXT NOT NULL,
    "bank_name" TEXT NOT NULL,
    "account_number" TEXT NOT NULL,
    "account_holder" TEXT NOT NULL,

    CONSTRAINT "bank_account_pkey" PRIMARY KEY ("bank_account_uuid")
);

-- CreateTable
CREATE TABLE "kakao_pay" (
    "user_uuid" UUID NOT NULL,
    "kakao_uid" TEXT NOT NULL,

    CONSTRAINT "kakao_pay_pkey" PRIMARY KEY ("user_uuid")
);

-- CreateIndex
CREATE UNIQUE INDEX "bank_account_user_uuid_bank_code_account_number_key" ON "bank_account"("user_uuid", "bank_code", "account_number");

-- AddForeignKey
ALTER TABLE "bank_account" ADD CONSTRAINT "bank_account_user_uuid_fkey" FOREIGN KEY ("user_uuid") REFERENCES "user"("user_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "kakao_pay" ADD CONSTRAINT "kakao_pay_user_uuid_fkey" FOREIGN KEY ("user_uuid") REFERENCES "user"("user_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;
