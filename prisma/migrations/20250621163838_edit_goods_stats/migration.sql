-- AlterTable
ALTER TABLE "goods_stats" RENAME COLUMN "goods_stat_date" TO "updated_at";
ALTER TABLE "goods_stats" RENAME COLUMN "goods_total_buy" TO "sales_count";
ALTER TABLE "goods_stats" RENAME COLUMN "goods_total_view" TO "view_count";
ALTER TABLE "goods_stats" ADD COLUMN    "netProfit" BIGINT NOT NULL DEFAULT 0;
ALTER TABLE "goods_stats" ADD COLUMN    "revenue" BIGINT NOT NULL DEFAULT 0;
ALTER TABLE "goods_stats" ALTER COLUMN  "updated_at" SET DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE "goods_stats" ALTER COLUMN  "sales_count" SET DEFAULT 0;
ALTER TABLE "goods_stats" ALTER COLUMN  "view_count" SET DEFAULT 0;
