/*
  Warnings:

  - Added the required column `ratio` to the `ordered_goods` table without a default value. This is not possible if the table is not empty.
  - Added the required column `seller_uuid` to the `ordered_goods` table without a default value. This is not possible if the table is not empty.
  - Added the required column `ratio` to the `prepare_ordered_goods` table without a default value. This is not possible if the table is not empty.
  - Added the required column `seller_uuid` to the `prepare_ordered_goods` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "ordered_goods" ADD COLUMN     "ratio" INTEGER NOT NULL,
ADD COLUMN     "seller_uuid" UUID NOT NULL;

-- AlterTable
ALTER TABLE "prepare_ordered_goods" ADD COLUMN     "ratio" INTEGER NOT NULL,
ADD COLUMN     "seller_uuid" UUID NOT NULL;

-- CreateTable
CREATE TABLE "sales_order" (
    "order_uuid" UUID NOT NULL,
    "user_uuid" UUID NOT NULL,
    "order_title" TEXT NOT NULL,
    "total_goods_price" INTEGER NOT NULL,
    "total_vat_price" INTEGER NOT NULL,
    "total_delivery_price" INTEGER,
    "pay_method" "pay_method_enum" NOT NULL,
    "order_status" INTEGER NOT NULL,
    "refunded_total_goods_price" INTEGER,
    "refunded_total_vat_price" INTEGER,
    "refunded_total_delivery_price" INTEGER,
    "card_type" VARCHAR(16),
    "card_number" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6),

    CONSTRAINT "sales_order_pkey" PRIMARY KEY ("order_uuid","user_uuid")
);

-- CreateTable
CREATE TABLE "prepare_sales_order" (
    "order_uuid" UUID NOT NULL,
    "user_uuid" UUID NOT NULL,
    "order_title" TEXT NOT NULL,
    "total_goods_price" INTEGER NOT NULL,
    "total_vat_price" INTEGER NOT NULL,
    "total_delivery_price" INTEGER,
    "pay_method" "pay_method_enum" NOT NULL,
    "order_status" INTEGER NOT NULL,
    "refunded_total_goods_price" INTEGER,
    "refunded_total_vat_price" INTEGER,
    "refunded_total_delivery_price" INTEGER,
    "card_type" VARCHAR(16),
    "card_number" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(6),

    CONSTRAINT "prepare_sales_order_pkey" PRIMARY KEY ("order_uuid","user_uuid")
);

-- AddForeignKey
ALTER TABLE "ordered_goods" ADD CONSTRAINT "ordered_goods_order_uuid_seller_uuid_fkey" FOREIGN KEY ("order_uuid", "seller_uuid") REFERENCES "sales_order"("order_uuid", "user_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "sales_order" ADD CONSTRAINT "sales_order_user_uuid_fkey" FOREIGN KEY ("user_uuid") REFERENCES "user"("user_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "prepare_ordered_goods" ADD CONSTRAINT "prepare_ordered_goods_order_uuid_seller_uuid_fkey" FOREIGN KEY ("order_uuid", "seller_uuid") REFERENCES "prepare_sales_order"("order_uuid", "user_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "prepare_sales_order" ADD CONSTRAINT "prepare_sales_order_user_uuid_fkey" FOREIGN KEY ("user_uuid") REFERENCES "user"("user_uuid") ON DELETE NO ACTION ON UPDATE NO ACTION;
