-- CreateEnum
CREATE TYPE "payment_status_enum" AS ENUM ('order_complete', 'order_cancel_request', 'exchange_request', 'order_cancel', 'partial_order_cancel', 'purchase_confirmed');

-- CreateEnum
CREATE TYPE "refund_reason_enum" AS ENUM ('change_color_size', 'order_mistakes', 'damage_and_defects', 'misdeliveries_and_delays');

-- CreateEnum
CREATE TYPE "refund_status_enum" AS ENUM ('request', 'complete', 'rejected');

-- CreateEnum
CREATE TYPE "delivery_status_enum" AS ENUM ('preparing', 'requested', 'in_transit', 'on_the_way', 'delivered');

-- CreateEnum
CREATE TYPE "delivery_type_enum" AS ENUM ('default', 'refund', 'exchange');

-- AlterTable
ALTER TABLE "order" ALTER COLUMN "order_status" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "ordered_goods" ALTER COLUMN "goods_order_status" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "prepare_order" ALTER COLUMN "order_status" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "prepare_ordered_goods" ALTER COLUMN "goods_order_status" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "prepare_sales_order" ALTER COLUMN "order_status" SET DATA TYPE TEXT;

-- AlterTable
ALTER TABLE "sales_order" ALTER COLUMN "order_status" SET DATA TYPE TEXT;
