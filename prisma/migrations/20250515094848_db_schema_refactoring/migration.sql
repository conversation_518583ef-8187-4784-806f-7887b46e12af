-- DropFore<PERSON>Key
ALTER TABLE "event_map" DROP CONSTRAINT "event_map_booth_uuid_fkey";

-- DropForeignKey
ALTER TABLE "event_map" DROP CONSTRAINT "event_map_event_uuid_fkey";

-- AlterTable
ALTER TABLE "event" RENAME COLUMN "event_date" TO "dates";
ALTER TABLE "event" RENAME COLUMN "event_img" TO "image";
ALTER TABLE "event" RENAME COLUMN "event_landing_url" TO "landing_url";
ALTER TABLE "event" RENAME COLUMN "event_location" TO "address";
ALTER TABLE "event" RENAME COLUMN "event_name" TO "name";
ALTER TABLE "event" RENAME COLUMN "event_ticket_url" TO "ticket_url";
ALTER TABLE "event" ADD COLUMN    "map_image_url" TEXT;

-- AlterTable
ALTER TABLE "location" RENAME COLUMN "location_detailed_address" TO "detailed_address";
ALTER TABLE "location" RENAME COLUMN "location_name" TO "name";
ALTER TABLE "location" RENAME COLUMN "location_postal_code" TO "postal_code";
ALTER TABLE "location" RENAME COLUMN "location_road_address" TO "road_address";

-- DropTable
DROP TABLE "event_map";

-- CreateTable
CREATE TABLE "booth_source_pivot" (
    "booth_uuid" UUID NOT NULL,
    "source_uuid" UUID NOT NULL,
    "booth_location_xy" JSONB[] DEFAULT ARRAY[]::JSONB[],

    CONSTRAINT "booth_source_pivot_pkey" PRIMARY KEY ("booth_uuid","source_uuid")
);

-- AddForeignKey
ALTER TABLE "booth_source_pivot" ADD CONSTRAINT "booth_source_pivot_booth_uuid_fkey" FOREIGN KEY ("booth_uuid") REFERENCES "booth"("booth_uuid") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "booth_source_pivot" ADD CONSTRAINT "booth_source_pivot_source_uuid_fkey" FOREIGN KEY ("source_uuid") REFERENCES "source"("source_uuid") ON DELETE CASCADE ON UPDATE CASCADE;
