-- CreateTable
CREATE TABLE "goods_set" (
    "goods_set_uuid" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "price" INTEGER NOT NULL,

    CONSTRAINT "goods_set_pkey" PRIMARY KEY ("goods_set_uuid")
);

-- CreateTable
CREATE TABLE "goods_set_pivot" (
    "goods_set_uuid" UUID NOT NULL,
    "goods_uuid" UUID NOT NULL,
    "amount" INTEGER NOT NULL,

    CONSTRAINT "goods_set_pivot_pkey" PRIMARY KEY ("goods_set_uuid","goods_uuid")
);

-- AddForeignKey
ALTER TABLE "goods_set_pivot" ADD CONSTRAINT "goods_set_pivot_goods_set_uuid_fkey" FOREIGN KEY ("goods_set_uuid") REFERENCES "goods_set"("goods_set_uuid") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "goods_set_pivot" ADD CONSTRAINT "goods_set_pivot_goods_uuid_fkey" FOREIGN KEY ("goods_uuid") REFERENCES "goods"("goods_uuid") ON DELETE CASCADE ON UPDATE NO ACTION;
