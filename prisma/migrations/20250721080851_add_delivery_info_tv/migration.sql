-- AlterTable
ALTER TABLE "ordered_goods" ADD COLUMN     "delivery_info_uuid" UUID;

-- AlterTable
ALTER TABLE "prepare_ordered_goods" ADD COLUMN     "delivery_info_uuid" UUID;

-- CreateTable
CREATE TABLE "delivery_info" (
    "delivery_info_uuid" UUID NOT NULL,
    "order_uuid" UUID NOT NULL,
    "recipient_name" TEXT NOT NULL,
    "recipient_phone" TEXT NOT NULL,
    "recipient_address" TEXT NOT NULL,
    "postal_code" TEXT NOT NULL,
    "comment" TEXT,
    "tracking_number" TEXT,
    "delivery_company_name" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "delivery_info_pkey" PRIMARY KEY ("delivery_info_uuid")
);

-- CreateTable
CREATE TABLE "prepare_delivery_info" (
    "delivery_info_uuid" UUID NOT NULL,
    "order_uuid" UUID NOT NULL,
    "recipient_name" TEXT NOT NULL,
    "recipient_phone" TEXT NOT NULL,
    "recipient_address" TEXT NOT NULL,
    "postal_code" TEXT NOT NULL,
    "comment" TEXT,
    "tracking_number" TEXT,
    "delivery_company_name" TEXT,
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "prepare_delivery_info_pkey" PRIMARY KEY ("delivery_info_uuid")
);

-- AddForeignKey
ALTER TABLE "delivery_info" ADD CONSTRAINT "delivery_info_order_uuid_fkey" FOREIGN KEY ("order_uuid") REFERENCES "order"("order_uuid") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "ordered_goods" ADD CONSTRAINT "ordered_goods_delivery_info_uuid_fkey" FOREIGN KEY ("delivery_info_uuid") REFERENCES "delivery_info"("delivery_info_uuid") ON DELETE SET NULL ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "prepare_delivery_info" ADD CONSTRAINT "prepare_delivery_info_order_uuid_fkey" FOREIGN KEY ("order_uuid") REFERENCES "prepare_order"("order_uuid") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "prepare_ordered_goods" ADD CONSTRAINT "prepare_ordered_goods_delivery_info_uuid_fkey" FOREIGN KEY ("delivery_info_uuid") REFERENCES "prepare_delivery_info"("delivery_info_uuid") ON DELETE SET NULL ON UPDATE CASCADE;
