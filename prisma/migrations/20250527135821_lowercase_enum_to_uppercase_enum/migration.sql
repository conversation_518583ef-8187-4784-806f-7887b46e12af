-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "goods_currency_enum" ADD VALUE 'eur';
ALTER TYPE "goods_currency_enum" ADD VALUE 'gbp';
ALTER TYPE "goods_currency_enum" ADD VALUE 'cny';
ALTER TYPE "goods_currency_enum" ADD VALUE 'aud';
ALTER TYPE "goods_currency_enum" ADD VALUE 'cad';
ALTER TYPE "goods_currency_enum" ADD VALUE 'inr';
ALTER TYPE "goods_currency_enum" ADD VALUE 'brl';
ALTER TYPE "goods_currency_enum" ADD VALUE 'zar';
