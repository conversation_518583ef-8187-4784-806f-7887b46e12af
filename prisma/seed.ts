import { PrismaClient } from '@prisma/client';
import { ONLINE_EVENT_AND_LOCATION_UUID } from '../src/common/lib/const';

const prisma = new PrismaClient();

const defaultUuid = '00000000-0000-0000-0000-000000000000';
let sourceUuid: string;
const defaultImage = 'https://static.sherry.gg/dodo-test-buck/miku.webp';

async function staticSeed() {
  console.info('Create static (0 = terms, 1 = privacy)');
  const staticData = await prisma.static.findFirst({});
  if (staticData) {
    console.info('Already exists static');
    return;
  }
  await prisma.static.createMany({
    data: [
      {
        // 서비스 이용약관
        staticId: 0,
        content: 'https://static.sherry.gg/dodo-test-buck/service.pdf',
      },
      {
        // 개인정보
        staticId: 1,
        content: 'https://static.sherry.gg/dodo-test-buck/pri.pdf',
      },
    ],
  });
  console.info('Created static');
}

async function eventSeed() {
  console.info('Create Event');
  const existEvent = await prisma.event.findUnique({
    where: { eventUuid: ONLINE_EVENT_AND_LOCATION_UUID },
  });
  if (existEvent) {
    console.info('Already exists event');
    return;
  }
  const now = new Date();
  const endDate = now;
  endDate.setDate(endDate.getDate() + 1);
  await prisma.event.create({
    data: {
      eventUuid: ONLINE_EVENT_AND_LOCATION_UUID,
      location: {
        create: {
          locationUuid: ONLINE_EVENT_AND_LOCATION_UUID,
          name: 'Sherry',
          roadAddress: 'https://sherry.gg',
          detailedAddress: 'https://event.sherry.gg',
          postalCode: '41256',
        },
      },
      name: 'Sherry',
      dates: [now, endDate],
      image:
        'https://pbs.twimg.com/media/GYeXkmiaMAELbS4?format=jpg&name=4096x4096',
      landingUrl: 'https://sherry.gg',
      ticketUrl: 'https://event.sherry.gg',
      createdAt: new Date(),
    },
  });
  console.info('Created Event');
}

async function characterAndSourceSeed() {
  console.info('Create character');
  const character = await prisma.character.findFirst({});

  if (character) {
    console.info('Already exists character');
    sourceUuid = character.sourceUuid;
    return;
  }

  const sourceAndCharacters = [
    {
      data: {
        name: '보컬로이드',
        image: defaultImage,
        character: {
          createMany: {
            data: [
              {
                name: '하츠네 미쿠',
                image: defaultImage,
              },
              { name: '카가미네 린', image: defaultImage },
              { name: '카가미네 렌', image: defaultImage },
              { name: '메구리네 루카', image: defaultImage },
              { name: '구미', image: defaultImage },
              { name: '카이토', image: defaultImage },
              { name: '메이', image: defaultImage },
            ],
          },
        },
      },
    },
    {
      data: {
        name: '블루아카이브',
        image: defaultUuid,
        character: {
          createMany: {
            data: [
              { name: '괴즈나', image: defaultUuid },
              { name: '미도리', image: defaultUuid },
            ],
          },
        },
      },
    },
    {
      data: {
        name: '트릭컬',
        image: defaultUuid,
        character: {
          createMany: {
            data: [
              { name: '코미', image: defaultUuid },
              { name: '버터', image: defaultUuid },
            ],
          },
        },
      },
    },
    {
      data: {
        name: '동방프로젝트',
        image: '원전 이미지',
        character: {
          createMany: {
            data: [
              { name: '하쿠레이 레이무', image: '레이무 이미지' },
              { name: '키리사메 마리사', image: '마리사 이미지' },
            ],
          },
        },
      },
    },
    {
      data: {
        name: '아이마스',
        image: '원전 이미지',
        character: {
          createMany: {
            data: [
              { name: '아마미 하루카', image: '하루카 이미지' },
              { name: '키사라기 치하야', image: '치하야 이미지' },
            ],
          },
        },
      },
    },
  ];

  await Promise.all(
    sourceAndCharacters.map(prisma.source.create.bind(prisma.source)),
  );

  console.info('Created character');
}

async function categorySeed() {
  console.info('Create Category');
  const category = await prisma.goodsCategory.findFirst({});

  if (category) {
    console.info('Already exists Category');
    return;
  }

  await prisma.goodsCategory.createMany({
    data: [
      {
        goodsCategoryUuid: defaultUuid,
        name: '코렛트',
      },
      {
        name: '아크릴 스텐드',
      },
      {
        name: '피규어',
      },
      {
        name: '다키마쿠라',
      },
      {
        name: '포토카드',
      },
      {
        name: '우편',
      },
      {
        name: '족자봉',
      },
      {
        name: '스티커',
      },
    ],
  });

  console.info('Created Category');
}

async function userSeed() {
  console.info('Create Test Dodo');
  const user = await prisma.user.findFirst({});

  if (user) {
    console.info('Already exists Category');
    return;
  }
  const google = await prisma.snsPlatform.create({
    data: {
      name: 'google',
    },
  });

  await prisma.user.create({
    data: {
      userUuid: defaultUuid,
      handle: 'dodoIsGod',
      profileImg: 'dodo',
      nickname: 'dodo',
      explainText: 'dodo dodo',
      realName: '김도도',
      email: '<EMAIL>',
      snsOauth: {
        create: {
          snsUid: '102842307144022203523',
          snsPlatformId: google.snsPlatformId,
        },
      },
      createdAt: new Date(),
    },
  });

  console.info('Created Test Dodo');
}

async function testDataSet() {
  console.info('Create booth');

  for (let index = 0; index < 5; index++) {
    const booth = await prisma.booth.create({
      data: {
        eventUuid: ONLINE_EVENT_AND_LOCATION_UUID,
        name: `보카로는 무적이고 신이다 ${index}`,
        participatedDates: [new Date('2024-10-05'), new Date('2024-10-06')],
        locationTexts: ['A1', 'A2', 'A3'],
        bannerImageUrl:
          'https://static.sherry.gg/dodo-test-buck/boothinfo.webp',
        infoBlocks: [
          'https://static.sherry.gg/dodo-test-buck/boothinfo2.webp',
          `<div>
      <img src="https://static.sherry.gg/dodo-test-buck/boothinfo.webp" alt="Image description" width="100%" height="auto" />
      <h1>안녕하세요! 이곳은 H1 태그입니다.</h1>
      <p>이것은 테스트 문단입니다. <strong>이 문장은 굵게 표시됩니다.</strong> 그리고 <small>이 문장은 작은 크기로 표시됩니다.</small></p>
      
      <h2 style="color=blue">H2 태그로 작성된 제목입니다</h2>
      <p><em>이 문장은 이탤릭체로 표시됩니다.</em> 또한 <u>밑줄</u>이 포함된 텍스트도 있습니다.</p>
      
      <h3>H3 태그로 작성된 제목입니다</h3>
      <p><s>이 문장은 취소선이 그어져 있습니다.</s> 텍스트 스타일을 자유롭게 조합할 수 있습니다.</p>
      
      <h4>H4 태그로 작성된 제목입니다</h4>
      <p>이 문단은 다양한 HTML 태그가 적용된 예제입니다. 예를 들어, <strong><em>굵고 이탤릭체</em></strong>를 사용하거나, <u><small>밑줄과 작은 글씨</small></u>를 함께 사용할 수 있습니다.</p>
      
      <h5>H5 태그로 작성된 제목입니다</h5>
      <p>다음은 일반 텍스트입니다. 스크롤 테스트를 위해 내용을 더 추가합니다. HTML 텍스트가 제대로 렌더링되는지 확인해 보세요.</p>
      
      <h6>H6 태그로 작성된 제목입니다</h6>
      <p>마지막으로, 이 페이지는 다양한 텍스트 관련 태그를 사용하는 예제입니다. <strong>굵은 텍스트</strong>, <small>작은 텍스트</small>, <em>이탤릭체</em>, <u>밑줄</u>, <s>취소선</s> 등이 포함되어 있습니다.</p>
      
      <h1>H1 태그를 다시 사용하여 큰 제목을 추가합니다.</h1>
      <p>다양한 <strong>HTML</strong> 태그를 사용해 스크롤 테스트를 계속합니다. 여러 줄을 추가하여 페이지가 길어지고, 스크롤 테스트에 적합한 상태가 될 것입니다.</p>
      
      <h2>H2 태그</h2>
      <p>HTML을 통해 Flutter에서 어떻게 텍스트 스타일을 적용할 수 있는지 확인해보세요.</p>
      
      <h3>H3 태그</h3>
      <p>이 문단은 추가로 스크롤을 발생시키기 위한 내용입니다.</p>
      
      <h4>H4 태그</h4>
      <p>Flutter에서 HTML 위젯을 사용하여 여러 스타일의 텍스트를 렌더링하는 것을 테스트합니다.</p>
      
      <h5>H5 태그</h5>
      <p>이 페이지는 스크롤 테스트와 다양한 텍스트 스타일링을 확인하는 데 사용됩니다.</p>
      
      <h6>H6 태그</h6>
      <p>마지막 문단입니다. <strong>굵은 텍스트</strong>, <small>작은 텍스트</small>, <em>이탤릭체</em>, <u>밑줄</u>, <s>취소선</s> 등이 포함되어 있습니다.</p>
    </div>`,
        ],
        boothSourcePivot: {
          create: { source: { create: { name: '테스트 장르' } } },
        },
        boothParticipatedUser: {
          create: {
            isAdmin: true,
            userUuid: defaultUuid,
            createdAt: new Date(),
          },
        },
      },
    });

    const ranString = ['보카로 코론토!!', '보카로 스텐드!!'];

    await prisma.goods.create({
      data: {
        name: ranString[Math.floor(Math.random() * ranString.length)],
        goodsCategoryUuid: defaultUuid,
        isSumUp: true,
        goodsBoothPivot: {
          create: {
            boothUuid: booth.boothUuid,
            quantity: 100,
          },
        },
        price: 11235,
        quantity: 1000,
      },
    });
  }

  console.info('Created booth');
}

async function main() {
  console.info('Start seeding ...');

  // await te();
  await staticSeed();
  await eventSeed();
  // await characterAndSourceSeed();
  // await categorySeed();
  // await userSeed();
  // await testDataSet();

  console.info('Seeding finished.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
