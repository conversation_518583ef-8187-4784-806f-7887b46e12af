model Order {
  orderUuid          String        @id @default(uuid(7)) @map("order_uuid") @db.Uuid
  linkKey            String?       @map("link_key") /// 페이플 link 결제 key
  userUuid           String?       @map("user_uuid") @db.Uuid /// 유저 uuid 비회원인 경우 null
  sellerUuid         String?       @map("seller_uuid") @db.Uuid /// 판매자 uuid 온라인 판매의 경우 null
  orderTitle         String        @map("order_title") /// Pg 전송용 타이틀
  totalGoodsPrice    Int           @map("total_goods_price") /// 총 굿즈 가격
  totalVatPrice      Int           @map("total_vat_price") /// 총 부가세 가격
  totalDeliveryPrice Int?          @map("total_delivery_price") /// 총 배송비 가격 null 이면 배송상품이 없음
  payMethod          PayMethodEnum @map("pay_method") /// 결제 방법
  orderStatus        String        @map("order_status") /// 결제 상태

  /// 환불관련
  refundedTotalGoodsPrice    Int? @map("refunded_total_goods_price")
  refundedTotalVatPrice      Int? @map("refunded_total_vat_price")
  refundedTotalDeliveryPrice Int? @map("refunded_total_delivery_price")

  /// 결제 정보 관련
  cardType   String? @map("card_type") @db.VarChar(16)
  cardNumber String? @map("card_number")

  /// 시간관련
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime? @map("updated_at") @db.Timestamptz(6)

  user         User?          @relation(fields: [userUuid], references: [userUuid], onDelete: NoAction, onUpdate: NoAction)
  seller       User?          @relation(fields: [sellerUuid], references: [userUuid], onDelete: NoAction, onUpdate: NoAction, name: "order_seller")
  orderedGoods OrderedGoods[]
  salesOrder   SalesOrder[]
  deliveryInfo DeliveryInfo[]

  @@map("order")
}

model DeliveryInfo {
  deliveryInfoUuid    String   @id @default(uuid(7)) @map("delivery_info_uuid") @db.Uuid
  orderUuid           String   @map("order_uuid") @db.Uuid /// 주문 uuid
  recipientName       String   @map("recipient_name") /// 수령인 이름
  recipientPhone      String   @map("recipient_phone") /// 수령인 전화번호
  recipientAddress    String   @map("recipient_address") /// 수령인 주소
  postalCode          String   @map("postal_code") /// 배송지 우편번호
  comment             String?  @map("comment") /// 배송지 요청사항
  trackingNumber      String?  @map("tracking_number") /// 택배사 운송장 번호
  deliveryCompanyName String?  @map("delivery_company_name") /// 택배사 이름
  createdAt           DateTime @default(now()) @map("created_at") @db.Timestamptz(6)

  order        Order          @relation(fields: [orderUuid], references: [orderUuid], onDelete: Cascade, onUpdate: NoAction)
  orderedGoods OrderedGoods[]

  @@map("delivery_info")
}

model OrderedGoods {
  orderedGoodsUuid String  @id @default(uuid(7)) @map("ordered_goods_uuid") @db.Uuid
  orderUuid        String  @map("order_uuid") @db.Uuid /// 주문 uuid
  deliveryInfoUuid String? @map("delivery_info_uuid") @db.Uuid /// 배송 정보 uuid
  sellerUuid       String  @map("seller_uuid") @db.Uuid /// 판매자 uuid
  deliveryStatus   Int?    @map("delivery_status") /// 배송상태
  goodsOrderStatus String  @map("goods_order_status") /// 주문 상태
  goodsCount       Int     @map("goods_count") /// 주문 수량
  goodsUuid        String  @map("goods_uuid") @db.Uuid /// 굿즈 uuid
  goodsName        String  @map("goods_name") /// 굿즈 이름 (결제 당시 캐싱용)
  goodsPrice       Int     @map("goods_price") /// 굿즈 가격 (결제 당시 캐싱용)
  vatPrice         Int     @map("vat_price") /// 굿즈 부가세 (결제 당시 캐싱용)
  ratio            Int     @map("ratio") /// 배분 비율 0~100 ( 결제 당시 캐싱용 )
  boothUuid        String? @map("booth_uuid") @db.Uuid /// 어떤 부스에서 구매했는지 확인용
  boothName        String? @map("booth_name") /// 부스 이름 (결제 당시 캐싱용)

  /// 환불관련
  refundedGoodsPrice           Int?      @map("refunded_goods_price")
  refundedVatPrice             Int?      @map("refunded_vat_price")
  exchangeStatus               Int?      @map("exchange_status")
  exchangeRequestDate          DateTime? @map("exchange_request_date") @db.Timestamptz(6)
  exchangeReason               String?   @map("exchange_reason")
  exchangeConfirmationDate     DateTime? @map("exchange_confirmation_date") @db.Timestamptz(6)
  cancellationRequestDate      DateTime? @map("cancellation_request_date") @db.Timestamptz(6)
  cancellationReason           String?   @map("cancellation_reason")
  cancellationConfirmationDate DateTime? @map("cancellation_confirmation_date") @db.Timestamptz(6)

  /// 시간 관련
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime? @map("updated_at") @db.Timestamptz(6)

  order        Order         @relation(fields: [orderUuid], references: [orderUuid], onDelete: NoAction, onUpdate: NoAction)
  salesOrder   SalesOrder    @relation(fields: [orderUuid, sellerUuid], references: [orderUuid, userUuid], onDelete: NoAction, onUpdate: NoAction)
  deliveryInfo DeliveryInfo? @relation(fields: [deliveryInfoUuid], references: [deliveryInfoUuid], onDelete: SetNull, onUpdate: NoAction)

  @@map("ordered_goods")
}

model SalesOrder {
  orderUuid                  String        @map("order_uuid") @db.Uuid
  userUuid                   String        @map("user_uuid") @db.Uuid /// 유저 uuid 비회원인 경우 null
  orderTitle                 String        @map("order_title") /// Pg 전송용 타이틀
  totalGoodsPrice            Int           @map("total_goods_price") /// 총 굿즈 가격
  totalVatPrice              Int           @map("total_vat_price") /// 총 부가세 가격
  totalDeliveryPrice         Int?          @map("total_delivery_price") /// 총 배송비 가격 null 이면 배송상품이 없음
  payMethod                  PayMethodEnum @map("pay_method") /// 결제 방법
  orderStatus                String        @map("order_status") /// 결제 상태
  payout                     DateTime?     @map("payout") @db.Timestamptz(6) /// 정산일시 정산하지 않았다면 null
  /// 환불관련
  refundedTotalGoodsPrice    Int?          @map("refunded_total_goods_price")
  refundedTotalVatPrice      Int?          @map("refunded_total_vat_price")
  refundedTotalDeliveryPrice Int?          @map("refunded_total_delivery_price")

  /// 결제 정보 관련
  cardType   String? @map("card_type") @db.VarChar(16)
  cardNumber String? @map("card_number")

  /// 시간관련
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime? @map("updated_at") @db.Timestamptz(6)

  user         User           @relation(fields: [userUuid], references: [userUuid], onDelete: NoAction, onUpdate: NoAction)
  order        Order          @relation(fields: [orderUuid], references: [orderUuid], onDelete: NoAction, onUpdate: NoAction)
  orderedGoods OrderedGoods[]

  @@id([orderUuid, userUuid])
  @@map("sales_order")
}

model PrepareOrder {
  orderUuid          String        @id @default(uuid(7)) @map("order_uuid") @db.Uuid
  linkKey            String?       @map("link_key") /// 페이플 link 결제 key
  userUuid           String?       @map("user_uuid") @db.Uuid /// 유저 uuid 비회원인 경우 null
  orderTitle         String        @map("order_title") /// Pg 전송용 타이틀
  totalGoodsPrice    Int           @map("total_goods_price") /// 총 굿즈 가격
  totalVatPrice      Int           @map("total_vat_price") /// 총 부가세 가격
  totalDeliveryPrice Int?          @map("total_delivery_price") /// 총 배송비 가격 null 이면 배송상품이 없음
  payMethod          PayMethodEnum @map("pay_method") /// 결제 방법
  orderStatus        String        @map("order_status") /// 결제 상태
  createdAt          DateTime      @default(now()) @map("created_at") @db.Timestamptz(6)

  user                User?                 @relation(fields: [userUuid], references: [userUuid], onDelete: NoAction, onUpdate: NoAction)
  prepareOrderedGoods PrepareOrderedGoods[]
  PrepareDeliveryInfo PrepareDeliveryInfo[]

  @@map("prepare_order")
}

model PrepareDeliveryInfo {
  deliveryInfoUuid    String   @id @default(uuid(7)) @map("delivery_info_uuid") @db.Uuid
  orderUuid           String   @map("order_uuid") @db.Uuid /// 주문 uuid
  recipientName       String   @map("recipient_name") /// 수령인 이름
  recipientPhone      String   @map("recipient_phone") /// 수령인 전화번호
  recipientAddress    String   @map("recipient_address") /// 수령인 주소
  postalCode          String   @map("postal_code") /// 배송지 우편번호
  comment             String?  @map("comment") /// 배송지 요청사항
  trackingNumber      String?  @map("tracking_number") /// 택배사 운송장 번호
  deliveryCompanyName String?  @map("delivery_company_name") /// 택배사 이름
  createdAt           DateTime @default(now()) @map("created_at") @db.Timestamptz(6)

  order        PrepareOrder          @relation(fields: [orderUuid], references: [orderUuid], onDelete: Cascade, onUpdate: NoAction)
  orderedGoods PrepareOrderedGoods[]

  @@map("prepare_delivery_info")
}

model PrepareOrderedGoods {
  orderedGoodsUuid String  @id @default(uuid(7)) @map("ordered_goods_uuid") @db.Uuid
  orderUuid        String  @map("order_uuid") @db.Uuid /// 주문 uuid
  sellerUuid       String  @map("seller_uuid") @db.Uuid /// 판매자 uuid
  deliveryInfoUuid String? @map("delivery_info_uuid") @db.Uuid /// 배송 정보 uuid
  deliveryStatus   Int?    @map("delivery_status") /// 배송상태
  goodsOrderStatus String  @map("goods_order_status") /// 주문 상태
  goodsCount       Int     @map("goods_count") /// 주문 수량
  goodsUuid        String  @map("goods_uuid") @db.Uuid /// 굿즈 uuid
  goodsName        String  @map("goods_name") /// 굿즈 이름 (결제 당시 캐싱용)
  goodsPrice       Int     @map("goods_price") /// 굿즈 가격 (결제 당시 캐싱용)
  vatPrice         Int     @map("vat_price") /// 굿즈 부가세 (결제 당시 캐싱용)
  ratio            Int     @map("ratio") /// 배분 비율 0~100 ( 결제 당시 캐싱용 )
  boothUuid        String? @map("booth_uuid") @db.Uuid /// 어떤 부스에서 구매했는지 확인용
  boothName        String? @map("booth_name") /// 부스 이름 (결제 당시 캐싱용)

  /// 시간 관련
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime? @map("updated_at") @db.Timestamptz(6)

  order               PrepareOrder         @relation(fields: [orderUuid], references: [orderUuid], onDelete: NoAction, onUpdate: NoAction)
  prepareSalesOrder   PrepareSalesOrder?   @relation(fields: [orderUuid, sellerUuid], references: [orderUuid, userUuid], onDelete: NoAction, onUpdate: NoAction)
  PrepareDeliveryInfo PrepareDeliveryInfo? @relation(fields: [deliveryInfoUuid], references: [deliveryInfoUuid])

  @@map("prepare_ordered_goods")
}

model PrepareSalesOrder {
  orderUuid          String        @default(uuid(7)) @map("order_uuid") @db.Uuid
  userUuid           String        @map("user_uuid") @db.Uuid /// 판매자 uuid
  orderTitle         String        @map("order_title") /// Pg 전송용 타이틀
  totalGoodsPrice    Int           @map("total_goods_price") /// 총 굿즈 가격
  totalVatPrice      Int           @map("total_vat_price") /// 총 부가세 가격
  totalDeliveryPrice Int?          @map("total_delivery_price") /// 총 배송비 가격 null 이면 배송상품이 없음
  payMethod          PayMethodEnum @map("pay_method") /// 결제 방법
  orderStatus        String        @map("order_status") /// 결제 상태

  /// 환불관련
  refundedTotalGoodsPrice    Int? @map("refunded_total_goods_price")
  refundedTotalVatPrice      Int? @map("refunded_total_vat_price")
  refundedTotalDeliveryPrice Int? @map("refunded_total_delivery_price")

  /// 결제 정보 관련
  cardType   String? @map("card_type") @db.VarChar(16)
  cardNumber String? @map("card_number")

  /// 시간관련
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime? @map("updated_at") @db.Timestamptz(6)

  user         User                  @relation(fields: [userUuid], references: [userUuid], onDelete: NoAction, onUpdate: NoAction)
  orderedGoods PrepareOrderedGoods[]

  @@id([orderUuid, userUuid])
  @@map("prepare_sales_order")
}
