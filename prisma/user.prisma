/// sns 연동정보
model SnsOauth {
  userId        String      @map("user_id") @db.Uuid
  snsPlatformId Int         @map("sns_platform_id") @db.SmallInt
  handle        String?
  snsUid        String      @map("sns_uid")
  refreshToken  String?     @map("refresh_token") /// refresh token
  snsPlatform   SnsPlatform @relation(fields: [snsPlatformId], references: [snsPlatformId], onDelete: NoAction, onUpdate: NoAction)
  user          User        @relation(fields: [userId], references: [userUuid], onDelete: Cascade, onUpdate: Cascade)

  @@id([userId, snsPlatformId])
  @@map("sns_oauth")
}

/// sns 플랫폼
model SnsPlatform {
  snsPlatformId Int                 @id @default(autoincrement()) @map("sns_platform_id") @db.SmallInt
  name          SnsPlatformNameEnum
  snsOauth      SnsOauth[]

  @@map("sns_platform")
}

/// 유저
model User {
  userUuid    String   @id @default(uuid(7)) @map("user_uuid") @db.Uuid
  handle      String   @unique /// 쉐리 핸들
  profileImg  String?  @map("profile_img") /// 프로필 이미지 
  nickname    String /// 닉네임
  explainText String?  @map("explain_text") /// 자기 소개
  realName    String?  @map("real_name") /// 실명 
  email       String? /// 이메일 
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz(6)

  boothParticipatedUser BoothParticipatedUser[]
  goodsUser             GoodsUserRatio[]
  order                 Order[] /// 구매 내역
  snsOauth              SnsOauth[] /// sns 연동정보
  userSynonym           UserSynonym[] /// 동의어
  salesOrder            SalesOrder[] /// 판매 내역
  prepareOrder          PrepareOrder[] /// 사전 구매 내역
  prepareSalesOrder     PrepareSalesOrder[] /// 사전 판매 내역
  bankAccount           BankAccount[]
  kakaopayInfo          KakaopayInfo?
  ticket                Ticket[] /// 이용권 내역
  prepareTicket         PrepareTicket[]
  sellerOrder           Order[]                 @relation("order_seller") /// 판매 내역

  @@map("user")
}

/// 유저 동의어
model UserSynonym {
  userUuid String @map("user_uuid") @db.Uuid
  synonym  String /// 동의어
  user     User   @relation(fields: [userUuid], references: [userUuid], onDelete: NoAction, onUpdate: NoAction)

  @@unique([userUuid, synonym])
  @@map("user_synonym")
}

/// 은행 계좌
model BankAccount {
  bankAccountUuid String  @id @default(uuid(7)) @map("bank_account_uuid") @db.Uuid
  userUuid        String  @map("user_uuid") @db.Uuid
  bankCode        String  @map("bank_code") /// 은행 코드
  accountNumber   String  @map("account_number") /// 계좌 번호
  accountHolder   String  @map("account_holder") /// 예금주
  isSettlement    Boolean @default(false) @map("is_settlement") /// 정산 계좌 여부

  user User @relation(fields: [userUuid], references: [userUuid], onDelete: NoAction, onUpdate: NoAction)
  bank Bank @relation(fields: [bankCode], references: [bankCode], onDelete: NoAction, onUpdate: NoAction)

  @@unique([userUuid, bankCode, accountNumber])
  @@map("bank_account")
}

/// 카카오페이 정보
model KakaopayInfo {
  userUuid  String @id @map("user_uuid") @db.Uuid
  kakao_uid String @map("kakao_uid") /// 카카오 uid

  user User @relation(fields: [userUuid], references: [userUuid], onDelete: NoAction, onUpdate: NoAction)

  @@map("kakao_pay")
}

/// 은행 정보
model Bank {
  bankCode    String        @id @map("bank_code") /// 은행 코드
  name        String /// 은행 이름
  bankAccount BankAccount[]

  @@map("bank")
}
