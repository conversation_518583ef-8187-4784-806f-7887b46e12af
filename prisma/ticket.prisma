/// 이용권
model Ticket {
  ticketUuid String   @id @default(uuid(7)) @map("ticket_uuid") @db.Uuid /// 이용권 사용 내역 UUID
  userUuid   String   @map("user_uuid") @db.Uuid /// 유저의 UUID
  usedDay    Int      @default(0) @map("used_day") /// 사용된 일 수
  usableDay  Int      @map("usable_day") /// 이 이용권으로 사용 가능한 일 수
  expiredAt  DateTime @map("expired_at") /// 이용권 유통기한 5년

  user          User            @relation(fields: [userUuid], references: [userUuid], onDelete: NoAction, onUpdate: NoAction)
  ticketHistory TicketHistory[]

  @@map("ticket")
}

/// 이용권 사용 내역
model TicketHistory {
  ticketHistoryUuid String   @id @default(uuid(7)) @map("ticket_history_uuid") @db.Uuid /// 이용권 사용 내역 UUID
  ticketUuid        String   @map("ticket_uuid") @db.Uuid /// 이용권 UUID
  boothUuid         String   @map("booth_uuid") @db.Uuid /// 부스 UUID
  usedDate          DateTime @map("used_date") /// 적용된 날짜

  ticket Ticket @relation(fields: [ticketUuid], references: [ticketUuid], onDelete: NoAction, onUpdate: NoAction)
  booth  Booth  @relation(fields: [boothUuid], references: [boothUuid], onDelete: NoAction, onUpdate: NoAction)

  @@map("ticket_history")
}

/// 이용권 사전구매
model PrepareTicket {
  ticketUuid String     @id @default(uuid(7)) @map("ticket_uuid") @db.Uuid /// 이용권 사용 내역 UUID
  userUuid   String     @map("user_uuid") @db.Uuid /// 유저의 UUID
  usedDates  DateTime[] @map("used_dates") /// 사용하기로 지정한 기간
  usableDay  Int        @map("usable_day") /// 이 이용권으로 사용 가능한 일 수
  createdAt  DateTime   @default(now()) @map("created_at") /// 해당 이용권 구매일
  expiredAt  DateTime   @map("expired_at") /// 이용권 유통기한 5년

  user User @relation(fields: [userUuid], references: [userUuid], onDelete: NoAction, onUpdate: NoAction)

  @@map("prepare_ticket")
}
