model Booth {
  boothUuid         String        @id @default(uuid(7)) @map("booth_uuid") @db.Uuid
  eventUuid         String        @map("event_uuid") @db.Uuid /// 이벤트 uuid
  name              String /// 부스 이름
  participatedDates DateTime[]    @map("participated_dates") @db.Timestamptz /// 행사 참여 일시들
  locationTexts     String[]      @map("location_texts") /// 부스 위치 문자
  bannerImageUrl    String        @map("banner_image_url") /// 부스 현수막 이미지 url
  infoBlocks        String[]      @map("info_blocks") /// 부스 인포 블록 배열
  createdAt         DateTime      @default(now()) @map("created_at") @db.Timestamptz(6) /// 부스 생성 일시
  coordinates       Json[]        @default([]) @map("coordinates") /// 행사 지도상에 표시할 부스 위치를 표현하기 위해 필요한 좌표. 예시) [[1,2], [2.3]]
  color             String        @default("#797979") @map("color") /// node-vibrant 로 뽑은 부스 색상
  type              BoothTypeEnum @default(OFFLINE) @map("type") /// 부스 타입
  deletedAt         DateTime?     @map("deleted_at") @db.Timestamptz(6) /// 삭제일시

  event                 Event                   @relation(fields: [eventUuid], references: [eventUuid], onDelete: NoAction, onUpdate: NoAction)
  boothParticipatedUser BoothParticipatedUser[]
  goodsBoothPivot       GoodsBoothPivot[]
  boothSourcePivot      BoothSourcePivot[]
  ticketHistory         TicketHistory[]

  @@map("booth")
}

model BoothParticipatedUser {
  boothUuid String   @map("booth_uuid") @db.Uuid /// 부스 uuid
  userUuid  String   @map("user_uuid") @db.Uuid /// 유저 uuid
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  isAdmin   Boolean? @map("is_admin") /// 부스 관리자 여부 이후에 빠질수있으나 일단 냅둠

  booth Booth @relation(fields: [boothUuid], references: [boothUuid], onDelete: NoAction, onUpdate: NoAction)
  user  User  @relation(fields: [userUuid], references: [userUuid], onDelete: NoAction, onUpdate: NoAction)

  @@id([boothUuid, userUuid])
  @@map("booth_participated_user")
}

model BoothSourcePivot {
  boothUuid  String @map("booth_uuid") @db.Uuid /// 부스 uuid
  sourceUuid String @map("source_uuid") @db.Uuid /// 소스 uuid

  booth  Booth  @relation(fields: [boothUuid], references: [boothUuid], onDelete: Cascade, onUpdate: Cascade)
  source Source @relation(fields: [sourceUuid], references: [sourceUuid], onDelete: Cascade, onUpdate: Cascade)

  @@id([boothUuid, sourceUuid])
  @@map("booth_source_pivot")
}
