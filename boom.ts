import axios from 'axios';

async function fetchUrl(url: string): Promise<string> {
  try {
    const response = await axios.get(url);
    return response.data; // 응답 데이터 반환
  } catch (error) {
    return `Error: ${error}`;
  }
}

async function sendRequests(urls: string[]): Promise<void> {
  const requests = urls.map((url) => fetchUrl(url));
  try {
    const results = await Promise.all(requests);
    results.forEach((result, index) => {
      console.log(`Response from URL ${index + 1}:`, result);
    });
  } catch (error) {
    console.error('Error occurred:', error);
  }
}

// URL 목록 생성
const urls = Array.from({ length: 100009 }, () => `https://backend.sherry.gg/`);

// 요청 실행

async function main() {
  console.info(`Start seeding ...`);

  sendRequests(urls);

  console.info(`Seeding finished.`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {});
