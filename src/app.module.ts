import { AllExceptionsFilter } from '@common/filters/all.exception.filter';
import { HttpExceptionFilter } from '@common/filters/http.exception.filter';
import { UserJWKGuard } from '@common/guards/user-jwk.guard';
import { TransformInterceptor } from '@common/interceptors/transform.interceptor';
import { ValidationPipe } from '@common/pipes/validation.pipe';
import { AppConfigModule } from '@config/app/config.module';
import { AppConfigService } from '@config/app/config.service';
import { OauthConfigModule } from '@config/oauth/config.module';
import { StorageConfigModule } from '@config/storage/config.module';
import { JwkModule } from '@module/jwk/jwk.module';
import { OauthModule } from '@module/oauth/oauth.module';
import { UserModule } from '@module/user/user.module';
import { WebhookModule } from '@module/webhook/webhook.module';
import { Module } from '@nestjs/common';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { PrismaModule } from '@provider/database/prisma.module';
import { StorageModule } from '@provider/storage/storage.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { BoothModule } from './module/booth/booth.module';
import { EventModule } from './module/event/event.module';
import { FileModule } from './module/file/file.module';
import { GoodsModule } from './module/goods/goods.module';
import { OrderModule } from './module/order/order.module';
import { PaymentModule } from './module/payment/payment.module';
import { PaypleModule } from './module/payple/payple.module';
import { TicketModule } from './module/ticket/ticket.module';
import { RedisCacheModule } from '@provider/redis/redis.module';

@Module({
  imports: [
    AppConfigModule,
    OauthConfigModule,
    StorageConfigModule,
    OauthModule,
    ThrottlerModule.forRoot({
      throttlers: [
        {
          ttl: 60,
          limit: 10,
        },
      ],
    }),
    RedisCacheModule,
    JwkModule.forRootAsync(JwkModule, {
      imports: [AppConfigModule],
      inject: [AppConfigService],
      useFactory: (configService: AppConfigService) => ({
        configService: {
          env: configService.env,
          jwtIssuer: configService.jwtIssuer,
          jwtPublic: configService.jwtPublic,
          jwtPrivate: configService.jwtPrivate,
        },
      }),
    }),
    UserModule,
    // ThrottlerModule.forRootAsync({
    //   imports: [RedisConfigModule],
    //   inject: [RedisConfigService],
    //   useFactory: (redisConfig: RedisConfigService) => ({
    //     ttl: 60, // 각 요청이 스토리지에서 지속되는 시간 (초)
    //     limit: 80, // TTL 제한 내 최대 요청 수
    //     storage: new ThrottlerStorageRedisService({
    //       host: redisConfig.host,
    //       port: redisConfig.port,
    //       password: redisConfig.pwd,
    //     }),
    //   }),
    // }),
    PrismaModule,
    OauthModule,
    GoodsModule,
    BoothModule,
    PaypleModule,
    PaymentModule,
    WebhookModule,
    TicketModule,
    StorageModule,
    FileModule,
    EventModule,
    OrderModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    { provide: APP_INTERCEPTOR, useClass: TransformInterceptor },
    { provide: APP_FILTER, useClass: AllExceptionsFilter },
    { provide: APP_FILTER, useClass: HttpExceptionFilter },
    { provide: APP_PIPE, useClass: ValidationPipe },
    { provide: APP_GUARD, useClass: ThrottlerGuard },
    { provide: APP_GUARD, useClass: UserJWKGuard },
    // { provide: APP_GUARD, useClass: UserRolesGuard },
  ],
})
export class AppModule {}
