import { softDeleteMiddleware } from './prisma.middleware';

describe('Prisma Soft Delete Middleware', () => {
  let middleware: any;
  let mockNext: jest.Mock;

  beforeEach(() => {
    middleware = softDeleteMiddleware();
    mockNext = jest.fn().mockResolvedValue({ id: 'test-result' });
  });

  it('GoodsBoothPivot findUnique를 findFirst로 변환하고 복합 키를 처리해야 함', async () => {
    const params: any = {
      model: 'GoodsBoothPivot',
      action: 'findUnique',
      args: {
        where: {
          goodsUuid_boothUuid: {
            goodsUuid: 'goods-uuid-123',
            boothUuid: 'booth-uuid-456',
          },
        },
      },
    };

    await middleware(params, mockNext);

    expect(params.action).toBe('findFirst');
    expect(params.args.where).toEqual({
      goodsUuid: 'goods-uuid-123',
      boothUuid: 'booth-uuid-456',
      deletedAt: null,
    });
    expect(mockNext).toHaveBeenCalledWith(params);
  });

  describe('복합 키 변환 테스트', () => {
    it('GoodsBoothPivot findUnique를 올바르게 변환해야 함', async () => {
      const params: any = {
        model: 'GoodsBoothPivot',
        action: 'findUnique',
        args: {
          where: {
            goodsUuid_boothUuid: {
              goodsUuid: 'goods-uuid-123',
              boothUuid: 'booth-uuid-456',
            },
          },
        },
      };

      await middleware(params, mockNext);

      expect(params.action).toBe('findFirst');
      expect(params.args.where).toEqual({
        goodsUuid: 'goods-uuid-123',
        boothUuid: 'booth-uuid-456',
        deletedAt: null,
      });
      expect(mockNext).toHaveBeenCalledWith(params);
    });

    it('GoodsCharacterPivot은 soft delete 테이블이 아니므로 변환하지 않아야 함', async () => {
      const params: any = {
        model: 'GoodsCharacterPivot',
        action: 'findUnique',
        args: {
          where: {
            characterUuid_goodsUuid: {
              characterUuid: 'character-uuid-123',
              goodsUuid: 'goods-uuid-456',
            },
          },
        },
      };

      const originalParams = JSON.parse(JSON.stringify(params));
      await middleware(params, mockNext);

      // middleware가 처리하지 않으므로 원본과 동일해야 함
      expect(params).toEqual(originalParams);
      expect(params.action).toBe('findUnique'); // 변환되지 않음
      expect(params.args.where.characterUuid_goodsUuid).toBeDefined(); // 복합 키 그대로 유지
      expect(params.args.where.deletedAt).toBeUndefined(); // deletedAt 추가되지 않음
    });

    it('BoothParticipatedUser는 soft delete 테이블이 아니므로 변환하지 않아야 함', async () => {
      const params: any = {
        model: 'BoothParticipatedUser',
        action: 'findUnique',
        args: {
          where: {
            boothUuid_userUuid: {
              boothUuid: 'booth-uuid-123',
              userUuid: 'user-uuid-456',
            },
          },
        },
      };

      const originalParams = JSON.parse(JSON.stringify(params));
      await middleware(params, mockNext);

      // middleware가 처리하지 않으므로 원본과 동일해야 함
      expect(params).toEqual(originalParams);
      expect(params.action).toBe('findUnique'); // 변환되지 않음
      expect(params.args.where.boothUuid_userUuid).toBeDefined(); // 복합 키 그대로 유지
      expect(params.args.where.deletedAt).toBeUndefined(); // deletedAt 추가되지 않음
    });

    it('복합 키와 다른 조건이 함께 있을 때 올바르게 처리해야 함', async () => {
      const params: any = {
        model: 'GoodsBoothPivot',
        action: 'findUnique',
        args: {
          where: {
            goodsUuid_boothUuid: {
              goodsUuid: 'goods-uuid-123',
              boothUuid: 'booth-uuid-456',
            },
            quantity: { gt: 0 },
          },
        },
      };

      await middleware(params, mockNext);

      expect(params.action).toBe('findFirst');
      expect(params.args.where).toEqual({
        goodsUuid: 'goods-uuid-123',
        boothUuid: 'booth-uuid-456',
        quantity: { gt: 0 },
        deletedAt: null,
      });
    });
  });

  describe('soft delete 필터 테스트', () => {
    it('deletedAt이 undefined일 때 null로 설정해야 함', async () => {
      const params = {
        model: 'GoodsBoothPivot',
        action: 'findUnique',
        args: {
          where: {
            goodsUuid_boothUuid: {
              goodsUuid: 'goods-uuid-123',
              boothUuid: 'booth-uuid-456',
            },
          },
        },
      };

      await middleware(params, mockNext);

      // @ts-expect-error -- deletedAt 은 softDelete 를 위해 추가한 필드이기 때문에 타입에 없음
      expect(params.args.where.deletedAt).toBe(null);
    });

    it('deletedAt이 "all"일 때 OR 조건으로 변환해야 함', async () => {
      const params = {
        model: 'GoodsBoothPivot',
        action: 'findUnique',
        args: {
          where: {
            goodsUuid_boothUuid: {
              goodsUuid: 'goods-uuid-123',
              boothUuid: 'booth-uuid-456',
            },
            deletedAt: 'all',
          },
        },
      };

      await middleware(params, mockNext);

      expect(params.args.where).toEqual({
        goodsUuid: 'goods-uuid-123',
        boothUuid: 'booth-uuid-456',
        OR: [{ deletedAt: null }, { NOT: { deletedAt: null } }],
      });
      expect(params.args.where.deletedAt).toBeUndefined();
    });

    it('deletedAt이 명시적으로 설정된 경우 그대로 유지해야 함', async () => {
      const specificDate = new Date('2023-01-01');
      const params = {
        model: 'GoodsBoothPivot',
        action: 'findUnique',
        args: {
          where: {
            goodsUuid_boothUuid: {
              goodsUuid: 'goods-uuid-123',
              boothUuid: 'booth-uuid-456',
            },
            deletedAt: specificDate,
          },
        },
      };

      await middleware(params, mockNext);

      expect(params.args.where.deletedAt).toBe(specificDate);
    });
  });

  describe('soft delete 테이블이 아닌 경우 (추가 테스트)', () => {
    it('User 모델은 변환하지 않아야 함', async () => {
      const params = {
        model: 'User',
        action: 'findUnique',
        args: {
          where: {
            userUuid: 'user-uuid-123',
          },
        },
      };

      const originalParams = JSON.parse(JSON.stringify(params));
      await middleware(params, mockNext);

      expect(params).toEqual(originalParams);
      expect(mockNext).toHaveBeenCalledWith(params);
    });
  });

  describe('update/delete/upsert 작업에서 복합 키 처리', () => {
    it('update 작업에서 복합 키를 올바르게 변환해야 함', async () => {
      const params: any = {
        model: 'GoodsBoothPivot',
        action: 'update',
        args: {
          where: {
            goodsUuid_boothUuid: {
              goodsUuid: 'goods-uuid-123',
              boothUuid: 'booth-uuid-456',
            },
          },
          data: {
            quantity: 10,
          },
        },
      };

      await middleware(params, mockNext);

      expect(params.action).toBe('updateMany');
      expect(params.args.where).toEqual({
        goodsUuid: 'goods-uuid-123',
        boothUuid: 'booth-uuid-456',
        deletedAt: null,
      });
      expect(params.args.where.goodsUuid_boothUuid).toBeUndefined();
    });

    it('delete 작업에서 복합 키를 올바르게 변환해야 함', async () => {
      const params: any = {
        model: 'GoodsBoothPivot',
        action: 'delete',
        args: {
          where: {
            goodsUuid_boothUuid: {
              goodsUuid: 'goods-uuid-123',
              boothUuid: 'booth-uuid-456',
            },
          },
        },
      };

      await middleware(params, mockNext);

      expect(params.action).toBe('update');
      expect(params.args.where).toEqual({
        goodsUuid: 'goods-uuid-123',
        boothUuid: 'booth-uuid-456',
        deletedAt: null,
      });
      expect(params.args.data.deletedAt).toBeInstanceOf(Date);
      expect(params.args.where.goodsUuid_boothUuid).toBeUndefined();
    });

    it('upsert 작업에서 where 절은 변환하지 않고 create/update 데이터만 처리해야 함', async () => {
      const params: any = {
        model: 'GoodsBoothPivot',
        action: 'upsert',
        args: {
          where: {
            goodsUuid_boothUuid: {
              goodsUuid: 'goods-uuid-123',
              boothUuid: 'booth-uuid-456',
            },
          },
          create: {
            goodsUuid: 'goods-uuid-123',
            boothUuid: 'booth-uuid-456',
            quantity: 5,
          },
          update: {
            quantity: 10,
          },
        },
      };

      await middleware(params, mockNext);

      expect(params.action).toBe('upsert');
      // where 절은 복합 키 그대로 유지 (unique constraint이므로)
      expect(params.args.where).toEqual({
        goodsUuid_boothUuid: {
          goodsUuid: 'goods-uuid-123',
          boothUuid: 'booth-uuid-456',
        },
      });
      // create/update 데이터에만 deletedAt 추가
      expect(params.args.create.deletedAt).toBe(null);
      expect(params.args.update.deletedAt).toBe(null);
    });
  });

  describe('에러 케이스', () => {
    it('where 조건이 없는 경우 기본 deletedAt 필터를 추가해야 함', async () => {
      const params: any = {
        model: 'GoodsBoothPivot',
        action: 'findUnique',
        args: {},
      };

      await middleware(params, mockNext);

      expect(params.action).toBe('findFirst');
      expect(params.args.where).toEqual({
        deletedAt: null,
      });
    });

    it('args가 없는 경우 기본 args를 생성해야 함', async () => {
      const params: any = {
        model: 'GoodsBoothPivot',
        action: 'findUnique',
      };

      await middleware(params, mockNext);

      expect(params.action).toBe('findFirst');
      expect(params.args).toEqual({
        where: { deletedAt: null },
      });
    });
  });
});
