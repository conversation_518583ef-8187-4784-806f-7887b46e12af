import { Prisma } from '@prisma/client';

function isSoftDeleteTable(modelName?: string) {
  // TODO: 테이블 이름추가
  return modelName && ['Goods', 'GoodsBoothPivot', 'Booth'].includes(modelName);
}

function addDeletedAtFilter(where: any) {
  if (!where) {
    return { deletedAt: null };
  }

  if (where.deletedAt === 'all') {
    where['OR'] = [{ deletedAt: null }, { NOT: { deletedAt: null } }];
    where.deletedAt = undefined;
  } else if (where.deletedAt === undefined) {
    where['deletedAt'] = null;
  }

  return where;
}

/**
 * 복합 키 변환 규칙 정의 (soft delete 테이블만)
 * soft delete 테이블: Goods, GoodsBoothPivot, Booth
 */
const COMPOSITE_KEY_MAPPINGS: Record<string, Record<string, string[]>> = {
  GoodsBoothPivot: {
    goodsUuid_boothUuid: ['goodsUuid', 'boothUuid'],
  },
  // 필요시 Goods, Booth 모델의 복합 키 매핑 추가
};

/**
 * 단일 복합 키를 개별 필드로 변환
 */
function transformSingleCompositeKey(
  transformedWhere: any,
  compositeKeyName: string,
  compositeKeyValue: any,
  fieldNames: string[],
): void {
  if (compositeKeyValue && typeof compositeKeyValue === 'object') {
    fieldNames.forEach((fieldName) => {
      if (compositeKeyValue[fieldName]) {
        transformedWhere[fieldName] = compositeKeyValue[fieldName];
      }
    });
    delete transformedWhere[compositeKeyName];
  }
}

/**
 * 복합 키를 개별 필드로 변환하는 함수 (재귀적으로 중첩 구조 처리)
 * 예: goodsUuid_boothUuid: { goodsUuid: "a", boothUuid: "b" }
 * -> { goodsUuid: "a", boothUuid: "b" }
 */
function transformCompositeKeys(where: any, modelName?: string): any {
  if (!where || typeof where !== 'object' || !modelName) {
    return where;
  }

  const transformedWhere = { ...where };
  const modelMappings = COMPOSITE_KEY_MAPPINGS[modelName];

  if (modelMappings) {
    Object.entries(modelMappings).forEach(([compositeKeyName, fieldNames]) => {
      if (where[compositeKeyName]) {
        transformSingleCompositeKey(
          transformedWhere,
          compositeKeyName,
          where[compositeKeyName],
          fieldNames,
        );
      }
    });
  }

  return transformedWhere;
}

/**
 * args 객체 전체에서 중첩된 where 절을 재귀적으로 찾아서 복합 키 변환 적용
 */
function transformNestedCompositeKeys(args: any, modelName?: string): any {
  if (!args || typeof args !== 'object' || !modelName) {
    return args;
  }

  const transformedArgs = { ...args };

  // 최상위 where 절 처리
  if (transformedArgs.where) {
    transformedArgs.where = transformCompositeKeys(
      transformedArgs.where,
      modelName,
    );
  }

  // include 내부의 where 절 처리
  if (transformedArgs.include) {
    transformedArgs.include = transformIncludeWhere(transformedArgs.include);
  }

  // select 내부의 where 절 처리
  if (transformedArgs.select) {
    transformedArgs.select = transformSelectWhere(transformedArgs.select);
  }

  return transformedArgs;
}

/**
 * include 객체 내부의 where 절을 재귀적으로 처리
 */
function transformIncludeWhere(include: any): any {
  if (!include || typeof include !== 'object') {
    return include;
  }

  const transformedInclude = { ...include };

  Object.keys(transformedInclude).forEach((relationName) => {
    const relation = transformedInclude[relationName];
    if (relation && typeof relation === 'object') {
      // 관계에 where 절이 있는 경우 복합 키 변환 적용
      if (relation.where) {
        // 관계 이름을 기반으로 모델명 추정 (예: goodsBoothPivot -> GoodsBoothPivot)
        const modelName = relationNameToModelName(relationName);
        if (
          modelName &&
          isSoftDeleteTable(modelName) &&
          COMPOSITE_KEY_MAPPINGS[modelName]
        ) {
          relation.where = transformCompositeKeys(relation.where, modelName);
        }
      }

      // 중첩된 include/select 처리
      if (relation.include) {
        relation.include = transformIncludeWhere(relation.include);
      }
      if (relation.select) {
        relation.select = transformSelectWhere(relation.select);
      }
    }
  });

  return transformedInclude;
}

/**
 * select 객체 내부의 where 절을 재귀적으로 처리
 */
function transformSelectWhere(select: any): any {
  if (!select || typeof select !== 'object') {
    return select;
  }

  const transformedSelect = { ...select };

  Object.keys(transformedSelect).forEach((relationName) => {
    const relation = transformedSelect[relationName];
    if (relation && typeof relation === 'object') {
      // 관계에 where 절이 있는 경우 복합 키 변환 적용
      if (relation.where) {
        const modelName = relationNameToModelName(relationName);
        if (
          modelName &&
          isSoftDeleteTable(modelName) &&
          COMPOSITE_KEY_MAPPINGS[modelName]
        ) {
          relation.where = transformCompositeKeys(relation.where, modelName);
        }
      }

      // 중첩된 include/select 처리
      if (relation.include) {
        relation.include = transformIncludeWhere(relation.include);
      }
      if (relation.select) {
        relation.select = transformSelectWhere(relation.select);
      }
    }
  });

  return transformedSelect;
}

/**
 * 관계 이름을 모델명으로 변환
 * 예: goodsBoothPivot -> GoodsBoothPivot
 */
function relationNameToModelName(relationName: string): string | null {
  // 일반적인 Prisma 관계 이름 패턴을 모델명으로 변환
  const modelNameMap: Record<string, string> = {
    goodsBoothPivot: 'GoodsBoothPivot',
    goodsCharacterPivot: 'GoodsCharacterPivot',
    goodsSourcePivot: 'GoodsSourcePivot',
    boothParticipatedUser: 'BoothParticipatedUser',
    goodsSetPivot: 'GoodsSetPivot',
    boothSourcePivot: 'BoothSourcePivot',
  };

  return modelNameMap[relationName] || null;
}

function handleFindOperations(params: any) {
  // 중첩된 구조를 포함하여 복합 키 변환 먼저 수행
  if (params.args) {
    params.args = transformNestedCompositeKeys(params.args, params.model);
  }

  if (!params.args || !params.args.where) {
    params['args'] = { where: { deletedAt: null } };
  } else {
    params.args.where = addDeletedAtFilter(params.args.where);
  }
}

function handleFindUniqueToFirst(params: any) {
  // findUnique를 findFirst로 변환하면서 복합 키 처리
  params.action = 'findFirst';

  if (!params.args || !params.args.where) {
    params['args'] = { where: { deletedAt: null } };
  } else {
    // 중첩된 구조를 포함하여 복합 키 변환 먼저 수행
    params.args = transformNestedCompositeKeys(params.args, params.model);
    // 그 다음 soft delete 필터 적용
    params.args.where = addDeletedAtFilter(params.args.where);
  }
}

function handleUpdateOperations(params: any) {
  // 중첩된 구조를 포함하여 복합 키 변환 먼저 수행
  if (params.args) {
    params.args = transformNestedCompositeKeys(params.args, params.model);
  }

  if (!params.args.where || params.args.where.deletedAt === undefined) {
    if (params.action === 'update') {
      params.action = 'updateMany';
    }
    if (!params.args.where) {
      params.args['where'] = { deletedAt: null };
    } else {
      params.args.where['deletedAt'] = null;
    }
  }
}

function handleDeleteOperations(params: any) {
  // 중첩된 구조를 포함하여 복합 키 변환 먼저 수행
  if (params.args) {
    params.args = transformNestedCompositeKeys(params.args, params.model);
  }

  if (params.action === 'delete') {
    params.action = 'update';
  } else if (params.action === 'deleteMany') {
    params.action = 'updateMany';
  }

  if (!params.args.where) {
    params.args['where'] = { deletedAt: null };
  } else if (params.args.where.deletedAt === undefined) {
    params.args.where['deletedAt'] = null;
  }

  params.args.data = { deletedAt: new Date(), ...params.args.data };
}

function handleUpsertOperations(params: any) {
  // upsert에서는 where 절의 복합 키를 변환하지 않음
  // 복합 키는 unique constraint이므로 Prisma가 직접 처리해야 함

  // upsert의 create 데이터에 deletedAt: null 설정
  if (params.args?.create && params.args.create.deletedAt === undefined) {
    params.args.create.deletedAt = null;
  }

  // upsert의 update 데이터에 deletedAt: null 설정 (명시적으로 설정되지 않은 경우)
  if (params.args?.update && params.args.update.deletedAt === undefined) {
    params.args.update.deletedAt = null;
  }
}

export function softDeleteMiddleware(): Prisma.Middleware<any> {
  return async (params, next) => {
    if (isSoftDeleteTable(params.model)) {
      switch (params.action) {
        case 'findUnique':
          // findUnique를 findFirst로 변환하면서 복합 키 처리
          handleFindUniqueToFirst(params);
          break;
        case 'findMany':
        case 'findFirst':
          handleFindOperations(params);
          break;
        case 'update':
        case 'updateMany':
          handleUpdateOperations(params);
          break;
        case 'delete':
        case 'deleteMany':
          handleDeleteOperations(params);
          break;
        case 'upsert':
          handleUpsertOperations(params);
          break;
      }
    }

    return next(params);
  };
}
