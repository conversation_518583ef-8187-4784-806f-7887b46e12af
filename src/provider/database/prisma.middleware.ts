import { Prisma } from '@prisma/client';

function isSoftDeleteTable(modelName?: string) {
  // TODO: 테이블 이름추가
  return modelName && ['Goods', 'GoodsBoothPivot', 'Booth'].includes(modelName);
}

function addDeletedAtFilter(where: any) {
  if (!where) {
    return { deletedAt: null };
  }

  if (where.deletedAt === 'all') {
    where['OR'] = [{ deletedAt: null }, { NOT: { deletedAt: null } }];
    where.deletedAt = undefined;
  } else if (where.deletedAt === undefined) {
    where['deletedAt'] = null;
  }

  return where;
}

/**
 * 복합 키 변환 규칙 정의
 */
const COMPOSITE_KEY_MAPPINGS: Record<string, Record<string, string[]>> = {
  GoodsBoothPivot: {
    goodsUuid_boothUuid: ['goodsUuid', 'boothUuid'],
  },
  GoodsCharacterPivot: {
    characterUuid_goodsUuid: ['characterUuid', 'goodsUuid'],
  },
  GoodsSourcePivot: {
    originUuid_goodsUuid: ['originUuid', 'goodsUuid'],
  },
  BoothParticipatedUser: {
    boothUuid_userUuid: ['boothUuid', 'userUuid'],
  },
  GoodsSetPivot: {
    goodsSetUuid_goodsUuid: ['goodsSetUuid', 'goodsUuid'],
  },
  BoothSourcePivot: {
    boothUuid_sourceUuid: ['boothUuid', 'sourceUuid'],
  },
};

/**
 * 단일 복합 키를 개별 필드로 변환
 */
function transformSingleCompositeKey(
  transformedWhere: any,
  compositeKeyName: string,
  compositeKeyValue: any,
  fieldNames: string[],
): void {
  if (compositeKeyValue && typeof compositeKeyValue === 'object') {
    fieldNames.forEach((fieldName) => {
      if (compositeKeyValue[fieldName]) {
        transformedWhere[fieldName] = compositeKeyValue[fieldName];
      }
    });
    delete transformedWhere[compositeKeyName];
  }
}

/**
 * 복합 키를 개별 필드로 변환하는 함수
 * 예: goodsUuid_boothUuid: { goodsUuid: "a", boothUuid: "b" }
 * -> { goodsUuid: "a", boothUuid: "b" }
 */
function transformCompositeKeys(where: any, modelName?: string): any {
  if (!where || typeof where !== 'object' || !modelName) {
    return where;
  }

  const transformedWhere = { ...where };
  const modelMappings = COMPOSITE_KEY_MAPPINGS[modelName];

  if (modelMappings) {
    Object.entries(modelMappings).forEach(([compositeKeyName, fieldNames]) => {
      if (where[compositeKeyName]) {
        transformSingleCompositeKey(
          transformedWhere,
          compositeKeyName,
          where[compositeKeyName],
          fieldNames,
        );
      }
    });
  }

  return transformedWhere;
}

function handleFindOperations(params: any) {
  if (!params.args || !params.args.where) {
    params['args'] = { where: { deletedAt: null } };
  } else {
    params.args.where = addDeletedAtFilter(params.args.where);
  }
}

function handleFindUniqueToFirst(params: any) {
  // findUnique를 findFirst로 변환하면서 복합 키 처리
  params.action = 'findFirst';

  if (!params.args || !params.args.where) {
    params['args'] = { where: { deletedAt: null } };
  } else {
    // 복합 키 변환 먼저 수행
    params.args.where = transformCompositeKeys(params.args.where, params.model);
    // 그 다음 soft delete 필터 적용
    params.args.where = addDeletedAtFilter(params.args.where);
  }
}

function handleUpdateOperations(params: any) {
  // 복합 키 변환 먼저 수행
  if (params.args?.where) {
    params.args.where = transformCompositeKeys(params.args.where, params.model);
  }

  if (!params.args.where || params.args.where.deletedAt === undefined) {
    if (params.action === 'update') {
      params.action = 'updateMany';
    }
    if (!params.args.where) {
      params.args['where'] = { deletedAt: null };
    } else {
      params.args.where['deletedAt'] = null;
    }
  }
}

function handleDeleteOperations(params: any) {
  // 복합 키 변환 먼저 수행
  if (params.args?.where) {
    params.args.where = transformCompositeKeys(params.args.where, params.model);
  }

  if (params.action === 'delete') {
    params.action = 'update';
  } else if (params.action === 'deleteMany') {
    params.action = 'updateMany';
  }

  if (!params.args.where) {
    params.args['where'] = { deletedAt: null };
  } else if (params.args.where.deletedAt === undefined) {
    params.args.where['deletedAt'] = null;
  }

  params.args.data = { deletedAt: new Date(), ...params.args.data };
}

function handleUpsertOperations(params: any) {
  // 복합 키 변환 먼저 수행
  if (params.args?.where) {
    params.args.where = transformCompositeKeys(params.args.where, params.model);
    params.args.where = addDeletedAtFilter(params.args.where);
  }

  // upsert의 create 데이터에도 deletedAt: null 설정
  if (params.args?.create && !params.args.create.deletedAt) {
    params.args.create.deletedAt = null;
  }

  // upsert의 update 데이터에서 deletedAt이 명시적으로 설정되지 않은 경우 null 유지
  if (params.args?.update && params.args.update.deletedAt === undefined) {
    params.args.update.deletedAt = null;
  }
}

export function softDeleteMiddleware(): Prisma.Middleware<any> {
  return async (params, next) => {
    if (isSoftDeleteTable(params.model)) {
      switch (params.action) {
        case 'findUnique':
          // findUnique를 findFirst로 변환하면서 복합 키 처리
          handleFindUniqueToFirst(params);
          break;
        case 'findMany':
        case 'findFirst':
          handleFindOperations(params);
          break;
        case 'update':
        case 'updateMany':
          handleUpdateOperations(params);
          break;
        case 'delete':
        case 'deleteMany':
          handleDeleteOperations(params);
          break;
        case 'upsert':
          handleUpsertOperations(params);
          break;
      }
    }

    return next(params);
  };
}
