import { Injectable, OnModule<PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common';
// import { softDeleteMiddleware } from './prisma.middleware';
// import { fieldEncryptionMiddleware } from 'prisma-field-encryption';
import { AppConfigService } from '@config/app/config.service';
import { Prisma, PrismaClient } from '@prisma/client';
import { softDeleteMiddleware } from './prisma.middleware';

@Injectable()
export class PrismaService
  extends PrismaClient<Prisma.PrismaClientOptions, 'query'>
  implements OnModuleInit, OnModuleDestroy
{
  constructor(private readonly appConfigService: AppConfigService) {
    // pass PrismaClientOptions e.g. logging levels or error formatting
    super({
      ...(appConfigService.env !== 'production'
        ? { log: [{ level: 'query', emit: 'stdout' }] }
        : {}),
    });
  }

  async onModuleInit() {
    if (this.appConfigService.env !== 'production') {
      this.$on('query', (e: Prisma.QueryEvent) => {
        console.log('Params: ' + e.params);
        console.log('Duration: ' + e.duration + 'ms');
      });
    }

    // TODO: 아래 미들웨어 추후 검토하고 존재 여부 처리 예정
    this.$use(softDeleteMiddleware());
    // this.$use(
    //   fieldEncryptionMiddleware({
    //     encryptionKey: this.appConfigService.dbPiiEncryptionKey,
    //   }),
    // );

    await this.$connect();
  }
  async onModuleDestroy() {
    await this.$disconnect();
  }

  // softDelete<Key extends Models>(
  //   key: Key,
  //   arg: Parameters<PrismaService[Key]['delete']>[0],
  // ) {
  //   return this.$transaction(async (prisma) => {
  //     // https://github.com/prisma/prisma/discussions/4185
  //     const argWhere = (arg as Prisma.UserDeleteArgs)['where'];
  //     const row = await prisma[key as 'user'].findFirst({
  //       where: { ...argWhere, deletedAt: null },
  //     });
  //     if (row) {
  //       return prisma[key as 'user'].update({
  //         where: {
  //           ...(argWhere as Prisma.UserDeleteArgs['where']),
  //         },
  //         data: { deletedAt: new Date() },
  //       });
  //     }
  //   });
  // }

  // softDeleteMany<Key extends Models>(
  //   key: Key,
  //   arg: Parameters<PrismaService[Key]['deleteMany']>[0],
  // ) {
  //   return this[key as 'user'].updateMany({
  //     where: {
  //       ...(arg?.where as Prisma.UserDeleteManyArgs['where']),
  //       deletedAt: null,
  //     },
  //     data: { deletedAt: new Date() },
  //   });
  // }

  async truncate() {
    // Add other schemas??
    // TODO: 스키마 이름 변경
    const records = (await this.$queryRawUnsafe(
      "SELECT tablename FROM pg_tables WHERE schemaname = 'account'",
    )) as Array<{
      tablename: string;
    }>;

    records.forEach((record) => this.truncateTable(record['tablename']));
  }

  async truncateTable(tablename: string) {
    // Add other schemas??

    if (tablename === undefined || tablename === '_prisma_migrations') {
      return;
    }
    // TODO: 스키마 이름 변경
    try {
      await this.$executeRawUnsafe(
        `TRUNCATE TABLE "account"."${tablename}" CASCADE;`,
      );
    } catch (error) {
      console.log({ error });
    }
  }

  async resetSequences() {
    // Add other schemas??
    // TODO: 스키마 이름 변경
    const results = (await this.$queryRawUnsafe(
      `SELECT c.relname
       FROM pg_class AS c
                JOIN pg_namespace AS n ON c.relnamespace = n.oid
       WHERE c.relkind = 'S'
         AND n.nspname = 'account'`,
    )) as Array<any>;
    for (const { record } of results) {
      await this.$executeRawUnsafe(
        `ALTER SEQUENCE "account"."${record['relname']}" RESTART WITH 1;`,
      );
    }
  }
}
