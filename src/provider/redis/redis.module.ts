import { RedisConfigModule } from '@config/redis/config.module';
import { RedisConfigService } from '@config/redis/config.service';
import { RedisModule, RedisModuleOptions } from '@liaoliaots/nestjs-redis';
import { Global, Module } from '@nestjs/common';
import { RedisCacheService } from './redis.service';
@Global()
@Module({
  imports: [
    RedisModule.forRootAsync({
      imports: [RedisConfigModule],
      inject: [RedisConfigService],
      useFactory: async (...args: unknown[]): Promise<RedisModuleOptions> => {
        const configService = args[0] as RedisConfigService;
        return {
          config: {
            host: configService.host,
            port: configService.port,
            password: configService.password,
          },
        };
      },
    }),
  ],
  providers: [RedisCacheService],
  exports: [RedisCacheService],
})
export class RedisCacheModule {}
