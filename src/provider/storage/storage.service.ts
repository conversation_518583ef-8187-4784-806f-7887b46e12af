import { StorageConfigService } from '@config/storage/config.service';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { Client } from 'minio';
import { v7 as uuidv7 } from 'uuid';

// 버킷 이름 관리
const Buckets = ['goods', 'booth', 'event', 'profile'] as const;
export const BucketNames = [...Buckets] as const;
// 버킷 이름 강제
export type BucketNames = (typeof BucketNames)[number];

@Injectable()
export class StorageService {
  constructor(private readonly storageConfigService: StorageConfigService) {
    (async () => {
      await this.initBucket();
    })();
  }

  // URL에서 http:// 또는 https:// 프리픽스를 제거하는 헬퍼 함수
  private removeUrlPrefix(url: string): string {
    return url.replace(/^(https?:\/\/)/, '');
  }

  // minio 셋팅입니다.
  private getClient(isExternalEndPoint = false) {
    return new Client({
      accessKey: this.storageConfigService.accessKeyId,
      secretKey: this.storageConfigService.secretAccessKey,
      region: this.storageConfigService.storageRegion,
      ...(isExternalEndPoint
        ? {
            endPoint: this.removeUrlPrefix(
              this.storageConfigService.fileExternalServerHost,
            ),
            port: this.storageConfigService.fileExternalServerPort,
          }
        : {
            endPoint: this.removeUrlPrefix(
              this.storageConfigService.fileServerHost,
            ),
            port: this.storageConfigService.fileServerPort,
          }),
      useSSL: isExternalEndPoint,
    });
  }

  // 여기서 버킷이름 수정가능
  private bucketName(name: BucketNames) {
    return `${name}-${this.storageConfigService.env}`;
  }

  /**
   * S3 버킷을 초기화합니다.
   * @async
   * @private
   * @function initBucket
   * @description
   * 주어진 버킷 이름들에 대해 S3 버킷을 초기화하고, 각 버킷에 접근 정책을 설정합니다.
   *
   * @example
   * // 사용 예시
   * this.initBucket();
   */
  private async initBucket() {
    try {
      for (const bucket of BucketNames) {
        const bucketName = this.bucketName(bucket);

        const json = JSON.stringify({
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Principal: {
                AWS: ['*'],
              },
              Action: ['s3:GetObject'],
              Resource: [`arn:aws:s3:::${bucketName}/*`],
            },
          ],
        });
        const client = this.getClient();
        const exist = await client.bucketExists(bucketName);
        if (!exist) {
          await client.makeBucket(bucketName);
        }
        if (json) {
          await client.setBucketPolicy(bucketName, json);
        }
      }
    } catch (error) {
      console.error('storageClient :', error);
    }
  }

  private makePath(fileName: string, path?: string) {
    return path ? `${path}/${fileName}` : fileName;
  }

  // 테스트 환경과 프로덕션 환경을 맞추기 위함
  private getFileServerUrl() {
    return `${this.storageConfigService.fileExternalServerHost}:${this.storageConfigService.fileExternalServerPort}/`;
  }

  /**
   * 주어진 URL을 분석하여 버킷 이름과 객체 이름을 추출합니다.
   * @private
   * @function urlToPatch
   * @param {string} url - 분석할 URL
   * @return {{ bucketName: string, objectName: string } | null} 추출된 버킷 이름과 객체 이름을 포함한 객체 또는 null
   * @throws {BadRequestException} URL의 유효성이 확인되지 않을 경우 예외를 발생시킵니다.
   * @description
   * 주어진 URL을 분석하여 S3 버킷 이름과 객체 이름을 추출하는 함수입니다.
   * @example
   * // 사용 예시
   * const result = this.urlToPatch('https://example.com/bucketName/objectName');
   * console.log(result); // { bucketName: 'bucketName', objectName: 'objectName' } 또는 null
   */
  private urlToPatch(url: string) {
    const urlObject = new URL(url);
    if (
      urlObject.hostname !== this.storageConfigService.fileExternalServerHost
    ) {
      return null;
    }
    const urlPath = urlObject.pathname.split('/');
    urlPath.shift();

    if (urlPath.length < 2) {
      throw new BadRequestException('Invalid string in the URL[0]');
    }
    const bucketName = urlPath.shift();
    if (!bucketName) {
      throw new BadRequestException('Invalid string in the URL[1]');
    }
    if (!(BucketNames as readonly string[]).includes(bucketName)) {
      throw new BadRequestException("Bucket names that don't exist");
    }
    const objectName = urlPath.join('/');
    if (!objectName) {
      throw new BadRequestException('Invalid string in the URL[2]');
    }

    return {
      bucketName,
      objectName,
    };
  }

  /**
   * 사전 서명된 URL을 생성합니다.
   * @async
   * @function getPresignedUrl
   * @param {BucketNames} bucketName - 버킷 이름
   * @param {string} originalFileName - 원본 파일 이름
   * @param {string} [path] - 선택적 경로
   * @throws {BadRequestException} 파일 이름이 잘못된 경우 예외를 던집니다. 파일은 항상 2글자 이상입니다.
   * @description
   * 주어진 버킷 이름과 원본 파일 이름을 사용하여 사전 서명된 업로드 URL을 생성하고, 파일 서버 URL과 함께 반환합니다.
   * 이 함수는 파일을 업로드할 수 있는 사전 서명된 URL과 관련 정보를 반환합니다.
   */
  async getPresignedUrl(
    bucketName: BucketNames,
    originalFileName: string,
    path?: string,
  ) {
    const bucket = this.bucketName(bucketName);
    const uuid = uuidv7();
    const fileNames = originalFileName.split('.');

    if (fileNames.length < 2) {
      throw new BadRequestException('Please enter the exact name of the file.');
    }

    const extension = fileNames.pop();
    const updateFileName = this.makePath(
      `${uuid}/${fileNames.join('.')}.${extension}`,
      path,
    );
    const uploadUrl = await this.getClient(true).presignedPutObject(
      bucket,
      updateFileName,
      60 * 60 * 3,
    );
    console.log('uploadUrl', uploadUrl);

    const url = `${this.getFileServerUrl()}${bucket}/${updateFileName}`;
    return {
      url,
      uploadUrl,
      originalFileName,
      fileName: updateFileName,
    };
  }

  /**
   * 객체를 삭제합니다.
   * @async
   * @function deleteObject
   * @param {Object} dto - 삭제할 객체의 URL을 포함한 데이터 전송 객체
   * @param {string} dto.url - 삭제할 객체의 URL
   * @throws {BadRequestException} 유효하지 않은 URL 또는 존재하지 않는 버킷 이름인 경우 예외를 던집니다.
   * @throws {InternalServerErrorException} 파일 삭제에 실패한 경우 예외를 던집니다.
   * @description
   * 주어진 URL을 분석하여 해당 스토리지 버킷에서 객체를 삭제합니다.
   *
   * @example
   * // 사용 예시
   * const result = await this.deleteObject({ url: 'https://example.com/bucketName/objectName' });
   * console.log(result); // true 또는 false
   */
  async deleteObject(dto: { url: string }) {
    const urlPatch = this.urlToPatch(dto.url);
    if (!urlPatch) {
      return false;
    }
    try {
      await this.getClient().removeObject(
        urlPatch.bucketName,
        decodeURI(urlPatch.objectName),
      );
    } catch (e) {
      console.error('Delete File Error', e);
      throw new InternalServerErrorException('파일 삭제에 실패했습니다.');
    }
    return true;
  }

  /**
   * 주어진 URL로부터 객체를 가져옵니다.
   * @function getObjectForUrl
   * @param {Object} dto - URL을 포함한 데이터 전송 객체
   * @param {string} dto.url - 가져올 객체의 URL
   * @return {Promise<ObjectReadResponse> | null} 객체를 가져오는 프로미스 또는 null
   * @description
   * 주어진 URL을 분석하여 해당하는 S3 버킷에서 객체를 가져오는 함수입니다. URL이 유효하지 않은 경우 null을 반환합니다.
   *
   * @example
   * // 사용 예시
   * const result = await this.getObjectForUrl({ url: 'https://example.com/bucketName/objectName' });
   * console.log(result); // 객체 또는 null
   */
  getObjectForUrl(dto: { url: string }) {
    const urlPatch = this.urlToPatch(dto.url);
    if (!urlPatch) {
      return null;
    }
    console.log(urlPatch);
    return this.getClient().getObject(
      urlPatch.bucketName,
      decodeURI(urlPatch.objectName),
    );
  }

  /**
   * 버킷 이름과 객체 이름을 사용하여 객체를 가져옵니다.
   * @function getObject
   * @param {Object} dto - 버킷 이름과 객체 이름을 포함한 데이터 전송 객체
   * @param {BucketNames} dto.bucketName - 객체가 저장된 버킷의 이름
   * @param {string} dto.objectName - 가져올 객체의 이름
   * @return {Promise<ObjectReadResponse>} 객체를 가져오는 프로미스
   * @description
   * 주어진 버킷 이름과 객체 이름을 사용하여 해당하는 S3 버킷에서 객체를 가져오는 함수입니다.
   *
   * @example
   * // 사용 예시
   * const result = await this.getObject({ bucketName: 'myBucket', objectName: 'myObject' });
   * console.log(result); // 객체
   */
  getObject(dto: { bucketName: BucketNames; objectName: string }) {
    return this.getClient().getObject(
      dto.bucketName,
      decodeURI(dto.objectName),
    );
  }
}
