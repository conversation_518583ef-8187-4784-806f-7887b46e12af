import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import Joi from 'joi';
import appConfig from './config';
import { StorageConfigService } from './config.service';

// APP_NAME, SERVICE_NAME
@Global()
@Module({
  imports: [
    ConfigModule.forRoot({
      load: [appConfig],
      validationSchema: Joi.object({
        REGION: Joi.string().required(),
        ACCESS_KEY_ID: Joi.string().required(),
        SECRET_ACCESS_KEY: Joi.string().required(),
        FILE_SERVER_HOST: Joi.string().required(),
        FILE_SERVER_PORT: Joi.number().default(9000),
        FILE_EXTERNAL_SERVER_HOST: Joi.string().required(),
        FILE_EXTERNAL_SERVER_PORT: Joi.number().default(443),
      }),
      validationOptions: {
        allowUnknown: true,
        abortEarly: true,
      },
    }),
  ],
  providers: [ConfigService, StorageConfigService],
  exports: [ConfigService, StorageConfigService],
})
export class StorageConfigModule {}
