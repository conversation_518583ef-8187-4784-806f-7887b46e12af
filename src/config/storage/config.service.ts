import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class StorageConfigService {
  constructor(private readonly configService: ConfigService) {}

  private get<T>(key: string) {
    const value = this.configService.get<T>(key);
    if (!value) {
      throw new InternalServerErrorException('app env config error');
    }
    return value;
  }
  get storageRegion(): string {
    return this.get<string>('storage.REGION');
  }

  get accessKeyId(): string {
    return this.get<string>('storage.ACCESS_KEY_ID');
  }

  get secretAccessKey(): string {
    return this.get<string>('storage.SECRET_ACCESS_KEY');
  }

  get fileServerHost(): string {
    return this.get<string>('storage.FILE_SERVER_HOST');
  }

  get fileServerPort(): number {
    return Number(this.get<number>('storage.FILE_SERVER_PORT'));
  }

  get fileExternalServerHost(): string {
    return this.get<string>('storage.FILE_EXTERNAL_SERVER_HOST');
  }

  get fileExternalServerPort(): number {
    return Number(this.get<number>('storage.FILE_EXTERNAL_SERVER_PORT'));
  }

  get env(): string {
    return this.get<string>('storage.NODE_ENV');
  }
}
