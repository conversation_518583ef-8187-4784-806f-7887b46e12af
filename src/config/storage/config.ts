import { registerAs } from '@nestjs/config';

export default registerAs('storage', () => ({
  NODE_ENV: process.env.NODE_ENV,
  BUCKET_NAME: process.env.BUCKET_NAME,
  REGION: process.env.REGION,
  ACCESS_KEY_ID: process.env.ACCESS_KEY_ID,
  SECRET_ACCESS_KEY: process.env.SECRET_ACCESS_KEY,
  FILE_SERVER_HOST: process.env.FILE_SERVER_HOST,
  FILE_SERVER_PORT: process.env.FILE_SERVER_PORT,
  FILE_EXTERNAL_SERVER_HOST: process.env.FILE_EXTERNAL_SERVER_HOST,
  FILE_EXTERNAL_SERVER_PORT: process.env.FILE_EXTERNAL_SERVER_PORT,
}));
