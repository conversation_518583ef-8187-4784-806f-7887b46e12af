import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import appRoot from 'app-root-path';
import { readFileSync } from 'fs';

@Injectable()
export class AppConfigService {
  constructor(private readonly configService: ConfigService) {}

  private readonly logger = new Logger(AppConfigService.name);

  private get<T>(key: string) {
    const value = this.configService.get<T>(key);
    if (!value) {
      throw new InternalServerErrorException('app env config error');
    }
    return value;
  }

  get env(): string {
    return this.get<string>('app.NODE_ENV');
  }

  get appName(): string {
    return this.get<string>('app.APP_NAME');
  }

  get serviceName() {
    return this.get<string>('app.SERVICE_NAME');
  }

  get jwtPublic(): string {
    const filePath = appRoot.resolve('/certs/public-key.pem');
    let str;
    try {
      str = readFileSync(filePath).toString();
    } catch (e) {
      throw new InternalServerErrorException('app env config error - public');
    }
    return str;
  }

  get jwtPrivate(): string {
    const filePath = appRoot.resolve('/certs/private-key.pem');
    let str;
    try {
      str = readFileSync(filePath).toString();
    } catch (e) {
      throw new InternalServerErrorException('app env config error - private');
    }
    return str;
  }

  get jwtIssuer(): string {
    return this.get<string>('app.JWT_ISSUER');
  }

  get accessTokenExpire() {
    return Number(this.get<number>('app.ACCESS_TOKEN_SECRET_EXPIRE'));
  }

  get refreshTokenExpire() {
    return Number(this.get<number>('app.REFRESH_TOKEN_SECRET_EXPIRE'));
  }

  get domain() {
    return this.get<string>('app.DOMAIN');
  }

  // payple Key Set
  get paypleClientKey() {
    return this.get<string>('app.PAYPLE_CLIENT_KEY');
  }

  get paypleCstId() {
    return this.get<string>('app.PAYPLE_CST_ID');
  }

  get paypleCustKey() {
    return this.get<string>('app.PAYPLE_CUST_KEY');
  }

  get paypleRefundKey() {
    return this.get<string>('app.PAYPLE_REFUND_KEY');
  }

  get paypleCode() {
    return this.get<string>('app.PAYPLE_CODE');
  }

  get baseUrl() {
    return this.get<string>('app.BASE_URL');
  }

  get mailHost(): string {
    return this.get<string>('app.MAIL_HOST');
  }

  get mailPort(): number {
    return Number(this.get<number>('app.MAIL_PORT'));
  }

  get mailUser(): string {
    return this.get<string>('app.MAIL_USER');
  }

  get mailPass(): string {
    return this.get<string>('app.MAIL_PASS');
  }
}
