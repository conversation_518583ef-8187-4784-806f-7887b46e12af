import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import Joi from 'joi';
import appConfig from './config';
import { AppConfigService } from './config.service';

// APP_NAME, SERVICE_NAME
@Global()
@Module({
  imports: [
    ConfigModule.forRoot({
      load: [appConfig],
      validationSchema: Joi.object({
        APP_NAME: Joi.string().default('sherry'),
        NODE_ENV: Joi.string()
          .valid('development', 'production', 'test', 'staging')
          .default('development'),
        SERVICE_NAME: Joi.string().default('sherry'),
        JWT_ISSUER: Joi.string().default('sherry.sweech.io'),
        ACCESS_TOKEN_SECRET_EXPIRE: Joi.number().required(),
        REFRESH_TOKEN_SECRET_EXPIRE: Joi.number().required(),
        DOMAIN: Joi.string().default('sherry.gg'),
        // payple Key set.
        PAYPLE_CLIENT_KEY: Joi.string().required(),
        PAYPLE_CST_ID: Joi.string().required(),
        PAYPLE_CUST_KEY: Joi.string().required(),
        PAYPLE_REFUND_KEY: Joi.string().required(),
        PAYPLE_CODE: Joi.string().default('dodoCode67'),
        BASE_URL: Joi.string().required(),

        // Mail
        MAIL_HOST: Joi.string().required(),
        MAIL_PORT: Joi.number().required(),
        MAIL_USER: Joi.string().required(),
        MAIL_PASS: Joi.string().required(),
      }),
      validationOptions: {
        allowUnknown: true,
        abortEarly: true,
      },
    }),
  ],
  providers: [ConfigService, AppConfigService],
  exports: [ConfigService, AppConfigService],
})
export class AppConfigModule {}
