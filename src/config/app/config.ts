import { registerAs } from '@nestjs/config';

export default registerAs('app', () => ({
  APP_NAME: process.env.APP_NAME,
  NODE_ENV: process.env.NODE_ENV,
  SERVICE_NAME: process.env.SERVICE_NAME,
  JWT_ISSUER: process.env.JWT_ISSUER,
  ACCESS_TOKEN_SECRET_EXPIRE: process.env.ACCESS_TOKEN_SECRET_EXPIRE,
  REFRESH_TOKEN_SECRET_EXPIRE: process.env.REFRESH_TOKEN_SECRET_EXPIRE,
  DOMAIN: process.env.DOMAIN,
  // payple Key set
  PAYPLE_CLIENT_KEY: process.env.PAYPLE_CLIENT_KEY,
  PAYPLE_CST_ID: process.env.PAYPLE_CST_ID,
  PAYPLE_CUST_KEY: process.env.PAYPLE_CUST_KEY,
  PAYPLE_REFUND_KEY: process.env.PAYPLE_REFUND_KEY,
  PAYPLE_CODE: process.env.PAYPLE_CODE,
  BASE_URL: process.env.BASE_URL,
  // Mail
  MAIL_HOST: process.env.MAIL_HOST,
  MAIL_PORT: process.env.MAIL_PORT,
  MAIL_USER: process.env.MAIL_USER,
  MAIL_PASS: process.env.MAIL_PASS,
}));
