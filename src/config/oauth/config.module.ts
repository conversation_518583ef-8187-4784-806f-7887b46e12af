import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import Joi from 'joi';
import appConfig from './config';
import { OauthConfigService } from './config.service';

// APP_NAME, SERVICE_NAME
@Global()
@Module({
  imports: [
    ConfigModule.forRoot({
      load: [appConfig],
      validationSchema: Joi.object({
        GOOGLE_CLIENT_ID: Joi.string().required(),
        GOOGLE_CLIENT_SECRET: Joi.string().required(),
        APPLE_CLIENT_ID: Joi.string().required(),
        APPLE_PRIVATE_KEY: Joi.string().required(),
        APPLE_TEAM_ID: Joi.string().required(),
        APPLE_KEY_ID: Joi.string().required(),
      }),
      validationOptions: {
        allowUnknown: true,
        abortEarly: true,
      },
    }),
  ],
  providers: [ConfigService, OauthConfigService],
  exports: [ConfigService, OauthConfigService],
})
export class OauthConfigModule {}
