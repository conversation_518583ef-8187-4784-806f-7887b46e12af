import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class OauthConfigService {
  constructor(private readonly configService: ConfigService) {}

  private get<T>(key: string) {
    const value = this.configService.get<T>(key);
    if (!value) {
      throw new InternalServerErrorException('app env config error');
    }
    return value;
  }

  get googleClientId(): string {
    return this.get<string>('oauth.GOOGLE_CLIENT_ID');
  }

  get googleClientSecret(): string {
    return this.get<string>('oauth.GOOGLE_CLIENT_SECRET');
  }

  get appleClientID(): string {
    return this.get<string>('oauth.APPLE_CLIENT_ID');
  }

  // TODO: 원래는 파일임 일단 임시로 이렇게 처리
  get applePrivateKey(): string {
    return this.get<string>('oauth.APPLE_PRIVATE_KEY');
  }

  get appleTeamId(): string {
    return this.get<string>('oauth.APPLE_TEAM_ID');
  }

  get appleKeyID(): string {
    return this.get<string>('oauth.APPLE_KEY_ID');
  }
}
