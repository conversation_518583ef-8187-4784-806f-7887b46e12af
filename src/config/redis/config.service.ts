import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class RedisConfigService {
  constructor(private readonly configService: ConfigService) {}

  private get<T>(key: string) {
    const value = this.configService.get<T>(key);
    if (!value) {
      throw new InternalServerErrorException('redis app env config error');
    }
    return value;
  }

  get host() {
    return this.get<string>('redis.REDIS_HOST');
  }

  get port() {
    return this.get<number>('redis.REDIS_PORT');
  }

  get password() {
    return this.get<string>('redis.REDIS_PASSWORD');
  }
}
