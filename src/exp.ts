// export enum DeliverySizeEnum {
// 	SP = 3000, // Pack || CE-037, CB-206 C형 박스
// 	S = 4000, //
// 	M = 5000, //
// 	L = 6000,
// }

// export const enum BoxSizeEnum {
// 	PACK = "pack", // https://www.xn--ij1bycz62bkyq.com/product/에어캡봉투접착10020050200매/400/category/104/display/1/#none
// 	CE_307 = "C307",
// 	CE_206 = "C206",
// 	BE_018 = "B018",
// }

// export const enum GoodsSizeEnum {
// 	SMALL_P = "SmallP",
// 	SMALL = "Small",
// 	MEDIUM = "Medium",
// 	LARGE = "Large",
// 	EXTRA = "Extra",
// }

// export interface GoodsDeliveryType {
// 	count: number;
// 	goodsSize: GoodsSizeEnum;
// 	goodsUuid: string;
// 	isSum: boolean;
// }

// export function calculatingDeliveryCost(goodsList: GoodsDeliveryType[]) {
// 	const box: {
// 		SP: Array<GoodsDeliveryType>;
// 		S: Array<GoodsDeliveryType>;
// 		M: Array<GoodsDeliveryType>;
// 		L: Array<GoodsDeliveryType>;
// 		E: Array<GoodsDeliveryType>;
// 	} = {
// 		SP: [],
// 		S: [],
// 		M: [],
// 		L: [],
// 		E: [],
// 	};

// 	const boxOther: {
// 		SP: Array<GoodsDeliveryType>;
// 		S: Array<GoodsDeliveryType>;
// 		M: Array<GoodsDeliveryType>;
// 		L: Array<GoodsDeliveryType>;
// 		E: Array<GoodsDeliveryType>;
// 	} = {
// 		SP: [],
// 		S: [],
// 		M: [],
// 		L: [],
// 		E: [],
// 	};
//   const goodsFiler = goodsList.reduce((pr:{
//     sum: GoodsDeliveryType[],
//     unSum: GoodsDeliveryType[]
//   }, goods)=>{
//     if(goods.isSum){
//       pr.sum.push(goods)
//     }else{
//       pr.sum.push(goods)

//     }
//     return pr
//   }, {
//     sum: [],
//     unSum: []
//   })
// 	goodsFiler.sum.forEach((item) => {
// 		switch (item.goodsSize) {
// 			case GoodsSizeEnum.SMALL_P:
// 				box.SP.push(item);
// 				break;
// 			case GoodsSizeEnum.SMALL:
// 				box.S.push(item);
// 				break;
// 			case GoodsSizeEnum.MEDIUM:
// 				box.M.push(item);
// 				break;
// 			case GoodsSizeEnum.LARGE:
// 				box.L.push(item);
// 				break;
// 			case GoodsSizeEnum.EXTRA:
// 				box.E.push(item);
// 				break;
// 		}
// 	});

//   goodsFiler.unSum.forEach((item) => {
// 		switch (item.goodsSize) {
// 			case GoodsSizeEnum.SMALL_P:
// 				box.SP.push(item);
// 				break;
// 			case GoodsSizeEnum.SMALL:
// 				box.S.push(item);
// 				break;
// 			case GoodsSizeEnum.MEDIUM:
// 				box.M.push(item);
// 				break;
// 			case GoodsSizeEnum.LARGE:
// 				box.L.push(item);
// 				break;
// 			case GoodsSizeEnum.EXTRA:
// 				box.E.push(item);
// 				break;
// 		}
// 	});
// 	const boxEntries = Object.entries(box);

// 	boxEntries.forEach(([key, array], index) => {
// 		if (array.length >= 2 && index < boxEntries.length - 1 && key !== "L") {
// 			const nextKey = boxEntries[index + 1][0];
// 			box[nextKey].push(...array);
// 			box[key] = [];
// 		}
// 	});

// 	return {
// 		totalPice: 0,
// 		box,
// 	};
// }

// console.log(
// 	calculatingDeliveryCost([
// 		{
// 			count: 1,
// 			goodsSize: GoodsSizeEnum.SMALL_P,
// 			goodsUuid: "test1",
// 			isSum: true,
// 		},
// 		{
// 			count: 1,
// 			goodsSize: GoodsSizeEnum.SMALL_P,
// 			goodsUuid: "test2",
// 			isSum: true,
// 		},
// 		{
// 			count: 1,
// 			goodsSize: GoodsSizeEnum.SMALL_P,
// 			goodsUuid: "test3",
// 			isSum: false,
// 		},
// 	])
// );
