import { IsPublic } from '@common/decorators/public.decorator';
import { Controller, Get } from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';
import { AppService } from './app.service';
import { ignoreTransformInterceptor } from '@common/decorators/ignore-transform-interceptor.decorator';
import { readFileSync } from 'fs';

@IsPublic()
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello() {
    return this.appService.getHello();
  }

  @Get('landing')
  getLanding() {
    return this.appService.getLanding();
  }

  @ApiOperation({
    summary: '현 유저수',
    description: '유저 몇명인가?',
  })
  @Get('count')
  landingCount() {
    return this.appService.landingCount();
  }

  @ApiOperation({
    summary: '성공',
    description: '성공',
  })
  @IsPublic()
  @Get('success')
  @ignoreTransformInterceptor()
  async paymentSuccess() {
    return readFileSync('src/module/order/view/success.html', 'utf8');
  }

  @ApiOperation({
    summary: '실패',
    description: '실패',
  })
  @IsPublic()
  @Get('fail')
  @ignoreTransformInterceptor()
  async paymentFail() {
    return readFileSync('src/module/order/view/fail.html', 'utf8');
  }
}
