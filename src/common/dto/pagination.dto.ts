import { RequestPagination } from '@common/types/dto.types';
import { Pagination } from '@common/types/response.types';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsInt, IsOptional } from 'class-validator';

export class PaginationDto implements RequestPagination {
  @ApiProperty({
    description: 'page number',
    default: 1,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  page?: number;

  @ApiProperty({
    description: 'Take count',
    default: 10,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  take?: number;

  get getTake() {
    return !this.take || this.take <= 0 ? 10 : this.take;
  }

  get getPage() {
    return !this.page || this.page <= 0 ? 1 : this.page;
  }

  get getSkip() {
    return (this.getPage - 1) * this.getTake;
  }

  toPrismaPaging = () => ({
    skip: this.getSkip,
    take: this.getTake,
  });

  createResponse = <T>(list: Array<T>, count?: number): Pagination => ({
    list,
    paging: {
      totalRow: count,
      currentPage: this.getPage,
      take: this.getTake,
    },
  });
}
