import TransformDate from '@common/decorators/transform-date.decorator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsOptional } from 'class-validator';

export class DateRangeDto {
  @ApiPropertyOptional({
    description: 'date range from',
    type: Number,
  })
  @IsOptional()
  @TransformDate({ required: false })
  @Type(() => Number)
  @IsDate()
  startAt?: Date;

  @ApiPropertyOptional({
    description: 'date range to',
    type: Number,
  })
  @IsOptional()
  @TransformDate({ required: false })
  @Type(() => Number)
  @IsDate()
  endAt?: Date;
}
