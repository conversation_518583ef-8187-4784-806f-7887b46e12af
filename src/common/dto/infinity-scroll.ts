import { RequestInfinityScroll } from '@common/types/dto.types';
import { InfinityPagination } from '@common/types/response.types';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsIn, IsInt, IsOptional, IsString } from 'class-validator';

export class InfinityScroll<T extends number | string | Date = number>
  implements RequestInfinityScroll<T>
{
  @ApiProperty({
    description: 'cursor',
    type: String,
    required: false,
  })
  @IsOptional()
  cursor: T | undefined;

  @ApiProperty({
    description: 'take',
    required: false,
    type: Number,
    example: 10,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  take: number | undefined;

  @ApiProperty({
    description: 'orderBy',
    required: false,
    enum: ['asc', 'desc'],
  })
  @IsString()
  @IsIn(['asc', 'desc'])
  @IsOptional()
  order?: 'asc' | 'desc';

  get getTake() {
    return this.take == null || this.take <= 0 ? 12 : this.take;
  }

  get getOrder(): 'asc' | 'desc' {
    return this.order ?? 'desc';
  }
  toPrismaPaging<U extends string>(basis: U, order: 'asc' | 'desc') {
    if (
      this.cursor !== null &&
      this.cursor !== undefined &&
      this.cursor !== '' &&
      this.cursor !== 0
    ) {
      return {
        cursor: { [basis]: this.cursor } as Record<U, T>,
        take: this.getTake + 1,
        orderBy: { [basis]: order } as Record<U, 'asc' | 'desc'>,
        skip: 1,
      };
    } else {
      return {
        take: this.getTake + 1,
        orderBy: { [basis]: order },
      } as { take: number; orderBy: Record<U, 'asc' | 'desc'> };
    }
  }

  createResponse<T, U extends keyof T>(
    list: Array<T>,
    basis: U,
    count?: number,
  ): InfinityPagination {
    const cursors = list.length === this.getTake + 1 ? list.pop() : null;
    return {
      list,
      paging: {
        cursor: cursors ? cursors[basis] : null,
        take: this.getTake,
        totalRow: count,
      },
    };
  }
}
