import {
  IAccessTokenPayload,
  IRefreshTokenPayload,
  RequestUser,
  UserRole,
  isRefreshToken,
} from '@common/types/token.types';
import { AppConfigService } from '@config/app/config.service';
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { FastifyRequest } from 'fastify';
import { Observable } from 'rxjs';

@Injectable()
export class RefreshTokenGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly config: AppConfigService,
  ) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const req: FastifyRequest & { user: RequestUser } = context
      .switchToHttp()
      .getRequest();
    const payload = this.parseJwkPayload(req);

    if (!payload.sub) {
      throw new UnauthorizedException('invalid token payload');
    }

    if (!isRefreshToken(payload)) {
      throw new UnauthorizedException('invalid token type [2]');
    }

    // 리프레쉬 토큰은 셀러이든 유저이든 둘다 일반유저로
    req.user = {
      id: payload.sub,
      role: UserRole.USER,
      snsPlatformId: payload.s,
    };

    return true;
  }

  private parseJwkPayload(
    req: FastifyRequest,
  ): IAccessTokenPayload | IRefreshTokenPayload {
    const payload: IAccessTokenPayload | IRefreshTokenPayload =
      this.verifyingToken(req);
    return payload;
  }

  private verifyingToken(
    req: FastifyRequest,
  ): IAccessTokenPayload | IRefreshTokenPayload {
    const authorization = req.headers.authorization;
    if (!authorization) {
      throw new UnauthorizedException('required header');
    }
    const token = authorization.replace('Bearer ', '').replace('bearer ', '');
    let rs;
    try {
      rs = this.jwtService.verify(token, {
        secret: this.config.jwtPublic,
      });
    } catch (e) {
      throw new UnauthorizedException('token parse error');
    }

    if (rs.iss !== this.config.jwtIssuer) {
      throw new UnauthorizedException('issuer mismatch');
    }

    return rs;
  }
}
