import {
  IAccessTokenPayload,
  IRefreshTokenPayload,
  isRefreshToken,
  RequestUser,
} from '@common/types/token.types';
import { AppConfigService } from '@config/app/config.service';
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { FastifyRequest } from 'fastify';
import { Observable } from 'rxjs';

@Injectable()
export class UserJWKGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly config: AppConfigService,
    private readonly reflector: Reflector,
  ) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const isPublic = this.reflector.getAllAndOverride('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }
    const req: FastifyRequest & { user: RequestUser } = context
      .switchToHttp()
      .getRequest();
    const payload = this.parseJwkPayload(req);

    if (!payload.sub) {
      throw new UnauthorizedException('invalid token payload');
    }

    if (isRefreshToken(payload)) {
      throw new UnauthorizedException('invalid token type [1]');
    }

    req.user = {
      id: payload.sub,
      role: payload.r,
      snsPlatformId: payload.s, // sns platform id
    };
    return true;
  }

  private parseJwkPayload(
    req: FastifyRequest,
  ): IAccessTokenPayload | IRefreshTokenPayload {
    const payload: IAccessTokenPayload | IRefreshTokenPayload =
      this.verifyingToken(req);
    return payload;
  }

  private verifyingToken(
    req: FastifyRequest,
  ): IAccessTokenPayload | IRefreshTokenPayload {
    const authorization = req.headers.authorization;
    if (!authorization) {
      throw new UnauthorizedException('required header');
    }
    const token = authorization.replace('Bearer ', '').replace('bearer ', '');
    let rs;
    try {
      rs = this.jwtService.verify(token, {
        secret: this.config.jwtPublic,
      });
    } catch (e) {
      throw new UnauthorizedException('token parse error');
    }

    if (rs.iss !== this.config.jwtIssuer) {
      throw new UnauthorizedException('issuer mismatch');
    }

    return rs;
  }
}
