import { ApiProperty } from '@nestjs/swagger';

class Paging {
  @ApiProperty({
    example: 1,
    required: false,
  })
  totalRow!: number;

  @ApiProperty({
    example: 1,
  })
  currentPage!: number;

  @ApiProperty({
    example: 10,
  })
  take!: number;
}
export class PagingDto {
  @ApiProperty()
  list!: object[];

  @ApiProperty({
    type: Paging,
  })
  paging!: Paging;
}

class InfinityPaging {
  @ApiProperty({
    example: 1,
    description: '총 Row 갯수 없는경우도 있음',
    required: false,
  })
  totalRow!: number;

  @ApiProperty({
    description: '해당 페이지의 커서',
  })
  cursor!: string | number;

  @ApiProperty({
    example: 10,
    description: '한페이지의 row 갯수',
  })
  take!: number;
}
export class InfinityPagingDto {
  @ApiProperty()
  list!: object[];

  @ApiProperty({
    type: InfinityPaging,
  })
  paging!: InfinityPaging;
}

export class OkDto {
  @ApiProperty({
    example: true,
  })
  success!: boolean;
  @ApiProperty({
    example: '',
  })
  error!: string;
  @ApiProperty({
    example: '',
  })
  message!: string;
  @ApiProperty()
  data!: object | string;
}
export class ErrorDto {
  @ApiProperty()
  data!: object | string;
  @ApiProperty({
    example: false,
  })
  success!: boolean;
  @ApiProperty()
  error!: string;
  @ApiProperty({
    example: '${URL} 정보를 가져오지 못했습니다.',
  })
  message!: string;
}
