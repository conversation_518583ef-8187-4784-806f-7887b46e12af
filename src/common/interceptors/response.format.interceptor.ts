import {
  CallH<PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
// import { isRabbitContext } from '@golevelup/nestjs-rabbitmq';
import { ResponseObject } from '@common/types/response.types';

@Injectable()
export class ResponseFormatInterceptor implements NestInterceptor {
  constructor(private reflector: Reflector) {}

  intercept(
    context: ExecutionContext,
    next: <PERSON>Hand<PERSON>,
  ): Observable<ResponseObject<Record<string, unknown>>> {
    // ): Observable<CommonResponse | InternalResponse> {

    // https://github.com/golevelup/nestjs/issues/112
    // TO RMQ
    // if (isRabbitContext(context)) {
    //   return next.handle() as unknown as Observable<
    //     ResponseObject<Record<string, unknown>>
    //   >;
    // }

    const ignore = this.reflector.getAllAndOverride<boolean>(
      ResponseFormatInterceptor,
      [context.getHandler(), context.getClass()],
    );
    if (ignore) {
      return next.handle().pipe(map((data) => data));
    }

    return next.handle().pipe(
      map((data) =>
        // if (
        //   data &&
        //   typeof data === 'object' &&
        //   data.hasOwnProperty('internalResponse')
        // ) {
        //   return { payload: data.internalResponse };
        // }

        ({
          success: true,
          error: '',
          data: data ?? {},
          message: '',
        }),
      ),
    );
  }
}
