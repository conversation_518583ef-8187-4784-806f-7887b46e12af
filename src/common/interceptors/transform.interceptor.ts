import { IGNORE_TRANSFORM_INTERCEPTOR } from '@common/decorators/ignore-transform-interceptor.decorator';
import { CommonResponse } from '@common/types/response.types';
import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class TransformInterceptor implements NestInterceptor {
  constructor(private reflector: Reflector) {}

  private convertBigIntToString(data: any): any {
    if (Array.isArray(data)) {
      return data.map((item) => this.convertBigIntToString(item));
    } else if (data !== null && typeof data === 'object') {
      Object.keys(data).forEach((key) => {
        if (typeof data[key] === 'bigint') {
          data[key] = data[key].toString();
        } else if (typeof data[key] === 'object') {
          this.convertBigIntToString(data[key]);
        }
      });
    }
    return data;
  }

  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<CommonResponse> {
    // ): Observable<CommonResponse | InternalResponse> {
    // if (isRabbitContext(context)) {
    //   return next.handle();
    // }

    const ignore = this.reflector.getAllAndOverride<boolean>(
      IGNORE_TRANSFORM_INTERCEPTOR,
      [context.getHandler(), context.getClass()],
    );
    if (ignore) {
      return next.handle().pipe(map((data) => data));
    }

    return next.handle().pipe(
      map((data) => ({
        success: true,
        error: '',
        data: this.convertBigIntToString(data) ?? {},
        message: '',
      })),
    );
  }
}
