import { HttpException } from '@nestjs/common';

export function PrismaTransactionErrorHelper(error: any) {
  if (error instanceof HttpException) {
    throw error;
  }
  const tooManyRequestError = [
    {
      key: 'Unable to start a transaction in the given time',
      description: '트랜잭션을 시작할 수 없음',
    },
    {
      key: 'conflict or a deadlock',
      description: '트랜잭션이 오류로 종료됨',
    },
    {
      key: 'Timed out fetching a new connection from the connection pool',
      description: '커넥션 풀에서 커넥션을 가져오는데 실패함',
    },
    {
      key: 'timeout for this transaction was 5000',
      description: '5초동안 트랜잭션이 완료되지 않음',
    },
  ];
  if (tooManyRequestError.some((v) => String(error).includes(v.key))) {
    throw new HttpException(`Too Many Request: ${error}`, 429);
  } else {
    throw new HttpException(`Internal Server Error: ${error}`, 500);
  }
}
