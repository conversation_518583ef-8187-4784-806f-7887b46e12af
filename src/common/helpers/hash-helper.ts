import { createCipheriv, randomBytes } from 'crypto';
const algorithm = 'aes-256-cbc';

const key = randomBytes(32);

// 암호화 메서드
export const cipher = (hasKey: string, iv: string) => {
  const encrypt = createCipheriv(algorithm, key, iv);
  // 인코딩

  return encrypt.update(hasKey, 'utf8', 'base64') + encrypt.final('base64');
};

// 복호화 메서드
export const decipher = (hasKey: string, iv: string) => {
  const decode = createCipheriv(algorithm, key, iv);
  return decode.update(hasKey, 'utf8', 'base64') + decode.final('base64');
};
