// import { PaymentStatus } from '@common/types/payment.types';
// import { PaymentGoodOptionDto } from '@module/order/dto/create-order.dto';
// import { BadRequestException } from '@nestjs/common';

// export interface DeliveryData {
//   goodsOptionUuid: string[];
//   deliveryUserUuid: string;
//   price: number;
//   isSumUp: boolean;
//   createData: DefaultCreateData[];
// }

// export interface DefaultCreateData {
//   goodsOptionUuid: string;
//   goodsName: string;
//   goodsOptionName: string;
//   goodsOrderStatus: PaymentStatus;
//   goodsOptionPrice: number;
//   vatPrice: number;
//   goodsOptionTotalPrice: number;
//   goodsOptionCount: number;
// }

// export interface BoothCreateData extends DefaultCreateData {
//   boothUuid: string;
// }

// export interface PriceData {
//   totalPrice: number;
//   vat: number;
//   deliveryPrice: number;
//   deliveryList: DeliveryData[];
//   createData: BoothCreateData[];
// }

// export interface GoodsOption {
//   goodsOptionUuid: string;
//   name: string;
//   price: number;
//   salesCount: number;
//   maxCount: number | null;
//   goodsSellCount: {
//     goodsOptionBookingCount?: number;
//   } | null;
//   good: {
//     deliveryUserUuid: string | null;
//     isSumUp: boolean;
//     deliveryPrice: number;
//     name: string;
//   };
//   goodsOptionBoothPivot: {
//     boothUuid: string;
//   }[];
// }

// // 상품의 품절을 확인
// function validateGoodsAvailability(goodsOption: GoodsOption, count: number) {
//   const currentBookingCount =
//     (goodsOption.goodsSellCount?.goodsOptionBookingCount ?? 0) * 2;
//   if (goodsOption.salesCount < count + currentBookingCount) {
//     throw new BadRequestException(
//       `${goodsOption.name} 상품이 품절 되었습니다.`,
//     );
//   }
// }

// function handleDeliveryData(
//   pr: PriceData,
//   cu: PaymentGoodOptionDto,
//   goodsOption: GoodsOption,
//   totalPrice: number,
//   totalVatPrice: number,
// ) {
//   if (!goodsOption.good.deliveryUserUuid) {
//     throw new BadRequestException(
//       `${goodsOption.name} 상품은 배송으로 주문 불가능한 상품입니다.`,
//     );
//   }

//   const existingDeliveryData = pr.deliveryList.find(
//     (delivery) =>
//       delivery.deliveryUserUuid === goodsOption.good.deliveryUserUuid &&
//       goodsOption.good.isSumUp === delivery.isSumUp,
//   );

//   // TODO: 배송비도 부가세 붙나요?
//   if (existingDeliveryData) {
//     const deliveryPriceDifference =
//       goodsOption.good.deliveryPrice - existingDeliveryData.price;

//     existingDeliveryData.price = Math.max(
//       goodsOption.good.deliveryPrice,
//       existingDeliveryData.price,
//     );

//     if (deliveryPriceDifference > 0) {
//       pr.deliveryPrice += deliveryPriceDifference;
//     }
//     existingDeliveryData.goodsOptionUuid.push(goodsOption.goodsOptionUuid);
//     existingDeliveryData.createData.push({
//       goodsName: goodsOption.good.name,
//       goodsOptionUuid: goodsOption.goodsOptionUuid,
//       goodsOptionName: goodsOption.name,
//       goodsOrderStatus: PaymentStatus.ORDER_COMPLETE,
//       goodsOptionPrice: goodsOption.price,
//       vatPrice: totalVatPrice,
//       goodsOptionTotalPrice: totalPrice,
//       goodsOptionCount: cu.count,
//     });
//   } else {
//     pr.deliveryList.push({
//       deliveryUserUuid: goodsOption.good.deliveryUserUuid,
//       goodsOptionUuid: [cu.goodsOptionUuid],
//       isSumUp: goodsOption.good.isSumUp,
//       price: goodsOption.good.deliveryPrice,
//       createData: [
//         {
//           goodsName: goodsOption.good.name,
//           goodsOptionUuid: goodsOption.goodsOptionUuid,
//           goodsOptionName: goodsOption.name,
//           goodsOrderStatus: PaymentStatus.ORDER_COMPLETE,
//           goodsOptionPrice: goodsOption.price,
//           vatPrice: totalVatPrice,
//           goodsOptionTotalPrice: totalPrice,
//           goodsOptionCount: cu.count,
//         },
//       ],
//     });
//     pr.deliveryPrice += goodsOption.good.deliveryPrice;
//   }
// }

// function validateBoothAvailability(
//   goodsOption: GoodsOption,
//   boothUuid: string,
// ) {
//   const booth = goodsOption.goodsOptionBoothPivot.find(
//     (pivot) => pivot.boothUuid === boothUuid,
//   );
//   if (!booth) {
//     throw new BadRequestException('해당 상품은 부스에서 주문이 불가능합니다.');
//   }
// }

// export function orderGoodsCalculatePrice(
//   pr: PriceData,
//   cu: PaymentGoodOptionDto,
//   goodsOptions: GoodsOption[],
// ): PriceData {
//   // Main logic starts here
//   const goodsOption = goodsOptions.find(
//     (option) => option.goodsOptionUuid === cu.goodsOptionUuid,
//   );

//   if (!goodsOption) {
//     throw new BadRequestException('옵션을 다시 확인해주세요..!');
//   }

//   validateGoodsAvailability(goodsOption, cu.count);

//   const totalPrice = goodsOption.price * cu.count;
//   const totalVatPrice = totalPrice * 0.1;
//   pr.totalPrice += totalPrice;
//   pr.vat += totalVatPrice;

//   if (!cu.boothUuid) {
//     handleDeliveryData(pr, cu, goodsOption, totalPrice, totalVatPrice);
//   } else {
//     validateBoothAvailability(goodsOption, cu.boothUuid);
//     pr.createData.push({
//       boothUuid: cu.boothUuid ?? undefined,
//       goodsOptionUuid: goodsOption.goodsOptionUuid,
//       goodsName: goodsOption.good.name,
//       goodsOptionName: goodsOption.name,
//       goodsOrderStatus: PaymentStatus.ORDER_COMPLETE,
//       goodsOptionPrice: goodsOption.price,
//       vatPrice: totalVatPrice,
//       goodsOptionTotalPrice: totalPrice,
//       goodsOptionCount: cu.count,
//     });
//   }

//   return pr;
// }

// export function distributionRateCalculationHelper(
//   options: Array<{
//     goodsOptionUuid: string;
//     userId: string;
//     ratio: number;
//   }>,
//   findUserId: string,
//   basePrice: number,
// ) {
//   // 전체 배율의 합 계산
//   const totalRatio = options.reduce((sum, option) => sum + option.ratio, 0);

//   // 총 합산 가격 계산
//   const calculatedPrices = options.map((option) => {
//     const price = (basePrice * option.ratio) / totalRatio;

//     // 1의 자리를 올림 처리 (10의 자릿수로 올림 처리)
//     const roundedPrice = Math.ceil(price / 10) * 10;

//     // 올림 처리된 차이 계산
//     const roundedUp = roundedPrice - price; // 원래 가격과의 차이

//     return {
//       userId: option.userId,
//       calculatedPrice: roundedPrice,
//       // 원래 계산된 가격
//       originalPrice: price,
//       // 내 배분율
//       ratio: option.ratio,
//       // 올림 처리된 금액 차이
//       roundedUp,
//     };
//   });

//   // `userId`가 주어진 `uuid`와 일치하는 항목 반환
//   return calculatedPrices.find((item) => item.userId === findUserId);
// }
