export async function sendSlackMessage(ops: { title: string; text: string }) {
  // const isDev = !(
  //   process.env.NODE_ENV === 'staging' || process.env.NODE_ENV === 'production'
  // );
  // if (isDev) return;
  // const convertObjectToHtmlDisplay = (obj: string) =>
  //   JSON.stringify(obj, null, '&nbsp;').replace(/\\n/gi, '\n');
  // try {
  //   await axios({
  //     url: 'https://notifications.pillowstudio.kr/send',
  //     method: 'post',
  //     headers: {
  //       'Content-Type': 'application/json',
  //     },
  //     data: {
  //       title: `${process.env.NODE_ENV} - ${ops.title}`,
  //       text: convertObjectToHtmlDisplay(ops.text),
  //     },
  //   });
  // } catch (error) {
  //   console.error(error);
  // }
}
