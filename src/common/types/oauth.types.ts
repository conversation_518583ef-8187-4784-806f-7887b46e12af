export const enum ConfigKey {
  NewUrl = 'newUrl',
  Url = 'url',
  keyFile = 'AppleKey',
}

// https://developers.kakao.com/docs/latest/ko/kakaologin/rest-api#req-user-info-response-body 2024/04/30 기준데이터
export interface KaKaoAuthUserMeInfo {
  // 회원 번호 Long 타입
  id: number;
  // 서비스에 연결 완료된 시각, UTC*
  connected_at: Date;
  // 카카오 프로필을 동의한 경우 실시간 카카오계정 프로필 값 2024/05/12 기준으로 변경 가능성이 보입니다. 확인필요 https://developers.kakao.com/docs/latest/ko/kakaologin/prerequisite#user-properties
  properties?: {
    nickname?: string;
    profile_image?: string;
    thumbnail_image?: string;
  };
  // 자동 연결을 설정을 비활성화한 경우만 존재 false 연결 대기 true 연결 상태
  has_signed_up?: boolean;
  // 카카오싱크 간편 가입을 통해 로그인한 시각 UTC
  synched_at?: Date;
  for_partner?: {
    // 고유 ID 카카오톡 메세지 API 사용 권한이 있는 경우에만 제공
    uuid?: string;
  };
  kakao_account?: {
    // 사용자 동의시 프로필 정보 (닉네임/프로필 사진)
    profile_needs_agreement?: boolean;
    // 사용자 동의 시 닉네임 제공 가능
    profile_nickname_needs_agreement?: boolean;
    // 사용자 동의 시 프로필 사진 제공 가능
    profile_image_needs_agreement?: boolean;
    // 프로필 정보
    profile?: {
      // 닉네임
      nickname?: string;
      // 프로필 미리보기 이미지 URL 110px * 110px 또는 100px * 100px
      thumbnail_image_url?: string;
      // 프로필 사진 URL 640px * 640px 또는 480px * 480px
      profile_image_url?: string;
      // 프로필 사진 URL이 기본 프로필 사진 URL인지 여부 사용자가 등록한 프로필 사진이 없을 경우, 기본 프로필 사진 제공 true: 기본 프로필 사진 false: 사용자가 등록한 프로필 사진
      is_default_image?: boolean;
      // 닉네임이 기본 닉네임인지 여부 사용자가 등록한 닉네임이 운영정책에 부합하지 않는 경우, "닉네임을 등록해주세요"가 기본 닉네임으로 적용됨 true: 기본 닉네임 false: 사용자가 등록한 닉네임
      is_default_nickname?: boolean;
    };
    // 이메일을 가지고 있는지 여부 문서에 없음
    has_email?: boolean;
    // 성별을 가지고 있는지 여부 문서에 없음
    has_gender?: boolean;
    // 사용자 동의 시 카카오계정 이름 제공 가능
    name_needs_agreement?: boolean;
    // 카카오계정 이름
    name?: string;
    // 사용자 동의 시 카카오계정 대표 이메일 제공 가능
    email_needs_agreement?: boolean;
    // 이메일 유효 여부 true: 유효한 이메일 false: 이메일이 다른 카카오계정에 사용돼 만료
    is_email_valid?: boolean;
    // 이메일 인증 여부 true: 인증된 이메일 false: 인증되지 않은 이메일
    is_email_verified?: boolean;
    // 카카오계정 대표 이메일
    email?: string;
    // 사용자 동의 시 연령대 제공 가능
    age_range_needs_agreement?: boolean;
    // 연령대
    age_range?: string;
    // 사용자 동의 시 출생 연도 제공 가능
    birthyear_needs_agreement?: boolean;
    // 출생 연도(YYYY 형식)
    birthyear?: string;
    // 사용자 동의 시 생일 제공 가능
    birthday_needs_agreement?: boolean;
    // 생일(MMDD 형식)
    birthday?: string;
    // 생일 타입
    birthday_type?: string;
    // 사용자 동의 시 성별 제공 가능
    gender_needs_agreement?: boolean;
    // 성별 female: 여성 male: 남성
    gender?: string;
    // 사용자 동의 시 전화번호 제공 가능
    phone_number_needs_agreement?: boolean;
    // 카카오계정의 전화번호 국내 번호인 경우 +82 00-0000-0000 형식 해외 번호인 경우 자릿수, 붙임표(-) 유무나 위치가 다를 수 있음
    phone_number?: string;
    // 사용자 동의 시 CI 참고 가능
    ci_needs_agreement?: boolean;
    // 연계정보
    ci?: string;
    // CI 발급 시각, UTC*
    ci_authenticated_at?: Date;
  };
}

export interface NaverAuthUserMeInfo {
  resultcode: '00' | '024' | '028' | '403' | '404' | '500';
  message: 'success';
  response: {
    // 이메일
    email?: string;
    // 넥네임
    nickname?: string;
    // 프로필 이미지
    profile_image?: string;
    // '40-49'
    age?: string;
    // F 여성, M 남성, U 확인불가
    gender?: 'F' | 'M' | 'U';
    // 동일인 식별 코드
    id: string;
    // 이름
    name?: string;
    // 생일
    birthday?: string;
    // 출생년도
    birthyear?: string;
    // 고객 번호
    mobile?: string;
  };
}
