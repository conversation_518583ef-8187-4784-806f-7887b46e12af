import { PaypleCardVerEnum } from '@common/types/payment.types';

export class PaypleAuthenticationOutcome {
  // 인증 결과입니다. success : 성공 / error : 실패 / close : 구매자가 결제 완료 전 결제창 종료 success
  PCD_PAY_RST!: 'success' | 'error' | 'close';

  // 응답 코드입니다. 0000
  PCD_PAY_CODE!: string;

  // 응답 메시지입니다.  카드인증완료
  PCD_PAY_MSG!: string;

  //  결제수단(카드/계좌)입니다.  card
  PCD_PAY_TYPE!: string;

  //  카드 결제수단 중 앱카드, 정기(빌링), 비밀번호 간편결제 방식입니다.  02
  PCD_CARD_VER!: PaypleCardVerEnum;

  //  승인 요청 방식 결과입니다.  CERT
  PCD_PAY_WORK!: 'CERT';

  //  이후 승인 요청에 필요한 파트너 인증 키입니다.  K0VnW…
  PCD_AUTH_KEY!: string;

  //  이후 승인 요청에 필요한 결제 키입니다.  Vnx...
  PCD_PAY_REQKEY!: string;

  //  페이플 접속 주소입니다.  https://democpay.payple.kr
  PCD_PAY_HOST!: string;

  //  (현재는 이용하지 않는) 파트너 인증 결과입니다.
  PCD_PAY_URL?: string;

  //  승인 요청 Request URL 입니다.  https://democpay.payple.kr/php/PayCardConfirmAct.php?ACT_=PAYM
  PCD_PAY_COFURL!: string;

  //  정기(빌링), 비밀번호 간편결제 시 필요한 빌링키입니다. 앱카드 결제(PCD_CARD_VER = ‘02’)인 경우, 빌링키는 반환되지 않으며 빈값으로 응답합니다.
  PCD_PAYER_ID?: string;

  //  파트너(상점)에서 이용하는 회원번호입니다.  1234
  PCD_PAYER_NO!: string;

  //  구매자 이름입니다.  김이플
  PCD_PAYER_NAME!: string;

  //  구매자 휴대폰번호입니다.  ***********
  PCD_PAYER_HP!: string;

  //  구매자 이메일입니다.  <EMAIL>
  PCD_PAYER_EMAIL!: string;

  //  주문번호입니다. 파트너(상점)에서 미전송 시 페이플에서 발급한 주문번호를 응답합니다.  order12345
  PCD_PAY_OID!: string;

  //  상품명입니다.  테스트 상품
  PCD_PAY_GOODS!: string;

  //  총 결제금액입니다.  1000
  PCD_PAY_TOTAL!: string;

  //  복합과세 부가세입니다.  500
  PCD_PAY_TAXTOTAL!: string;

  //  과세 여부입니다.  Y
  PCD_PAY_ISTAX!: string;

  // 카드 결제일때 카드사명입니다
  PCD_PAY_CARDNAME?: string;

  // 카드 결제일때 카드번호입니다.
  PCD_PAY_CARDNUM?: string;

  // 카드 결제일때 할부 개월수입니다.
  PCD_PAY_CARDQUOTA?: string;

  //  해당 거래의 고유한 키입니다.
  PCD_PAY_CARDTRADENUM!: string;

  //  승인번호입니다.
  PCD_PAY_CARDAUTHNO!: string;

  //  매출 전표(영수증) 출력 URL 입니다.
  PCD_PAY_CARDRECEIPT!: string;

  //  결제를 요청한 시간입니다.  20231219134223
  PCD_PAY_TIME!: string;

  //  월 중복방지 거래 설정 여부입니다.  1000
  PCD_REGULER_FLAG!: string;

  //  월 중복방지 거래 - 년(Year)  2023
  PCD_PAY_YEAR!: string;

  //  월 중복방지 거래 - 월(Month)  12
  PCD_PAY_MONTH!: string;

  //  정기(빌링), 비밀번호 간편결제 시 필요한 설정값입니다.  N
  PCD_SIMPLE_FLAG!: string;

  //  결제 정보가 성공적으로 입력된 경우, 인증 결과가 POST 방식으로 전송됩니다. 경로지정 방식에 따라 결제창이 다르게 띄워집니다.  https://result-domain.com
  PCD_RST_URL!: string;

  //  (현재는 이용하지 않는) 총 결제금액입니다.
  PCD_PAY_AMOUNT!: string;

  //  (현재는 이용하지 않는) 할인금액입니다.
  PCD_PAY_DISCOUNT!: string;

  //  (현재는 이용하지 않는) 총 결제금액 - 할인금액입니다.
  PCD_PAY_AMOUNT_REAL!: string;

  //  파트너(상점)에서 입력한 값을 그대로 응답합니다. 단, 특수문자는 공백처리 합니다.  define1
  PCD_USER_DEFINE1!: string;

  //  파트너(상점)에서 입력한 값을 그대로 응답합니다. 단, 특수문자는 공백처리 합니다.  define2
  PCD_USER_DEFINE2!: string;

  // 은행 코드입니다. 020
  PCD_PAY_BANK?: string;

  // 은행명입니다. 우리은행
  PCD_PAY_BANKNAME?: string;

  // 구매자의 개인, 사업자 여부입니다. 개인 : personal / 사업자 : company  personal
  PCD_PAY_BANKACCTYPE?: string;

  // 계좌번호입니다. 123-********-456
  PCD_PAY_BANKNUM?: string;

  // 사용자 정의 파라미터입니다.  params1=val1&params2=val2
  PCD_LINK_ADD_PARAM?: string;
}

export class PayplePartnerRes {
  //	파트너 인증 서버의 Domain을 반환합니다.	democpay.payple.kr
  server_name!: string;
  //	응답 코드입니다. success : 성공 / error : 실패success
  result!: string;
  //	응답 메시지입니다.	사용자 인증 완료!!
  result_msg!: string;
  //	파트너 ID 입니다. 매 인증 시 변동되는 값입니다. UFVNNVZ…
  cst_id!: string;
  //	파트너 키입니다. 매 인증 시 변동되는 값입니다. T3JzRkp5L…
  custKey!: string;
  //	파트너 인증 키입니다. 매 인증 시 변동되는 값입니다. a688ccb3555...
  AuthKey!: string;
  //	페이플 접속 주소입니다.	https://democpay.payple.kr
  PCD_PAY_HOST!: string;
  //	파트너 인증 결과로 응답되는 URL Path 입니다.	/php/account/api/cPayCAct.php
  PCD_PAY_URL!: string;
  //	파트너 인증 결과로 응답되는 URL 입니다.	https://democpay.payple.kr/php/account/api/cPayCAct.php
  return_url!: string;
}

export interface PaypleCardRes {
  //	요청 결과입니다. success : 성공 / error : 실패 success
  PCD_PAY_RST: string;
  //	응답 코드입니다.	0000
  PCD_PAY_CODE: string;
  //	응답 메시지입니다.	회원조회 성공
  PCD_PAY_MSG: string;
  //	결제수단(카드/계좌)입니다.	card
  PCD_PAY_TYPE: string;
  //	빌링키입니다.	OVA3...
  PCD_PAYER_ID: string;
  //	구매자 이름입니다.	김이플
  PCD_PAYER_NAME: string;
  //	구매자 휴대폰번호입니다.	***********
  PCD_PAYER_HP: string;
  //	카드사 코드입니다.	0200
  PCD_PAY_CARD: string;
  //	카드사명입니다.	삼성카드
  PCD_PAY_CARDNAME: string;
  //	카드번호입니다.	1234-****-****-5678
  PCD_PAY_CARDNUM: string;
}

export interface PaypleTransferRes {
  //	요청 결과입니다. success : 성공 / error : 실패 success
  PCD_PAY_RST: string;
  //	응답 코드입니다.	0000
  PCD_PAY_CODE: string;
  //	응답 메시지입니다.	회원조회 성공
  PCD_PAY_MSG: string;
  //	결제수단(카드/계좌)입니다.	transfer
  PCD_PAY_TYPE: string;
  //	구매자의 개인, 사업자 여부입니다. 개인 / 사업자 개인
  PCD_PAY_BANKACCTYPE: string;
  //	빌링키입니다.	OVA3…
  PCD_PAYER_ID: string;
  //	구매자 이름입니다.	김이플
  PCD_PAYER_NAME: string;
  //	구매자 휴대폰번호입니다.	010-****-5678
  PCD_PAYER_HP: string;
  //	은행 코드입니다.	020
  PCD_PAY_BANK: string;
  //	은행명입니다.	우리은행
  PCD_PAY_BANKNAME: string;
  //	계좌번호입니다.	123-********-456
  PCD_PAY_BANKNUM: string;
}

/**
 * 계좌 인증 요청에 대한 페이플 응답 데이터 구조.
 */
export interface AccountVerificationRes {
  // 페이플 응답코드 (예: "A0000")
  result: string;
  // 응답 메시지 (예: "처리 성공")
  message: string;
  // 파트너 ID (예: "test")
  cst_id: string;
  // 파트너 하위 셀러의 ID (예: "sub01")
  sub_id: string;
  // 인증 완료된 계좌의 빌링키 (예: "cb9d695e-c034-4eeb-9550-c53a9ab7c2e4")
  billing_tran_id: string;
  // 금융기관으로부터 수신한 인증 완료 상세일시 (밀리세컨드) (예: "********175503547")
  api_tran_dtm: string;
  // 금융기관과 통신을 위해 필요한 고유 키 (예: "M202112389U152040289")
  bank_tran_id: string;
  // 금융기관으로부터 수신한 인증 완료 일자 (예: "********")
  bank_tran_date: string;
  // 금융기관 응답코드 (예: "000")
  bank_rsp_code: string;
  // 금융기관 코드 (예: "020")
  bank_code_std: string;
  // 금융기관 점별 코드 (예: "0201234")
  bank_code_sub: string;
  // 금융기관명 (예: "우리은행")
  bank_name: string;
  // 계좌번호 (예: "****************")
  account_num: string;
  // 예금주명 (예: "김이플")
  account_holder_name: string;
}

/**
 * 이체 요청 대기 상태에 대한 페이플 응답 데이터 구조.
 */
export interface TransferRequestWaitingForBillingKeyRes {
  // 페이플 응답코드 (예: "A0000")
  result: string;
  // 응답 메시지 (예: "처리 성공")
  message: string;
  // 파트너 ID (예: "test")
  cst_id: string;
  // 파트너 하위 셀러의 ID (예: "sub01")
  sub_id: string;
  // 중복 이체를 방지하기 위한 키 (예: "uo3h1708394911737306031340821")
  distinct_key: string;
  // 대기 요청을 수행한 여러 거래 건을 하나의 그룹으로 묶는 키 (예: "QlJKZDZrNVBzbW9Yc1UzTWNIdW05dz09")
  group_key: string;
  // 계좌의 빌링키 (예: "cb9d695e-c034-4eeb-9550-c53a9ab7c2e4")
  billing_tran_id: string;
  // 이체할 금액 (예: "1000")
  tran_amt: string;
  // 이체 가능 금액 (예: "999000")
  remain_amt: string;
  // 상대방 계좌 거래 내역에 표시될 최대 6자의 문구 (예: "테스트입니다")
  print_content: string;
  // 금융기관으로부터 수신한 계좌인증 완료 상세일시 (밀리세컨드) (예: "20231022152040289")
  api_tran_dtm: string;
}

/**
 * 이체 실행 요청에 대한 페이플 응답 데이터 구조.
 */
export interface TransferExecutionRes {
  /** 페이플 응답코드입니다. (예: "A0000") */
  result: string;

  /** 응답 메시지입니다. (예: "처리 성공") */
  message: string;

  /** 파트너 ID입니다. (예: "test") */
  cst_id: string;

  /** 이체 실행한 그룹키입니다. (예: "QlJKZDZrNVBzbW9Yc1UzTWNIdW05dz09") */
  group_key: string;

  /** 계좌의 빌링키입니다. (예: "cb9d695e-c034-4eeb-9550-c53a9ab7c2e4") */
  billing_tran_id: string;

  /** 이체 완료 금액입니다. (예: "1000") */
  tot_tran_amt: string;

  /** 이체 가능 금액입니다. (예: "999000") */
  remain_amt: string;

  /** 이체 실행을 위한 구분값입니다. (예: "NOW") */
  execute_type: string;

  /** 금융기관으로부터 수신한 이체 완료 상세일시입니다. (밀리세컨드 단위, 예: "20231022152040289") */
  api_tran_dtm: string;
}

export interface PartnerAuthenticationRes {
  /**
   * 응답 코드입니다.
   * @example "T0000"
   */
  result: string;

  /**
   * 응답 메시지입니다.
   * @example "처리 성공"
   */
  message: string;

  /**
   * 토큰 확인 코드입니다.
   * @example "as12345678"
   */
  code: string;

  /**
   * 발행된 Access 토큰입니다.
   * API 요청 시 Header의 Authorization 필드에 포함하여 전송해야 합니다.
   * @example "eyJhlNDlj..."
   */
  access_token: string;

  /**
   * Access 토큰 유형이며, 고정값입니다.
   * @example "Bearer"
   */
  token_type: string;

  /**
   * Access 토큰 만료 시간입니다.(초)
   * @example "60"
   */
  expires_in: string;
}

export const enum SettlementInformationTypeEnum {
  Individual = 1,
  Corporation = 6,
}

export enum PaypleBankCodes {
  KDB산업은행 = '002',
  IBK기업은행 = '003',
  KB국민은행 = '004',
  수협은행 = '007', // (수협중앙회)
  NH농협은행 = '011', // 단위농축협은 상호금융기관의 농협중앙회(단위농축협) 012 코드를 사용해주세요.
  우리은행 = '020',
  SC제일은행 = '023',
  한국씨티은행 = '027',
  대구은행 = '031',
  부산은행 = '032',
  광주은행 = '034',
  제주은행 = '035',
  전북은행 = '037',
  경남은행 = '039',
  하나은행 = '081',
  신한은행 = '088',
  케이뱅크 = '089',
  카카오뱅크 = '090',
  토스뱅크 = '092',
  수협중앙회 = '007',
  농협중앙회 = '012', // (단위농축협) 단위농축협이 아닌 경우, 은행의 NH농협은행 011 코드를 사용해주세요.
  새마을금고중앙회 = '045',
  신협중앙회 = '048',
  저축은행중앙회 = '050',
  산림조합중앙회 = '064',
  우정사업본부 = '071', // (우체국)
  KB증권 = '218',
  미래에셋증권 = '230',
  미래에셋대우 = '238',
  삼성증권 = '240',
  한국투자증권 = '243',
  NH투자증권 = '247',
  교보증권 = '261',
  하이투자증권 = '262',
  현대차증권 = '263',
  키움증권 = '264',
  이베스트투자증권 = '265',
  SK증권 = '266',
  대신증권 = '267',
  한화투자증권 = '269',
  토스증권 = '271',
  신한금융투자 = '278',
  DB금융투자 = '279',
  유진투자증권 = '280',
  메리츠증권 = '287',
  카카오페이증권 = '288',
}

/**
 * 페이플 결제 링크 생성 요청 인터페이스
 */
export interface PaypleCreateLinkRequest {
  /** 파트너 인증 후 수신한 ID */
  pcd_cst_id: string;
  /** 파트너 인증 후 수신한 키 */
  pcd_cust_key: string;
  /** 파트너 인증 후 수신한 인증 키 */
  pcd_auth_key: string;
  /** 결제링크 생성을 요청합니다. */
  pcd_pay_work: 'LINKREG';
  /**
   * 결제수단을 선택합니다.
   * 기본값(카드+계좌) : transfer+card
   * 카드: card
   * 계좌: transfer
   */
  pcd_pay_type: 'transfer+card' | 'card' | 'transfer';
  /** 상품명 */
  pcd_pay_goods: string;
  /** 총 결제금액 */
  pcd_pay_total: number;
  /** 상품 설명 */
  pcd_pay_goods_explain: string;
  /**
   * 과세 여부입니다. 기본값은 Y 이며, 유형별로 아래와 같이 설정해주세요.
   * 과세, 복합과세 : Y
   * 비과세 : N
   */
  pcd_pay_istax: 'Y' | 'N';
  /**
   * 복합과세 주문 시에만 이용하며, 복합과세 주문의 부가세를 설정합니다.
   * 예: 총 결제금액(PCD_PAY_TOTAL) 10,000원 중 복합과세 주문의 부가세가 500원이면 500으로 설정
   */
  pcd_pay_taxtotal?: number;
  /**
   * 결제 만료일시를 설정합니다. 미지정 시 무기한으로 만료일시가 설정됩니다.
   * 형식: YYYYMMDDHH (예: 2025010122)
   */
  pcd_link_expiredate?: string;
  /** 사용자 정의 파라미터 (예: params1=val1&params2=val2) */
  pcd_link_parameter?: string;
  /** 결제완료 화면에서 표시될 내용을 입력합니다. */
  pcd_link_noti_msg?: string;
  /** 상점 관리자에서 표시될 메모 */
  pcd_link_memo?: string;
  /** 결제 완료 화면에서 구매자가 '이동하기' 버튼을 클릭하면 이동할 URL */
  pcd_link_url?: string;
  /**
   * 결제 마감 수량을 지정합니다.
   * 1 ~ 999까지 가능
   */
  pcd_goods_amount?: number;
  /**
   * 카드 결제방식을 선택합니다. (PCD_PAY_TYPE 이 ‘card’ 또는 'transfer+card' 인 경우에만 해당)
   * 기본값(앱카드+빌링): 01+02
   * 01 : 빌링 - 정기결제가 아닌 일회성 키인 결제
   * 02 : 앱카드
   */
  pcd_card_ver?: '01+02' | '01' | '02';
  /**
   * 현금영수증 발행창 호출 여부입니다. (PCD_PAY_TYPE 이 ‘transfer’ 또는 'transfer+card' 인 경우에만 해당)
   * 호출 : Y
   * 미호출: 미입력
   */
  pcd_taxsave_flag?: 'Y';
}

/**
 * 페이플 결제 링크 생성 응답 인터페이스
 */
export interface PaypleCreateLinkResponse {
  /**
   * 생성 결과입니다.
   * success, error가 반환됩니다.
   */
  PCD_LINK_RST: 'success' | 'error';
  /** 응답 메시지 */
  PCD_LINK_MSG: string;
  /** 결제수단 */
  PCD_PAY_TYPE: 'transfer+card' | 'card' | 'transfer';
  /** 상품명 */
  PCD_PAY_GOODS: string;
  /** 상품 설명 */
  PCD_PAY_GOODS_EXPLAIN: string;
  /** 총 결제금액 */
  PCD_PAY_TOTAL: string;
  /** 과세 여부 */
  PCD_PAY_ISTAX: 'Y' | 'N';
  /** 복합과세 부가세 (값이 없을 수 있음) */
  PCD_PAY_TAXTOTAL?: string;
  /** 월 중복방지 거래 설정 여부 */
  PCD_REGULER_FLAG: 'Y' | 'N';
  /** 월 중복방지 거래 - 년(Year) (값이 없을 수 있음) */
  PCD_PAY_YEAR?: string;
  /** 월 중복방지 거래 - 월(Month) (값이 없을 수 있음) */
  PCD_PAY_MONTH?: string;
  /**
   * 현금영수증 발행 여부 (값이 없을 수 있음)
   * Y 또는 미입력(N)
   */
  PCD_TAXSAVE_FLAG?: 'Y';
  /**
   * 결제 만료일시 (형식: YYYY-MM-DD HH:mm:ss)
   */
  PCD_LINK_EXPIREDATE: string;
  /** 사용자 정의 파라미터 (값이 없을 수 있음) */
  PCD_LINK_ADD_PARAM?: string;
  /** 생성된 링크의 고유 키 */
  PCD_LINK_KEY: string;
  /** 생성된 링크의 전체 주소 */
  PCD_LINK_URL: string;
}
