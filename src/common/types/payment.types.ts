export enum PaymentCurrency {
  // 미국 달러
  USD = 'usd',
  // 유로
  EUR = 'eur',
  // 영국 파운드
  GBP = 'gbp',
  // 일본 엔
  JPY = 'jpy',
  // 한국 원
  KRW = 'krw',
  // 중국 위안
  CNY = 'cny',
  // 호주 달러
  AUD = 'aud',
  // 캐나다 달러
  CAD = 'cad',
  // 인도 루피
  INR = 'inr',
  // 브라질 레알
  BRL = 'brl',
  // 남아프리카 랜드
  ZAR = 'zar',
}

export enum PaymentMethodType {
  CARD = 'card',
  CARD_PWA = 'card_pwa',
  TRANSFER = 'transfer',
}

export enum PaypleCardVerEnum {
  // 정기(빌링) / 비밀번호 간편결제 : 01
  PASSWORD_BILLING = '01',
  // 앱카드 : 02
  CARD = '02',
}

export enum PaymentStatusEnum {
  // 주문 완료
  ORDER_COMPLETE = 'ORDER_COMPLETE',
  // 주문 취소 신청
  ORDER_CANCEL_REQUEST = 'ORDER_CANCEL_REQUEST',
  // 교환 신청
  EXCHANGE_REQUEST = 'EXCHANGE_REQUEST',
  // 주문 취소
  ORDER_CANCEL = 'ORDER_CANCEL',
  // 부분 주문 취소
  PARTIAL_ORDER_CANCEL = 'PARTIAL_ORDER_CANCEL',
  // 구매 확정
  PURCHASE_CONFIRMED = 'PURCHASE_CONFIRMED',
}

// 색상,사이즈변경: 0, 주문실수: 1, 파손 및 불량: 2, 오배송 및 지연 : 3
export enum RefundReasonEnum {
  CHANGE_COLOR_SIZE = 'CHANGE_COLOR_SIZE',
  ORDER_MISTAKES = 'ORDER_MISTAKES',
  DAMAGE_AND_DEFECTS = 'DAMAGE_AND_DEFECTS',
  MISDELIVERIES_AND_DELAYS = 'MISDELIVERIES_AND_DELAYS',
}

export enum RefundStatusEnum {
  REQUEST = 'REQUEST',
  COMPLETE = 'COMPLETE',
  REJECTED = 'REJECTED',
}

// TODO: 임시
export enum DeliveryStatusEnum {
  PREPARING = 'PREPARING', // 배송준비
  REQUESTED = 'REQUESTED', // 배송신청
  IN_TRANSIT = 'IN_TRANSIT', // 배송시작
  ON_THE_WAY = 'ON_THE_WAY', // 배송중
  DELIVERED = 'DELIVERED', // 배송완료
}

export enum DeliveryTypeEnum {
  DEFAULT = 'DEFAULT',
  REFUND = 'REFUND',
  EXCHANGE = 'EXCHANGE',
}

// 계좌번호 최대 길이
export const AccountMaxNum = 14;
