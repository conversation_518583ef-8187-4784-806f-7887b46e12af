export interface IAccessTokenPayload {
  sub: string; // userid
  r: UserRole; // role
  s: number; // sns platform id
}

export interface IRefreshTokenPayload {
  sub: string; // userid
  h: string; // hashed access token
  s: number; // sns platform id
}

export interface ILoginResponse {
  accessToken: string;
  accessTokenExpiresIn: string;
  refreshToken: string;
  refreshTokenExpiresIn: string;
}

export function isRefreshToken(object: any): object is IRefreshTokenPayload {
  return 'h' in object;
}

export interface RequestUser {
  id: string;
  role: UserRole;
  snsPlatformId: number;
}

export enum UserRole {
  USER = 'USER',
  SELLER = 'SELLER',
}
