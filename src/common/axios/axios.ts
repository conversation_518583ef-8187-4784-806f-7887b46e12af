import { HttpException, InternalServerErrorException } from '@nestjs/common';
import axios from 'axios';

export async function callAxios<T>(params: {
  baseUrl: string;
  url: string;
  method: 'get' | 'post' | 'delete' | 'patch';
  data?: object;
  query?: object;
}) {
  const res: T = await axios({
    method: params.method,
    url: `${params.baseUrl}${params.url}`,
    timeout: 5000,
    params: params.query,
    data: params.data,
    paramsSerializer: (params) => parseParams(params),
  })
    .then((response) => response.data)
    .catch((error) => {
      if (axios.isAxiosError(error)) {
        console.error(error.response?.data, 'Axios call Error');
        throw new HttpException(
          (error.response?.data as string) ??
            `${params.baseUrl} 정보를 가져오지 못했습니다.`,
          Number(error.response?.status ?? 500),
        );
      } else {
        console.error(error);
      }
      throw new InternalServerErrorException(
        `${params.baseUrl} 정보를 가져오지 못했습니다.`,
      );
    });
  return res;
}
export const parseParams = (params: object) => {
  let options = '';

  for (const [key, value] of Object.entries(params)) {
    if (Array.isArray(value)) {
      for (const element of value) {
        options += `${key}=${element}&`;
      }
    } else {
      options += `${key}=${value}&`;
    }
  }

  return options.slice(0, -1);
};
