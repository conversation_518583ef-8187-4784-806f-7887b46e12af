import { makeResponse } from '@common/types/response.types';
import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  Injectable,
} from '@nestjs/common';
import { isObject } from '@nestjs/common/utils/shared.utils';
import { ThrottlerException } from '@nestjs/throttler';
import { FastifyReply } from 'fastify';

@Injectable()
@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<FastifyReply>();
    const request = ctx.getRequest();

    console.error('error', {
      ...exception,
      path: request.url,
    });

    const res = exception.getResponse() as
      | string
      | {
          success?: boolean;
          message: string | string[];
          error?: string;
          data?: object;
        };
    // console.log('res', res);

    let msg;
    let error = '';
    let success = false;
    let data = {};

    if (exception instanceof ThrottlerException) {
      msg = '비정상적인 요청이 감지되었습니다. 잠시후 다시 시도하여 주세요.';
      error = 'ThrottlerException';
    } else if (isObject(res)) {
      msg = Array.isArray(res.message) ? res.message.pop() : res.message;
      error = typeof res.error === 'string' ? res.error : '';
      data = res.data ?? {};
      success = res.success ?? false;
    } else {
      msg = res;
    }

    const responseBody = makeResponse({
      success: success,
      error: error,
      message: msg,
      data: data,
    });

    return response.status(exception.getStatus()).send(responseBody);
  }
}
