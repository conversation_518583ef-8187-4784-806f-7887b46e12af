import { sendSlackMessage } from '@common/helpers/logging.helper';
import { makeResponse } from '@common/types/response.types';
import { AppConfigService } from '@config/app/config.service';
import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';

@Injectable()
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(
    private readonly httpAdapterHost: HttpAdapterHost,
    private readonly appConfig: AppConfigService,
  ) {}

  catch(exception: unknown, host: ArgumentsHost): void {
    let errorMsg;
    try {
      errorMsg = exception;
    } catch (e) {
      errorMsg = 'INTERNAL_SERVER_ERROR';
    }
    const errorString = (errorMsg as Error).toString();
    const requestType = host.getType<'rmq' | 'http'>();

    // Queue 오류는 따로 처리, switchToHttp 불가
    if (requestType === 'rmq') {
      // TODO: 큐 오류 처리
    } else if (requestType === 'http') {
      this.forHttpHost(host, exception, errorString);
    }
  }

  private forHttpHost(
    host: ArgumentsHost,
    exception: unknown,
    errorString: string,
  ) {
    const { httpAdapter } = this.httpAdapterHost;

    const ctx = host.switchToHttp();

    const httpStatus =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    const request = ctx.getRequest();
    Logger.error(
      process.env.NODE_ENV === 'development' ? exception : errorString,
      this.constructor.name,
    );
    sendSlackMessage({
      title: `${this.appConfig.appName} > ${this.appConfig.serviceName} - ${request.method} ${request.originalUrl} ${httpStatus}`,
      text: errorString,
    })
      .then()
      .catch();

    httpAdapter.reply(
      ctx.getResponse(),
      makeResponse({
        success: false,
        error: `${httpAdapter.getRequestUrl(request)}`,
        message:
          '서비스에 문제가 발생하였어요. 오류가 지속되면 관리자에게 문의해 주세요.',
        data: {},
      }),
      httpStatus,
    );
  }
}
