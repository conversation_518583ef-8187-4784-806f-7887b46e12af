import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import dayjs from 'dayjs';

@ValidatorConstraint({ async: true })
export class CustomDateFormat implements ValidatorConstraintInterface {
  validate(text: string) {
    const regex = /(^(\d{4})[./-](\d{1,2})[./-](\d{1,2})$)/gi;

    if (typeof text !== 'string') {
      return false;
    }
    const dateArray = text.split(/[./-]/);

    if (
      !(
        regex.test(text) &&
        +dateArray[1] <= 12 &&
        +dateArray[1] >= 1 &&
        +dateArray[2] <= 31 &&
        +dateArray[2] >= 1
      )
    ) {
      return false;
    }
    const year = +dateArray[0];
    const month = +dateArray[1];
    const date = +dateArray[2];

    // dayjs
    const daysInMonth = dayjs(`${year}-${month}`).daysInMonth();
    if (date < 1 || date > daysInMonth) {
      return false;
    }

    return dayjs(text).isValid();
  }
  defaultMessage(args: ValidationArguments) {
    return `${args.property} 날짜 형식은 YYYY-MM-DD | YYYY/MM/DD | YYYY.MM.DD 입니다.`;
  }
}

export function IsDateFormat(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: CustomDateFormat,
    });
  };
}
