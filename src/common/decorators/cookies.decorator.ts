import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const CookiesCsrfToken = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const cookies = request.cookies || {};
    // TODO: CSRF 토큰을 쿠키 이름 확인필요
    // production 우선
    const prodCookie = cookies['__Secure-sherry-csrfToken-production'];
    if (prodCookie) {
      return prodCookie;
    }

    const devCookie = cookies['__Secure-sherry-csrfToken-development'];
    if (devCookie) {
      return devCookie;
    }

    const localCookie = cookies['sherry-csrfToken-development'];
    if (localCookie) {
      return localCookie;
    }

    return undefined;
  },
);
