import { Transform } from 'class-transformer';

export default function TransformDate({
  required = true,
  nullable = false,
  forceUndefined = false,
} = {}): PropertyDecorator {
  const toPlain = Transform(
    ({ value }: { value: Date }) => value.toISOString(),
    { toPlainOnly: true },
  );

  const toClass = Transform(
    ({ value }: { value: number | number[] }) => {
      if (!required && value === undefined) {
        return undefined;
      }
      if (nullable && value === null) {
        return forceUndefined ? undefined : null;
      }
      if (value instanceof Array) {
        return value.map((v) => new Date(v));
      }
      return new Date(value);
    },
    { toClassOnly: true },
  );

  return function (target: number | Date, key: string) {
    toPlain(target, key);
    toClass(target, key);
  } as any;
}
