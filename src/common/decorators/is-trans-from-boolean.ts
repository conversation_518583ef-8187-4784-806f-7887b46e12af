import { BadRequestException } from '@nestjs/common';
import { Transform } from 'class-transformer';

export function IsTransFromStringToBooleanType(): (
  target: any,
  key: string,
) => void {
  return Transform((value: any) => {
    if (
      value.value === '1' ||
      value.value === 1 ||
      value.value === 'true' ||
      value.value === true
    ) {
      return true;
    } else if (
      value.value === '0' ||
      value.value === 0 ||
      value.value === 'false' ||
      value.value === false
    ) {
      return false;
    } else {
      throw new BadRequestException(`${value.key} must be a boolean value`);
    }
  });
}
