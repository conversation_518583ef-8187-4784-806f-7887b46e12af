import {
  isInt,
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

@ValidatorConstraint({ async: true })
export class CustomTwoDimensionalArrayFormat
  implements ValidatorConstraintInterface
{
  validate(text: unknown) {
    if (!Array.isArray(text)) {
      return false;
    }
    for (const elem of text) {
      if (!Array.isArray(elem)) {
        return false;
      }
      elem.forEach((e) => {
        if (!isInt(e)) {
          return false;
        }
      });
    }
    return true;
  }
  defaultMessage(args: ValidationArguments) {
    return `${args.property} 2차월 배열을 넣어주세요.`;
  }
}

export function IsTwoDimensionalArrayFormat(
  validationOptions?: ValidationOptions,
) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      propertyName,
      target: object.constructor,
      options: validationOptions,
      constraints: [],
      validator: CustomTwoDimensionalArrayFormat,
    });
  };
}
