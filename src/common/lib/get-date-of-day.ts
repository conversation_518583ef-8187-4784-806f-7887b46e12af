import { ONE_HOUR_TO_MS } from './const';

export function getStartDateOfDay(date: Date, timeDiff: number = 0): Date {
  const newDate = new Date(date.getTime() + timeDiff * ONE_HOUR_TO_MS);
  newDate.setUTCHours(-timeDiff, 0, 0, 0);
  return newDate;
}

export function getEndDateOfDay(date: Date, timeDiff: number = 0): Date {
  const newDate = new Date(date.getTime() + timeDiff * ONE_HOUR_TO_MS);
  newDate.setUTCHours(23 - timeDiff, 59, 59, 999);
  return newDate;
}
