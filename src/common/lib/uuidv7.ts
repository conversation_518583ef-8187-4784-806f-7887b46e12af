import { v7 } from 'uuid';
import { getEndDateOfDay, getStartDateOfDay } from './get-date-of-day';

export const uuidv7 = (date: Date = new Date()) =>
  v7({
    msecs: date.getTime(),
  });

export const startUuidv7 = (date: Date = new Date()) =>
  v7({
    msecs: getStartDateOfDay(date).getTime(),
    random: new Uint8Array(16),
  }).replace('7000-8000', '7000-0000');

export const endUuidv7 = (date: Date = new Date()) =>
  v7({
    msecs: getEndDateOfDay(date).getTime(),
    random: new Uint8Array(16).fill(255),
    seq: 0xffffffff,
  }).replace('7fff-bfff', '7fff-ffff');

export const uuidv7ToDate = (uuid: string) =>
  new Date(parseInt(uuid.slice(0, 13).replace('-', ''), 16));
