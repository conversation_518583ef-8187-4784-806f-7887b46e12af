import {
  BadRequestException,
  PipeTransform,
  Injectable,
  ArgumentMetadata,
} from '@nestjs/common';

@Injectable()
export class OptionalParseIntPipe implements PipeTransform<string> {
  async transform(
    value: string | null | undefined,
    metadata: ArgumentMetadata,
  ) {
    if (!value || value.length === 0) {
      return undefined;
    }
    const val1 = Number(value);
    if (isNaN(val1)) {
      throw new BadRequestException(
        `Validation failed. ${metadata.data} is Not Int`,
      );
    }
    const val = parseInt(value, 10);
    if (isNaN(val)) {
      throw new BadRequestException(
        `Validation failed. ${metadata.data} is Not Int`,
      );
    }

    if (val1 - val !== 0) {
      throw new BadRequestException(
        `Validation failed. ${metadata.data} is Not Int`,
      );
    }
    return val;
  }
}
