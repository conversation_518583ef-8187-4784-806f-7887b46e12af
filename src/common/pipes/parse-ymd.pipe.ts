import {
  ArgumentMetadata,
  BadRequestException,
  Injectable,
  PipeTransform,
} from '@nestjs/common';

@Injectable()
export class ParseYmdPipe implements PipeTransform<string> {
  async transform(value: any, metadata: ArgumentMetadata) {
    if (!(typeof value === 'string') || !value) {
      return undefined;
    }

    value = value.trim();
    if (value === '') {
      return undefined;
    }

    const regEx = /^\d{4}-\d{2}-\d{2}$/;
    if (!value.match(regEx)) {
      //
      throw new BadRequestException(`Invalid format ${metadata.data}`);
    }
    const d = new Date(value);
    const dNum = d.getTime();
    if (!dNum && dNum !== 0) {
      throw new BadRequestException();
    }
    if (d.toISOString().slice(0, 10) !== value) {
      throw new BadRequestException(`Invalid date ${metadata.data}`);
    }

    return value;
  }
}
