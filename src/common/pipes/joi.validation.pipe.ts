import {
  ArgumentMetadata,
  BadRequestException,
  Injectable,
  PipeTransform,
} from '@nestjs/common';
import { ObjectSchema, AlternativesSchema } from 'joi';

@Injectable()
export class JoiValidationPipe implements PipeTransform {
  constructor(private schema: ObjectSchema | AlternativesSchema) {}

  transform(v: any, metadata: ArgumentMetadata) {
    const { error, value } = this.schema.validate(v);
    if (error) {
      // FIXME: 주입 안됨
      console.error(error);
      const message = error.details[0].message;
      throw new BadRequestException(message);
    }
    return value;
  }
}
