import { ignoreTransformInterceptor } from '@common/decorators/ignore-transform-interceptor.decorator';
import { User } from '@common/decorators/jwk.decorators';
import { IsPublic } from '@common/decorators/public.decorator';
import type { RequestUser } from '@common/types/token.types';
import { Body, Controller, Get, Header, Post, Query } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { readFileSync } from 'fs';
import { TicketCreateDto, TicketDto } from './dto/ticket.create.dto';
import { TicketPgDataDto } from './dto/ticket.payment.dto';
import { TicketService } from './ticket.service';

@ApiTags('Ticket')
@ApiBearerAuth()
@Controller('tickets')
export class TicketController {
  constructor(private readonly ticketService: TicketService) {}

  @ApiOperation({
    summary: 'tickets Product List',
    description: '',
  })
  @Get('products')
  getProductTickets() {
    return this.ticketService.getProductTickets();
  }

  @ApiOperation({
    summary: 'Get my tickets',
    description:
      '`(Available Ticket count) = (Total usableDay) - (Total usedDay)`',
  })
  @ApiOkResponse({ type: [TicketDto] })
  @Get()
  getTicketsByUserUuid(@User() user: RequestUser) {
    return this.ticketService.getTicketsByUserUuid(user.id);
  }

  @ApiOperation({ summary: 'Buy ticket' })
  @Post()
  async buyTicket(@User() user: RequestUser, @Body() dto: TicketCreateDto) {
    return this.ticketService.buyTicketPrepare(user.id, dto);
  }

  @ApiOperation({ summary: 'Buy ticket' })
  @Get('payment')
  @ignoreTransformInterceptor()
  @Header('Content-Type', 'text/html')
  @IsPublic()
  async buyTicketGet(@Query() dto: TicketPgDataDto) {
    const pgData = await this.ticketService.getTicketPreparePgData(dto);

    if (!pgData) {
      return '0 원 에러 처리해야됩니다.';
    }
    const renderingPage = readFileSync(
      'src/module/payple/view/order.html',
      'utf8',
    );
    // 여기 리팩토링 필요보임 일단 되네
    // Object.entries(pgData).forEach(([key, value]) => {
    //   console.log(`{{${key}}}`);

    //   renderingPage.replace(key, `${value ?? ''}`);
    // });
    // if (pgData.isTest) {
    //   return readFileSync('src/module/payple/view/success.html', 'utf8');
    // }
    const prodMode = 'cpay';
    // if (isTest === 'test') {
    //   prodMode = 'democpay';
    // }
    return renderingPage
      .replace('{{clientKey}}', pgData.clientKey)
      .replace('{{PCD_PAY_TYPE}}', pgData.PCD_PAY_TYPE)
      .replace('{{PCD_PAY_WORK}}', pgData.PCD_PAY_WORK)
      .replace('{{PCD_CARD_VER}}', pgData.PCD_CARD_VER ?? '')
      .replace('{{PCD_PAY_GOODS}}', pgData.PCD_PAY_GOODS ?? '')
      .replace('{{PCD_PAY_TOTAL}}', pgData.PCD_PAY_TOTAL.toString())
      .replace('{{PCD_RST_URL}}', pgData.PCD_RST_URL)
      .replace('{{PCD_PAY_OID}}', pgData.PCD_PAY_OID)
      .replace('{{PCD_PAYER_NAME}}', pgData.PCD_PAYER_NAME ?? '')
      .replace('{{PCD_PAYER_HP}}', pgData.PCD_PAYER_HP ?? '')
      .replace('{{PCD_PAYER_EMAIL}}', pgData.PCD_PAYER_EMAIL ?? '')
      .replace(
        '{{PCD_PAY_TAXTOTAL}}',
        pgData.PCD_PAY_TAXTOTAL?.toString() ?? '',
      )
      .replace('{{PCD_SIMPLE_FLAG}}', pgData.PCD_SIMPLE_FLAG ?? '')
      .replace('{{PCD_PAYER_AUTHTYPE}}', pgData.PCD_PAYER_AUTHTYPE ?? '')
      .replace('{{PCD_PAYER_ID}}', pgData.PCD_PAYER_ID ?? '')
      .replace('{{MODE}}', prodMode);
  }
}
