import { ApiProperty, PickType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsUUID,
} from 'class-validator';

export class TicketDto {
  @ApiProperty({
    description: '티켓 UUID',
    example: '01968565-e708-7373-9b2f-b3265b8afb5d',
  })
  @IsUUID('7', { message: 'ticketUuid는 유효한 UUID v7 형식이어야 합니다.' })
  @IsNotEmpty({ message: 'ticketUuid는 필수 입력 항목입니다.' })
  ticketUuid!: string;

  @ApiProperty({
    description: '유저의 UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID('7', { message: 'userUuid는 유효한 UUID v7 형식이어야 합니다.' })
  @IsNotEmpty({ message: 'userUuid는 필수 입력 항목입니다.' })
  userUuid!: string;

  @ApiProperty({
    description: '사용하기로 지정한 기간 (ISO 8601 날짜/시간 문자열 배열)',
    example: ['2025-05-07T10:00:00.000Z', '2025-05-08T10:00:00.000Z'],
    type: [String],
    format: 'date-time', // Specifies the format for the items in the array
  })
  @IsArray({ message: 'usedDates는 배열이어야 합니다.' })
  @IsDateString(
    {},
    {
      each: true,
      message:
        'usedDates의 각 항목은 유효한 ISO 8601 형식의 날짜 문자열이어야 합니다.',
    },
  )
  @IsNotEmpty({ message: 'usedDates는 필수 입력 항목입니다.' }) // Assuming the array itself must be provided, can be empty
  usedDates!: string[];

  @ApiProperty({
    description: '이 이용권으로 사용 가능한 일 수 (1, 3, 7 중 하나)',
    example: 3,
  })
  @IsInt({ message: 'usableDay는 정수여야 합니다.' })
  @IsNotEmpty({ message: 'usableDay는 필수 입력 항목입니다.' })
  usableDay!: 1 | 3 | 7;

  @ApiProperty({
    description: '이용권 유통기한 (ISO 8601 날짜/시간 문자열)',
    example: '2030-05-07T23:59:59.999Z',
    type: String,
    format: 'date-time',
  })
  @IsDateString(
    {},
    { message: 'expiredAt은 유효한 ISO 8601 형식의 날짜 문자열이어야 합니다.' },
  )
  @IsNotEmpty({ message: 'expiredAt은 필수 입력 항목입니다.' })
  expiredAt!: string;
}

export class TicketCreateDto extends PickType(TicketDto, [
  'usableDay',
] as const) {
  @ApiProperty({
    description: '테스트용',
    example: 'test',
  })
  @Type(() => Boolean)
  @IsBoolean({ message: 'isTest는 boolean이어야 합니다.' })
  @IsOptional()
  isTest?: boolean;
}
