import { InfinityScroll } from '@common/dto/infinity-scroll';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class GetEventsDto extends InfinityScroll<string> {
  @ApiPropertyOptional({
    description: 'event uuid',
    type: String,
  })
  @IsString()
  @IsOptional()
  cursor: string | undefined;

  @ApiPropertyOptional({
    description: 'event name',
  })
  @IsString()
  @IsOptional()
  name?: string;
}
