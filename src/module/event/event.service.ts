import { AppConfigService } from '@config/app/config.service';
import { Injectable } from '@nestjs/common';
import { PrismaService } from '@provider/database/prisma.service';
import * as nodemailer from 'nodemailer';
import Mail from 'nodemailer/lib/mailer';
import { GetEventsDto } from './dto/event.dto';
import { EventRequestDto } from './event.request-dto';

@Injectable()
export class EventService {
  constructor(
    private readonly appConfigService: AppConfigService,
    private readonly prismaService: PrismaService,
  ) {}
  async getEvents(dto: GetEventsDto) {
    const whereCondition = {
      ...(dto.name && { name: { contains: dto.name } }),
    };
    return dto.createResponse(
      await this.prismaService.event.findMany({
        where: whereCondition,
        ...dto.toPrismaPaging('eventUuid', dto.getOrder),
      }),
      'eventUuid',
    );
  }

  async eventRequest(dto: EventRequestDto) {
    const transporter = nodemailer.createTransport({
      host: this.appConfigService.mailHost,
      port: this.appConfigService.mailPort,
      // secure: true,
      auth: {
        user: this.appConfigService.mailUser,
        pass: this.appConfigService.mailPass,
      },
    });

    // EventRequestDto를 기반으로 HTML 내용 생성
    const html = this.generateEmailHtml(dto);

    const mailOptions: Mail.Options = {
      from: this.appConfigService.mailUser,
      to: '<EMAIL>',
      subject: '[행사신청] ' + dto.name,
      html,
    };

    await transporter.sendMail(mailOptions);
  }

  /**
   * EventRequestDto 데이터를 기반으로 HTML 이메일 내용을 생성합니다.
   * @param dto 행사 신청 정보
   * @returns HTML 형식의 이메일 내용
   */
  private generateEmailHtml(dto: EventRequestDto): string {
    const formatDate = (date: Date) =>
      date.toLocaleDateString('ko-KR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long',
      });

    // 날짜 정보 포맷팅
    const dateRangeText =
      dto.dates.length === 1
        ? formatDate(dto.dates[0])
        : `${formatDate(dto.dates[0])} ~ ${formatDate(dto.dates[dto.dates.length - 1])}`;

    // 행사 이미지 (있는 경우에만 표시)
    const imageHtml = dto.image
      ? `<div style="margin: 20px 0;">
          <img src="${dto.image}" alt="행사 이미지" style="max-width: 100%; max-height: 300px; border-radius: 8px;">
        </div>`
      : '';

    // 행사 위치 정보
    const addressHtml = dto.roadAddress
      ? `<tr>
          <td style="padding: 10px; font-weight: bold; width: 120px; vertical-align: top;">행사 위치:</td>
          <td style="padding: 10px;">${dto.roadAddress}${
            dto.detailAddress ? ' ' + dto.detailAddress : ''
          }</td>
        </tr>`
      : '';

    // 행사 홈페이지 정보
    const landingUrlHtml = dto.landingUrl
      ? `<tr>
          <td style="padding: 10px; font-weight: bold; width: 120px;">홈페이지:</td>
          <td style="padding: 10px;"><a href="${dto.landingUrl}" style="color: #4a86e8;">${dto.landingUrl}</a></td>
        </tr>`
      : '';

    // 연락처 정보
    const contactHtml =
      dto.howTo && dto.value
        ? `<tr>
          <td style="padding: 10px; font-weight: bold; width: 120px;">연락처 정보:</td>
          <td style="padding: 10px;">${dto.howTo}: ${dto.value}</td>
        </tr>`
        : '';

    // HTML 템플릿 조합
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>행사 신청 정보</title>
    </head>
    <body style="font-family: 'Apple SD Gothic Neo', 'Malgun Gothic', '맑은 고딕', sans-serif; max-width: 650px; margin: 0 auto; padding: 20px; color: #333333;">
      <div style="background-color: #f8f9fa; border-radius: 10px; padding: 25px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
        <h1 style="color: #3c78d8; text-align: center; margin-bottom: 25px;">행사 신청이 접수되었습니다</h1>
        
        ${imageHtml}
        
        <h2 style="color: #333; margin-top: 25px; margin-bottom: 15px; border-bottom: 1px solid #e0e0e0; padding-bottom: 10px;">
          ${dto.name}
        </h2>
        
        <table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
          <tr>
            <td style="padding: 10px; font-weight: bold; width: 120px;">행사 일정:</td>
            <td style="padding: 10px;">${dateRangeText}</td>
          </tr>
          ${addressHtml}
          ${landingUrlHtml}
          ${contactHtml}
        </table>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; text-align: center; color: #666666; font-size: 14px;">
          <p>이 이메일은 자동 발송되었습니다.</p>
          <p>문의사항이 있으시면 도도해 주세요.</p>
        </div>
      </div>
    </body>
    </html>
    `;
  }
}
