import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsDate,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';

export class EventRequestDto {
  // 행사 이름, 행사 홈페이지 주소, 행사기간(date[]), 행사 현수막 url, howto, value
  @ApiProperty({
    description: '행사 이름',
  })
  @IsNotEmpty()
  @IsString()
  name!: string;

  @ApiProperty({
    description: '행사 홈페이지 주소',
  })
  @IsOptional()
  @IsString()
  landingUrl?: string;

  // 행사장 주소
  @ApiProperty({
    description: '행사장 주소',
  })
  @IsOptional()
  @IsString()
  roadAddress?: string;

  @ApiProperty({
    description: '행사장 상세주소',
  })
  @IsOptional()
  @IsString()
  detailAddress?: string;

  @ApiProperty({
    description: '행사 기간',
  })
  @IsNotEmpty()
  @IsDate({ each: true })
  @Type(() => Date)
  dates!: Date[];

  @ApiProperty({
    description: '행사 현수막 등록 url',
  })
  @IsOptional()
  @IsString()
  image?: string;

  @ApiProperty({
    description: '어디로 알려줄까?',
  })
  @IsOptional()
  @IsString()
  howTo?: string;

  @ApiProperty({
    description: '여기로 알려줘 이메일, 핸들 etc...',
  })
  @ValidateIf((o) => o.howTo)
  @IsNotEmpty()
  @IsString()
  value?: string;
}
