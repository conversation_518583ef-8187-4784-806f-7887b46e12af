import { IsPublic } from '@common/decorators/public.decorator';
import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { GetEventsDto } from './dto/event.dto';
import { EventRequestDto } from './event.request-dto';
import { EventService } from './event.service';

@Controller('event')
export class EventController {
  constructor(private readonly eventService: EventService) {}

  @IsPublic()
  @Get()
  async getEvents(@Query() dto: GetEventsDto) {
    return this.eventService.getEvents(dto);
  }

  @IsPublic()
  @Post('request')
  async eventRequest(@Body() dto: EventRequestDto) {
    return this.eventService.eventRequest(dto);
  }
}
