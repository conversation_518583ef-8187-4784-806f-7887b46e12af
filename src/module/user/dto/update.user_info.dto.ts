import { ApiProperty, IntersectionType, PartialType } from '@nestjs/swagger';
import { IsAlphanumeric, IsOptional, IsString } from 'class-validator';
import { BankAccountSettlementUpsertDto } from '../_/bank-account/dto/bank-account-settlement-upsert.dto';
import { KakaopayInfoUpsertDto } from '../_/kakaopay-info/dto/kakaopay-info.upsert.dto';

export class UpdateUserInfoDto extends IntersectionType(
  PartialType(BankAccountSettlementUpsertDto),
  PartialType(KakaopayInfoUpsertDto),
) {
  @ApiProperty({
    description: 'nickname to edit (if undefined, no update)',
    example: 'user123',
    required: false,
  })
  @IsString()
  @IsOptional()
  nickname?: string;

  @ApiProperty({
    description: 'profile image url to edit (if undefined, no update)',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  profileImg?: string;

  @ApiProperty({
    description:
      '[NOT USE, FOR FUTURE] introduction text to edit (if undefined, no update)',
    example: 'user123',
    required: false,
    deprecated: true,
  })
  @IsString()
  @IsOptional()
  explainText?: string;

  @ApiProperty({
    description: '[NOT USE] handle to edit (if undefined, no update)',
    example: 'user123',
    required: false,
    deprecated: true,
  })
  @IsString()
  @IsAlphanumeric()
  @IsOptional()
  handle?: string;

  @ApiProperty({
    description: 'email to edit (if undefined, no update)',
    example: '<EMAIL>',
    required: false,
  })
  @IsString()
  @IsOptional()
  email?: string;

  @ApiProperty({
    description: 'Twitter handle (ID)',
    example: 'user123',
    required: false,
  })
  @IsString()
  @IsOptional()
  twitterHandle?: string;

  @ApiProperty({
    description: 'Instagram handle (ID)',
    example: 'user123',
    required: false,
  })
  @IsString()
  @IsOptional()
  instagramHandle?: string;
}
