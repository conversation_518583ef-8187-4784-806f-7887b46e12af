import { InfinityScroll } from '@common/dto/infinity-scroll';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class FindUserGoodsDto extends InfinityScroll<string> {
  @ApiProperty({
    description: '유저 핸들',
    example: '00000000-0000-0000-0000-000000000000',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  userHandle!: string;

  @ApiProperty({
    description: 'goodsUuid',
    type: String,
    required: false,
  })
  @IsUUID()
  @IsOptional()
  cursor: string | undefined;
}
