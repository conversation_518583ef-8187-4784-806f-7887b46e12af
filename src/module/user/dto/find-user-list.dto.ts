import { InfinityScroll } from '@common/dto/infinity-scroll';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';

export class FindUserListDto extends InfinityScroll<string> {
  @ApiProperty({
    description: 'handle',
    type: String,
    required: false,
  })
  @IsString()
  @IsOptional()
  cursor: string | undefined;

  @ApiProperty({
    description: 'boothUuid',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  boothUuid?: string;

  @ApiProperty({
    description: 'goodsUuid',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  goodsUuid?: string;
}
