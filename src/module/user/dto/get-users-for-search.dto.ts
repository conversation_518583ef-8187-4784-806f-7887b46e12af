import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class GetUsersForSearchDto {
  @ApiProperty({
    description: 'User nickname to search for',
    required: false,
    example: 'user123',
  })
  @IsString()
  @IsOptional()
  nickname?: string;

  @ApiProperty({
    description: 'User handle to search for',
    required: false,
    example: 'handle123',
  })
  @IsString()
  @IsOptional()
  handle?: string;
}
