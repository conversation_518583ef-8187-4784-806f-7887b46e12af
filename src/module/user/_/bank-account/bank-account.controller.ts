import { User } from '@common/decorators/jwk.decorators';
import type { RequestUser } from '@common/types/token.types';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { BankAccountService } from './bank-account.service';
import { BankAccountSettlementUpsertDto } from './dto/bank-account-settlement-upsert.dto';
import {
  BankAccountCreateDto,
  BankAccountDto,
} from './dto/bank-account.create.dto';
import { BankAccountUpdateDto } from './dto/bank-account.update.dto';
import {
  BankAccountVerifyDto,
  BankAccountVerifyResDto,
} from './dto/bank-account.verify.dto';

@ApiTags('Bank Account')
@ApiBearerAuth()
@Controller('bank-accounts')
export class BankAccountController {
  constructor(private readonly bankAccountService: BankAccountService) {}

  @Get('settlement')
  @ApiOperation({
    summary: '정산 계좌 조회',
    description: '사용자의 정산 계좌 정보를 조회합니다.',
  })
  @ApiResponse({
    status: 200,
    description: '정산 계좌 조회 성공',
    type: BankAccountDto,
  })
  async getSettlementBankAccount(
    @User() user: RequestUser,
  ): Promise<BankAccountDto> {
    return this.bankAccountService.getSettlementBankAccount(user.id);
  }

  @Post()
  @ApiOperation({
    summary: '은행 계좌 생성',
    description: '사용자의 은행 계좌를 생성합니다.',
  })
  @ApiResponse({
    status: 201,
    description: '은행 계좌 생성 성공',
    type: BankAccountDto,
  })
  async createBankAccount(
    @User() user: RequestUser,
    @Body() dto: BankAccountCreateDto,
  ): Promise<BankAccountDto> {
    console.log('user', user);
    return this.bankAccountService.createBankAccount(user.id, dto);
  }

  @Patch(':bankAccountUuid')
  @ApiOperation({
    summary: '은행 계좌 수정',
    description: '사용자의 특정 은행 계좌 정보를 수정합니다.',
  })
  @ApiResponse({
    status: 200,
    description: '은행 계좌 수정 성공',
    type: BankAccountDto,
  })
  async updateBankAccount(
    @User() user: RequestUser,
    @Param('bankAccountUuid') bankAccountUuid: string,
    @Body() dto: BankAccountUpdateDto,
  ): Promise<BankAccountDto> {
    return this.bankAccountService.updateBankAccount(
      user.id,
      bankAccountUuid,
      dto,
    );
  }

  @Put('settlement')
  @ApiOperation({
    summary: '정산 계좌 등록/수정',
    description:
      '사용자의 정산 계좌를 등록하거나 수정합니다. 기존에 정산 계좌가 있었다면 해당 계좌는 일반 계좌로 변경됩니다. 이 기능은 임시 기능으로, 추후 Sherry 에 결제수단을 등록하는 기능이 생기면 계좌 검증 등의 기능이 추가되어야 합니다.',
  })
  @ApiResponse({
    status: 200,
    description: '정산 계좌 등록/수정 성공',
    type: BankAccountDto,
  })
  async upsertSettlementBankAccount(
    @User() user: RequestUser,
    @Body() dto: BankAccountSettlementUpsertDto,
  ): Promise<BankAccountDto> {
    return this.bankAccountService.upsertSettlementBankAccount(user.id, dto);
  }

  @Delete('settlement')
  @ApiOperation({
    summary: '정산 계좌 해제/삭제',
    description: '정산 계좌를 정산 계좌에서 해제(삭제)합니다.',
  })
  @ApiResponse({
    status: 200,
    description: '정산 계좌 해제 성공',
    type: BankAccountDto,
  })
  async deleteSettlementBankAccount(
    @User() user: RequestUser,
  ): Promise<BankAccountDto> {
    return this.bankAccountService.deleteSettlementBankAccount(user.id);
  }

  @Post('verify')
  @ApiOperation({
    summary: '은행 계좌 검증',
    description: '사용자의 은행 계좌를 검증합니다.',
  })
  @ApiResponse({
    status: 200,
    description: '은행 계좌 검증 성공',
    type: BankAccountVerifyResDto,
  })
  async verifyBankAccount(
    @Body() dto: BankAccountVerifyDto,
  ): Promise<BankAccountVerifyResDto> {
    return this.bankAccountService.verifyBankAccount(dto);
  }
}
