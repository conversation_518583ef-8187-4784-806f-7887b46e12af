import { AppConfigService } from '@config/app/config.service';
import { PaypleService } from '@module/payple/payple.service';
import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { PrismaService } from '@provider/database/prisma.service';
import { BankAccountSettlementUpsertDto } from './dto/bank-account-settlement-upsert.dto';
import { BankAccountCreateDto } from './dto/bank-account.create.dto';
import { BankAccountUpdateDto } from './dto/bank-account.update.dto';
import {
  BankAccountVerifyDto,
  type BankAccountVerifyResDto,
} from './dto/bank-account.verify.dto';

@Injectable()
export class BankAccountService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly appConfigService: AppConfigService,
    private readonly paypleService: PaypleService,
  ) {}

  async createBankAccount(
    userUuid: string,
    dto: BankAccountCreateDto,
  ): Promise<Prisma.BankAccountGetPayload<null>> {
    return this.prismaService.bankAccount.create({
      data: { ...dto, userUuid },
    });
  }

  async updateBankAccount(
    userUuid: string,
    bankAccountUuid: string,
    dto: BankAccountUpdateDto,
  ): Promise<Prisma.BankAccountGetPayload<null>> {
    try {
      return await this.prismaService.bankAccount.update({
        where: { bankAccountUuid, userUuid },
        data: dto,
      });
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2025' // Record to update not found.
      ) {
        throw new NotFoundException(
          `UUID가 ${bankAccountUuid}인 계좌를 찾을 수 없거나, 해당 계좌가 사용자 ${userUuid}의 소유가 아닙니다.`,
        );
      }
      // Re-throw other errors
      throw error;
    }
  }

  async getSettlementBankAccount(
    userUuid: string,
  ): Promise<Prisma.BankAccountGetPayload<null>> {
    const settlementAccount = await this.prismaService.bankAccount.findFirst({
      where: { userUuid, isSettlement: true },
    });

    if (!settlementAccount) {
      throw new NotFoundException(
        `사용자 ${userUuid}의 정산 계좌를 찾을 수 없습니다.`,
      );
    }

    return settlementAccount;
  }

  async upsertSettlementBankAccount(
    userUuid: string,
    dto: BankAccountSettlementUpsertDto,
  ): Promise<Prisma.BankAccountGetPayload<null>> {
    return this.prismaService.$transaction(async (tx) => {
      // 1. 먼저 해당 계좌 정보가 있는지 확인
      const existingAccount = await tx.bankAccount.findUnique({
        where: {
          userUuid_bankCode_accountNumber: {
            userUuid,
            bankCode: dto.bankCode,
            accountNumber: dto.accountNumber,
          },
        },
      });

      // 2. 기존에 다른 정산 계좌가 있었다면 isSettlement = false 로 변경
      await tx.bankAccount.updateMany({
        where: {
          userUuid,
          isSettlement: true,
          // 방금 찾은 계좌가 기존 정산 계좌였을 수도 있으므로, 해당 계좌는 제외하고 업데이트
          NOT: existingAccount
            ? { bankAccountUuid: existingAccount.bankAccountUuid }
            : undefined,
        },
        data: { isSettlement: false },
      });

      // 3. 계좌 존재 여부에 따라 분기 처리
      if (existingAccount) {
        // 3-1. 계좌가 존재하면 업데이트 (isSettlement = true, accountHolder 업데이트)
        return tx.bankAccount.update({
          where: { bankAccountUuid: existingAccount.bankAccountUuid },
          data: { isSettlement: true, ...dto },
        });
      } else {
        // 3-2. 계좌가 없으면 새로 생성 (isSettlement = true)
        return tx.bankAccount.create({
          data: { ...dto, userUuid, isSettlement: true },
        });
      }
    });
  }
  async deleteSettlementBankAccount(
    userUuid: string,
  ): Promise<Prisma.BankAccountGetPayload<null>> {
    const account = await this.prismaService.bankAccount.findFirst({
      where: { isSettlement: true, userUuid },
    });
    if (!account || !account.isSettlement) {
      throw new NotFoundException('정산 계좌를 찾을 수 없습니다.');
    }
    return this.prismaService.bankAccount.update({
      where: { bankAccountUuid: account.bankAccountUuid },
      data: { isSettlement: false },
    });
  }

  async verifyBankAccount(
    dto: BankAccountVerifyDto,
  ): Promise<BankAccountVerifyResDto> {
    const paypleToken = await this.paypleService.partnerAuthenticationRequest();
    if (paypleToken.result !== 'T0000') {
      throw new InternalServerErrorException('페이플 토큰 발급 실패');
    }
    const verifyResponse = await this.paypleService.accountVerificationRequest(
      {
        cst_id: this.appConfigService.paypleCstId,
        custKey: this.appConfigService.paypleCustKey,
        bank_code_std: dto.bankCode,
        account_num: dto.accountNumber,
        account_holder_info_type:
          dto.accountHolderInfo.length === 6 ? '0' : '6',
        account_holder_info: dto.accountHolderInfo,
        sub_id: 'sub01',
      },
      paypleToken.access_token,
    );
    if (verifyResponse.result !== 'A0000') {
      throw new InternalServerErrorException('정산 계좌 검증 실패');
    }
    return { accountHolderName: verifyResponse.account_holder_name };
  }
}
