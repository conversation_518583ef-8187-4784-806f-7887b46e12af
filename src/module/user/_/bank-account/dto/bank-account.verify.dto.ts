import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Matches } from 'class-validator';

export class BankAccountVerifyDto {
  @ApiProperty({
    description: '은행 코드',
    example: '001',
  })
  @IsString()
  @IsNotEmpty()
  bankCode!: string;

  @ApiProperty({
    description: 'Account number',
    example: '**********',
  })
  @IsString()
  @IsNotEmpty()
  accountNumber!: string;

  @ApiProperty({
    description:
      'Personal information (6 digits for personal, 10 digits for company)',
    example: '880212',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^(?:\d{6}|\d{10})$/, {
    message: '계좌 개인정보는 6자리 또는 10자리여야 합니다.',
  })
  accountHolderInfo!: string;
}

export class BankAccountVerifyResDto {
  @ApiProperty({
    description: '예금주',
    example: '김철수',
  })
  accountHolderName!: string;
}
