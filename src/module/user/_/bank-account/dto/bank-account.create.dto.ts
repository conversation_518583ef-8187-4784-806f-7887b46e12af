import { ApiProperty, PickType } from '@nestjs/swagger';
import {
  IsBoolean,
  IsNotEmpty,
  IsNumberString,
  IsString,
  IsUUID,
} from 'class-validator';

export class BankAccountDto {
  @ApiProperty({
    description: 'Bank account UUID',
    type: String,
    example: '019665a3-0dde-7891-b090-cc2c45150d61',
  })
  @IsUUID('7', { message: 'Invalid UUID format.' })
  @IsNotEmpty({
    message: 'bankAccountUuid is required.',
  })
  bankAccountUuid!: string;

  @ApiProperty({
    description: 'Bank code',
    type: String,
    example: '088', // Example: Shinhan Bank
  })
  @IsString({ message: 'Bank code must be a string.' })
  @IsNotEmpty({ message: 'bankCode is required.' })
  bankCode!: string;

  @ApiProperty({
    description: 'Account number (no dash)',
    type: String,
    example: '************',
  })
  @IsString({ message: 'accountNumber must be a string.' })
  @IsNumberString(undefined, {
    message: 'accountNumber must be a number string.',
  })
  @IsNotEmpty({ message: 'accountNumber is required.' })
  accountNumber!: string;

  @ApiProperty({
    description: 'Account holder name',
    type: String,
    example: 'John Doe',
  })
  @IsString({ message: 'Account holder must be a string.' })
  @IsNotEmpty({ message: 'accountHolder is required.' })
  accountHolder!: string;

  @ApiProperty({
    description: 'Whether this is a settlement account',
    type: Boolean,
    example: false,
    default: false,
  })
  @IsBoolean({ message: 'isSettlement must be a boolean value.' })
  isSettlement!: boolean;
}

export class BankAccountCreateDto extends PickType(BankAccountDto, [
  'bankCode',
  'accountNumber',
  'accountHolder',
  'isSettlement',
] as const) {}
