import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class BankDto {
  @ApiProperty({
    description: '은행 코드',
    example: '088', // 예시: 신한은행
  })
  @IsString({ message: '은행 코드는 문자열이어야 합니다.' })
  @IsNotEmpty({ message: '은행 코드는 필수 입력 항목입니다.' })
  bankCode!: string;

  @ApiProperty({
    description: '은행 이름',
    example: '신한은행',
  })
  @IsString({ message: '은행 이름은 문자열이어야 합니다.' })
  @IsNotEmpty({ message: '은행 이름은 필수 입력 항목입니다.' })
  name!: string;
}
