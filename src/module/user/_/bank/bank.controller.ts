import { IsPublic } from '@common/decorators/public.decorator';
import { Controller, Get } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { BankService } from './bank.service';
import { BankDto } from './dto/bank.create.dto';

@ApiTags('Bank')
@Controller('banks')
export class BankController {
  constructor(private readonly bankService: BankService) {}

  @IsPublic()
  @Get()
  @ApiOperation({
    summary: '은행 목록 조회',
    description: '등록된 모든 은행 목록을 조회합니다.',
  })
  @ApiResponse({
    status: 200,
    description: '은행 목록 조회 성공',
    type: [BankDto],
  })
  async findAllBanks(): Promise<BankDto[]> {
    return this.bankService.findAllBanks();
  }
}
