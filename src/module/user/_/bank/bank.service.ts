import { Injectable } from '@nestjs/common';
import { Bank } from '@prisma/client';
import { PrismaService } from '@provider/database/prisma.service';

@Injectable()
export class BankService {
  constructor(private readonly prismaService: PrismaService) {}

  /**
   * 모든 은행 목록을 조회합니다.
   * @returns {Promise<Bank[]>} 은행 목록
   */
  async findAllBanks(): Promise<Bank[]> {
    return this.prismaService.bank.findMany();
  }
}
