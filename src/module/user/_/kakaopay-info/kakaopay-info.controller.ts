import { User } from '@common/decorators/jwk.decorators';
import { RequestUser } from '@common/types/token.types';
import { Body, Controller, Delete, Get, Put } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  KakaopayInfoDto,
  KakaopayInfoUpsertDto,
} from './dto/kakaopay-info.upsert.dto';
import { KakaopayInfoService } from './kakaopay-info.service';

@ApiTags('KakaopayInfo')
@ApiBearerAuth()
@Controller('kakaopay-info')
export class KakaopayInfoController {
  constructor(private readonly kakaopayInfoService: KakaopayInfoService) {}

  @ApiOperation({ summary: '내 카카오페이 정보 조회' })
  @Get()
  async getMyKakaopayInfo(
    @User() user: RequestUser,
  ): Promise<KakaopayInfoDto | null> {
    return this.kakaopayInfoService.getMyKakaopayInfo(user.id);
  }

  @ApiOperation({ summary: '카카오페이 정보 upsert' })
  @Put()
  async upsertKakaopayInfo(
    @User() user: RequestUser,
    @Body() dto: KakaopayInfoUpsertDto,
  ): Promise<KakaopayInfoDto> {
    return this.kakaopayInfoService.upsertKakaopayInfo(user.id, dto);
  }

  @ApiOperation({ summary: '카카오페이 정보 삭제' })
  @Delete()
  async deleteKakaopayInfo(@User() user: RequestUser): Promise<void> {
    return this.kakaopayInfoService.deleteKakaopayInfo(user.id);
  }
}
