import { Injectable } from '@nestjs/common';
import { PrismaService } from '@provider/database/prisma.service';
import {
  KakaopayInfoDto,
  KakaopayInfoUpsertDto,
} from './dto/kakaopay-info.upsert.dto';

@Injectable()
export class KakaopayInfoService {
  constructor(private readonly prismaService: PrismaService) {}

  async getMyKakaopayInfo(userUuid: string): Promise<KakaopayInfoDto | null> {
    const info = await this.prismaService.kakaopayInfo.findUnique({
      where: { userUuid },
    });
    if (!info) {
      return null;
    }
    return { userUuid: info.userUuid, kakao_uid: info.kakao_uid };
  }

  async upsertKakaopayInfo(
    userUuid: string,
    dto: KakaopayInfoUpsertDto,
  ): Promise<KakaopayInfoDto> {
    const info = await this.prismaService.kakaopayInfo.upsert({
      where: { userUuid },
      create: { userUuid, kakao_uid: dto.kakao_uid },
      update: { kakao_uid: dto.kakao_uid },
    });
    return { userUuid: info.userUuid, kakao_uid: info.kakao_uid };
  }

  async deleteKakaopayInfo(userUuid: string): Promise<void> {
    await this.prismaService.kakaopayInfo.delete({ where: { userUuid } });
  }
}
