import { ApiProperty, PickType } from '@nestjs/swagger';
import { IsString, IsUUID } from 'class-validator';

export class KakaopayInfoDto {
  @ApiProperty({
    description: '유저 UUID',
    example: 'uuid',
  })
  @IsUUID('7')
  userUuid!: string;

  @ApiProperty({
    description: 'kakao uid from QR Code',
    example: '123456789012345678901234',
  })
  @IsString()
  kakao_uid!: string;
}

export class KakaopayInfoUpsertDto extends PickType(KakaopayInfoDto, [
  'kakao_uid',
]) {}
