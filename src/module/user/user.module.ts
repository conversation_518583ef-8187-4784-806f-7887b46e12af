import { Global, Module } from '@nestjs/common';
import { BankAccountModule } from './_/bank-account/bank-account.module';
import { BankModule } from './_/bank/bank.module';
import { UserController } from './user.controller';
import { UserPublicController } from './user.public.controller';
import { UserService } from './user.service';
import { KakaopayInfoModule } from './_/kakaopay-info/kakaopay-info.module';

@Global()
@Module({
  providers: [UserService],
  exports: [UserService],
  controllers: [UserController, UserPublicController],
  imports: [BankAccountModule, BankModule, KakaopayInfoModule],
})
export class UserModule {}
