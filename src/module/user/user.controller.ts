import { User } from '@common/decorators/jwk.decorators';
import { IsPublic } from '@common/decorators/public.decorator';
import type { RequestUser } from '@common/types/token.types';
import { UserService } from '@module/user/user.service';
import { Body, Controller, Get, Patch, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { GetUsersForSearchDto } from './dto/get-users-for-search.dto';
import { UpdateUserInfoDto } from './dto/update.user_info.dto';
import { UserHandleDuplicateCheckDto } from './dto/user.handle-duplicate-check.dto';

@ApiBearerAuth()
@ApiTags('User')
@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @ApiOperation({
    summary: '내 정보 조회',
    description: '내 정보를 조회합니다.',
  })
  @Get('me')
  getMyUserInfo(@User() user: RequestUser) {
    return this.userService.getUserInfo(user.id);
  }

  @ApiOperation({
    summary: '내 정보 수정',
    description: '내 정보를 수정합니다.',
  })
  @Patch('me')
  updateMyUserInfo(@User() user: RequestUser, @Body() dto: UpdateUserInfoDto) {
    return this.userService.updateUserInfo(dto, user);
  }

  @ApiOperation({
    summary: '유저 핸들 중복체크',
    description: '유저 핸들의 중복을 체크합니다. 있다면 true, 없으면 false',
  })
  @IsPublic()
  @Get('handle/duplicate')
  userHandleDuplicateCheck(@Query() dto: UserHandleDuplicateCheckDto) {
    return this.userService.userHandleDuplicateCheck(dto);
  }

  @ApiOperation({
    summary: '유저 검색',
    description: '닉네임 또는 핸들로 유저를 검색합니다.',
  })
  @Get('search')
  async getUsersForSearch(@Query() dto: GetUsersForSearchDto) {
    return this.userService.getUsersForSearch(dto);
  }
}
