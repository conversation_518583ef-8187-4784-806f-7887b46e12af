// import { UserRole } from '@common/types/token.types';
// import { AppConfigModule } from '@config/app/config.module';
// import { Test, TestingModule } from '@nestjs/testing';
// import { PrismaService } from '@provider/database/prisma.service';
// import { UserService } from './user.service';

// describe('UserService', () => {
//   let service: UserService;
//   let prismaService: PrismaService;

//   beforeAll(async () => {
//     const module: TestingModule = await Test.createTestingModule({
//       imports: [AppConfigModule],
//       providers: [UserService, PrismaService],
//     }).compile();

//     service = module.get<UserService>(UserService);
//     prismaService = module.get<PrismaService>(PrismaService);
//   });

//   it('팔로우 안된 상태에서 다른 사람을 팔로우', async () => {
//     const follower = await prismaService.user.create({
//       data: {
//         email: '<EMAIL>',
//         nickname: 'miu',
//         handle: 'miu',
//       },
//     });
//     const followee = await prismaService.user.create({
//       data: {
//         email: '<EMAIL>',
//         nickname: 'sella',
//         handle: 'sella',
//       },
//     });

//     await service.follow(
//       { id: follower.id, role: UserRole.USER },
//       followee.handle,
//     );

//     const follow = await prismaService.follow.findFirst({
//       where: {
//         followerUserId: follower.id,
//         followeeUserId: followee.id,
//       },
//     });

//     expect(follow).toStrictEqual({
//       followerUserId: follower.id,
//       followeeUserId: followee.id,
//     });
//   });

//   it('팔로우 된 상태에서 다른 사람을 언팔로우', async () => {
//     const follower = await prismaService.user.create({
//       data: {
//         email: '<EMAIL>',
//         nickname: 'miu',
//         handle: 'miu',
//       },
//     });
//     const followee = await prismaService.user.create({
//       data: {
//         email: '<EMAIL>',
//         nickname: 'sella',
//         handle: 'sella',
//       },
//     });

//     await prismaService.follow.create({
//       data: {
//         followerUserId: follower.id,
//         followeeUserId: followee.id,
//       },
//     });

//     await service.follow(
//       { id: follower.id, role: UserRole.USER },
//       followee.handle,
//     );

//     const follow = await prismaService.follow.findFirst({
//       where: {
//         followerUserId: follower.id,
//         followeeUserId: followee.id,
//       },
//     });

//     expect(follow).toBeNull();
//   });

//   afterEach(async () => {
//     await prismaService.$transaction([
//       prismaService.follow.deleteMany(),
//       prismaService.user.deleteMany(),
//     ]);
//     jest.clearAllMocks();
//   });

//   afterAll(async () => {
//     await prismaService.$disconnect();
//   });
// });
