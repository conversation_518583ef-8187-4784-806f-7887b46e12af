import { SnsPlatformEnum } from '@common/lib/enum';
import { RequestUser, UserRole } from '@common/types/token.types';
import {
  ConflictException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { SnsPlatformNameEnum } from '@prisma/client';
import { PrismaService } from '@provider/database/prisma.service';
import { isDefined } from 'class-validator';
import { randomUUID } from 'crypto';
import type { GetUsersForSearchDto } from './dto/get-users-for-search.dto';
import type { UpdateUserInfoDto } from './dto/update.user_info.dto';
import { UserHandleDuplicateCheckDto } from './dto/user.handle-duplicate-check.dto';

@Injectable()
export class UserService {
  constructor(private readonly prismaService: PrismaService) {}

  private async snsFindOrCreate(type: SnsPlatformNameEnum) {
    let sns = await this.prismaService.snsPlatform.findFirst({
      where: {
        name: type,
      },
    });
    if (!sns) {
      sns = await this.prismaService.snsPlatform.create({
        data: {
          name: type,
        },
      });
    }
    return sns;
  }

  async ifNotPresentCreate(arg: {
    type: SnsPlatformNameEnum;
    oauthId: string;
    nickname: string;
    realName?: string;
    handle?: string;
    email?: string;
    profileImg?: string;
    refreshToken?: string;
    seller?: boolean;
  }) {
    const sns = await this.snsFindOrCreate(arg.type);

    let user = await this.prismaService.user.findFirst({
      where: {
        snsOauth: {
          some: {
            snsPlatformId: sns.snsPlatformId,
            snsUid: arg.oauthId,
          },
        },
      },
      select: {
        userUuid: true,
      },
    });
    if (arg.handle) {
      const existHandle = await this.prismaService.user.findUnique({
        where: {
          handle: arg.handle,
        },
      });

      if (existHandle) {
        throw new ConflictException('이 아이디는 이미 사용중이에요 ㅠㅠ');
      }
    }
    if (!user) {
      const uuid = randomUUID();
      user = await this.prismaService.user.create({
        data: {
          nickname: arg.nickname,
          handle: arg.handle ?? uuid.split('-')[4],
          realName: arg.realName,
          email: arg.email,
          profileImg: arg.profileImg,
          snsOauth: {
            create: {
              snsUid: arg.oauthId,
              snsPlatformId: sns.snsPlatformId,
              refreshToken: arg.refreshToken,
            },
          },
        },
        select: {
          userUuid: true,
        },
      });
    }

    // 일단 셀러여부는 임시로 전부 일반 유저로
    return {
      user,
      role: UserRole.USER,
      snsPlatformId: sns.snsPlatformId,
    };
  }

  async updateUserInfo(dto: UpdateUserInfoDto, requestUser: RequestUser) {
    const user = await this.prismaService.user.findUnique({
      where: {
        userUuid: requestUser.id,
      },
    });
    if (user === null) {
      throw new UnauthorizedException('invalid token payload [0001]');
    }
    // if (dto.handle && dto.handle !== user.handle) {
    //   const existHandle = await this.prismaService.user.findUnique({
    //     where: {
    //       handle: dto.handle,
    //     },
    //   });

    //   if (existHandle) {
    //     throw new BadRequestException('이 핸들은 이미 사용중이에요 ㅠㅠ');
    //   }
    // }

    await this.prismaService.$transaction(async (tx) => {
      await tx.user.update({
        where: { userUuid: user.userUuid },
        data: {
          ...(isDefined(dto.nickname) && { nickname: dto.nickname }),
          ...(isDefined(dto.profileImg) && { profileImg: dto.profileImg }),
          ...(isDefined(dto.explainText) && { explainText: dto.explainText }),
          ...(isDefined(dto.email) && { email: dto.email }),
        },
      });
      if (isDefined(dto.twitterHandle)) {
        const { snsPlatformId } = await tx.snsPlatform.findFirstOrThrow({
          where: { name: SnsPlatformNameEnum.x },
        });
        await tx.snsOauth.upsert({
          where: {
            userId_snsPlatformId: {
              userId: user.userUuid,
              snsPlatformId,
            },
          },
          create: {
            userId: user.userUuid,
            snsPlatformId,
            snsUid: `temp-${dto.twitterHandle}`, // XXX: 임시값
            handle: dto.twitterHandle,
          },
          update: { handle: dto.twitterHandle || null },
        });
      } else {
        await tx.snsOauth.deleteMany({
          where: {
            userId: user.userUuid,
            snsPlatformId: { in: [SnsPlatformEnum.x] },
          },
        });
      }
      if (isDefined(dto.instagramHandle)) {
        const { snsPlatformId } = await tx.snsPlatform.findFirstOrThrow({
          where: { name: SnsPlatformNameEnum.instagram },
        });
        await tx.snsOauth.upsert({
          where: {
            userId_snsPlatformId: {
              userId: user.userUuid,
              snsPlatformId,
            },
          },
          create: {
            userId: user.userUuid,
            snsPlatformId,
            snsUid: `temp-${dto.instagramHandle}`, // XXX: 임시값
            handle: dto.instagramHandle,
          },
          update: { handle: dto.instagramHandle || null },
        });
      } else {
        await tx.snsOauth.deleteMany({
          where: {
            userId: user.userUuid,
            snsPlatformId: { in: [SnsPlatformEnum.instagram] },
          },
        });
      }
      if (dto.bankCode && dto.accountNumber && dto.accountHolder) {
        await tx.bankAccount.upsert({
          where: {
            userUuid_bankCode_accountNumber: {
              userUuid: user.userUuid,
              bankCode: dto.bankCode,
              accountNumber: dto.accountNumber,
            },
          },
          create: {
            userUuid: user.userUuid,
            accountHolder: dto.accountHolder,
            bankCode: dto.bankCode,
            accountNumber: dto.accountNumber,
            isSettlement: true,
          },
          update: { isSettlement: true },
        });
      } else {
        await tx.bankAccount.deleteMany({
          where: {
            userUuid: user.userUuid,
            isSettlement: true,
          },
        });
      }
      if (isDefined(dto.kakao_uid)) {
        await tx.kakaopayInfo.upsert({
          where: {
            userUuid: user.userUuid,
          },
          create: {
            userUuid: user.userUuid,
            kakao_uid: dto.kakao_uid,
          },
          update: { kakao_uid: dto.kakao_uid },
        });
      } else {
        await tx.kakaopayInfo.deleteMany({
          where: { userUuid: user.userUuid },
        });
      }
    });
  }

  async getUserInfo(userUuid: string) {
    return {
      ...(await this.prismaService.user.findUnique({
        where: { userUuid },
        select: {
          nickname: true,
          profileImg: true,
          explainText: true,
          handle: true,
          realName: true,
          email: true,
          userUuid: true,
        },
      })),
      bankAccount:
        (await this.prismaService.bankAccount.findFirst({
          where: { userUuid, isSettlement: true },
          select: { bankCode: true, accountNumber: true, accountHolder: true },
        })) ?? null,
      kakaopayInfo:
        (await this.prismaService.kakaopayInfo.findUnique({
          select: { kakao_uid: true },
          where: { userUuid },
        })) ?? null,
      snsOauth: await this.prismaService.snsOauth.findMany({
        where: {
          userId: userUuid,
          snsPlatformId: { in: [SnsPlatformEnum.x, SnsPlatformEnum.instagram] },
        },
        select: { snsPlatform: true, handle: true },
      }),
    };
  }

  async userHandleDuplicateCheck(dto: UserHandleDuplicateCheckDto) {
    const user = await this.prismaService.user.findUnique({
      where: {
        handle: dto.handle,
      },
    });
    return !!user;
  }

  async getUsersForSearch(dto: GetUsersForSearchDto) {
    return this.prismaService.user.findMany({
      where: {
        nickname: { contains: dto.nickname },
        handle: { contains: dto.handle },
      },
    });
  }
  async findUser(arg: { type: SnsPlatformNameEnum; oauthId: string }) {
    const user = await this.prismaService.user.findFirst({
      where: {
        snsOauth: {
          some: {
            snsPlatform: {
              name: arg.type,
            },
            snsUid: arg.oauthId,
          },
        },
      },
      select: {
        userUuid: true,
        snsOauth: {
          select: {
            snsPlatformId: true,
          },
          where: {
            snsUid: arg.oauthId,
            snsPlatform: {
              name: arg.type,
            },
          },
        },
      },
    });
    if (!user) {
      return null;
    }

    return {
      userUuid: user.userUuid,
      snsPlatformId: user.snsOauth[0].snsPlatformId,
    };
  }
}
