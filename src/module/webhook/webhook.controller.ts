import { IsPublic } from '@common/decorators/public.decorator';
import { PaypleAuthenticationOutcome } from '@common/types/payple.types';
import { AppConfigService } from '@config/app/config.service';
import { Body, Controller, Post, Res } from '@nestjs/common';
import { FastifyReply } from 'fastify';
import { WebhookService } from './webhook.service';
@Controller('webhook')
export class WebhookController {
  constructor(
    private readonly webhookService: WebhookService,
    private readonly appConfigService: AppConfigService,
  ) {}

  @IsPublic()
  @Post('callback')
  async paypleWebhook(
    @Body() dto: PaypleAuthenticationOutcome,
    @Res() response: FastifyReply,
  ) {
    const result = await this.webhookService.paypleWebhookTicket(dto);
    if (result.success && result.orderUuid) {
      // TODO: 성공 URL 여기에 작성해주세요.
      return response.send(
        `${this.appConfigService.domain}/success?orderUuid=${result.orderUuid}`,
      );
    } else {
      // fail URL로 리다이렉트
      return response.send(
        `${this.appConfigService.domain}/fail?reason=${result.reason}`,
      );
    }
  }

  @IsPublic()
  @Post('callback/customer')
  async paypleCustomer(@Body() dto: PaypleAuthenticationOutcome) {
    await this.webhookService.paypleWebhook(dto);
  }

  // 링크 결제 웹훅
  @IsPublic()
  @Post('callback/default')
  async paypleWebhookDefault(@Body() dto: PaypleAuthenticationOutcome) {
    console.log('default webhook CallBack', dto);
    await this.webhookService.paypleWebhook(dto);
    // const result = await this.webhookService.paypleWebhook(dto);
    // if (result.success && result.orderUuid) {
    //   // success URL로 리다이렉트
    //   return response.redirect(
    //     `https://backend.${this.appConfigService.domain}/success?orderUuid=${result.orderUuid}`,
    //   );
    // } else {
    //   // fail URL로 리다이렉트
    //   return response.redirect(
    //     `https://backend.${this.appConfigService.domain}/fail?reason=${result.reason}`,
    //   );
    // }
  }
}
