import { PaypleAuthenticationOutcome } from '@common/types/payple.types';
import { TICKET_PRICE, TicketDuration } from '@common/types/ticket.types';
import { PaypleService } from '@module/payple/payple.service';
import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '@provider/database/prisma.service';
import { compact } from 'lodash';

@Injectable()
export class WebhookService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly paypleService: PaypleService,
  ) {}

  async paypleWebhookTicket(
    dto: PaypleAuthenticationOutcome,
  ): Promise<{ orderUuid?: string; success: boolean; reason: string }> {
    if (dto.PCD_PAY_RST !== 'success') {
      return {
        success: false,
        reason: '결제 시간 초과 다시한번 결제 해주세요.',
      };
    }

    const ticket = await this.prismaService.prepareTicket.findUnique({
      where: {
        ticketUuid: dto.PCD_PAY_OID,
      },
    });
    if (!ticket) {
      const existTicket = await this.prismaService.ticket.findUnique({
        where: {
          ticketUuid: dto.PCD_PAY_OID,
        },
      });
      if (existTicket) {
        return {
          success: false,
          reason: '이미 결제된 건입니다.',
        };
      } else {
        return {
          success: false,
          reason: '해당 결제건이 존재하지 않습니다.',
        };
      }
    }

    if (
      TICKET_PRICE[ticket.usableDay as TicketDuration] !==
      Number(dto.PCD_PAY_TOTAL)
    ) {
      return {
        success: false,
        reason: '기록되어 있는 결제건과 승인 결제건의 데이터가 다릅니다.',
      };
    }

    await this.prismaService.$transaction(async (prisma) => {
      await prisma.ticket.create({
        data: {
          ticketUuid: ticket.ticketUuid,
          userUuid: ticket.userUuid,
          usableDay: ticket.usableDay,
          expiredAt: ticket.expiredAt,
        },
      });

      await prisma.prepareTicket.delete({
        where: {
          ticketUuid: ticket.ticketUuid,
        },
      });
      // 넣고 실패했을때
      const res = await this.paypleService.requestApproval({
        PCD_AUTH_KEY: dto.PCD_AUTH_KEY,
        PCD_PAY_REQKEY: dto.PCD_PAY_REQKEY,
        url: dto.PCD_PAY_COFURL,
        PCD_PAYER_ID: dto.PCD_PAYER_ID,
      });

      if (res.data.PCD_PAY_RST !== 'success') {
        throw new BadRequestException(
          '결제 승인 요청에 실패했습니다. 결제 승인 요청을 다시 시도해주세요.',
        );
      }
    });

    return {
      reason: '',
      success: true,
      orderUuid: ticket.ticketUuid,
    };
  }

  // 링크결제
  async paypleWebhook(
    dto: PaypleAuthenticationOutcome,
  ): Promise<{ orderUuid?: string; success: boolean; reason: string }> {
    if (dto.PCD_PAY_RST !== 'success') {
      return {
        success: false,
        reason: '결제 시간 초과 다시한번 결제 해주세요.',
      };
    }
    const prepareOrder = await this.prismaService.prepareOrder.findUnique({
      where: {
        orderUuid: dto.PCD_PAY_OID,
      },
      include: {
        prepareOrderedGoods: true,
        PrepareDeliveryInfo: true,
      },
    });
    if (!prepareOrder) {
      const existOrder = await this.prismaService.order.findUnique({
        where: {
          orderUuid: dto.PCD_PAY_OID,
        },
      });
      if (existOrder) {
        return {
          reason: '이미 결제가 완료된 건입니다.',
          success: false,
        };
      }

      // TODO: 해당 주문건 취소요청
      return {
        reason: 'Order not found.',
        success: false,
      };
    }

    // const parameter = new URLSearchParams(dto.PCD_LINK_ADD_PARAM);
    // sellerUuid
    const prepareSellerOrder =
      await this.prismaService.prepareSalesOrder.findMany({
        where: {
          orderUuid: prepareOrder.orderUuid,
        },
      });

    await this.prismaService.$transaction([
      this.prismaService.order.create({
        data: {
          payMethod: prepareOrder.payMethod,
          orderUuid: prepareOrder.orderUuid,
          linkKey: prepareOrder.linkKey,
          userUuid: prepareOrder.userUuid,
          orderTitle: prepareOrder.orderTitle,
          totalGoodsPrice: prepareOrder.totalGoodsPrice,
          totalVatPrice: prepareOrder.totalVatPrice,
          totalDeliveryPrice: prepareOrder.totalDeliveryPrice,
          orderStatus: prepareOrder.orderStatus,
        },
      }),
      this.prismaService.salesOrder.createMany({
        data: prepareSellerOrder.map((item) => ({
          orderUuid: item.orderUuid,
          userUuid: item.userUuid,
          orderTitle: item.orderTitle,
          totalGoodsPrice: item.totalGoodsPrice,
          totalVatPrice: item.totalVatPrice,
          totalDeliveryPrice: item.totalDeliveryPrice,
          payMethod: item.payMethod,
          orderStatus: item.orderStatus,
        })),
      }),
      this.prismaService.orderedGoods.createMany({
        data: prepareOrder.prepareOrderedGoods.map((item) => ({
          orderUuid: prepareOrder.orderUuid,
          orderedGoodsUuid: item.orderedGoodsUuid,
          deliveryStatus: item.deliveryStatus,
          goodsOrderStatus: item.goodsOrderStatus,
          goodsCount: item.goodsCount,
          goodsUuid: item.goodsUuid,
          goodsName: item.goodsName,
          goodsPrice: item.goodsPrice,
          vatPrice: item.vatPrice,
          ratio: item.ratio,
          sellerUuid: item.sellerUuid,
          boothUuid: item.boothUuid,
          boothName: item.boothName,
        })),
      }),
      this.prismaService.deliveryInfo.createMany({
        data: prepareOrder.PrepareDeliveryInfo,
      }),
      ...compact(
        prepareOrder.prepareOrderedGoods.map((orderGoods) => {
          if (!orderGoods.boothUuid) {
            // TODO: 부스에서 산 굿즈가 아닌경우 따로 처리필요
            return null;
          }
          return this.prismaService.goodsBoothPivot.updateMany({
            where: {
              boothUuid: orderGoods.boothUuid,
              goodsUuid: orderGoods.goodsUuid,
            },
            data: {
              quantity: {
                increment: orderGoods.goodsCount,
              },
              prepareCount: {
                decrement: orderGoods.goodsCount,
              },
            },
          });
        }),
      ),
      this.prismaService.prepareOrderedGoods.deleteMany({
        where: {
          orderUuid: prepareOrder.orderUuid,
        },
      }),
      this.prismaService.prepareSalesOrder.deleteMany({
        where: {
          orderUuid: prepareOrder.orderUuid,
        },
      }),
      this.prismaService.prepareOrder.delete({
        where: {
          orderUuid: prepareOrder.orderUuid,
        },
      }),
      // 굿즈 재고 차감 링크결제 만들 때 사전 차감하고 머시깽이 하는 로직 필요 그 머시깽이 로직이 위에있음
      ...prepareOrder.prepareOrderedGoods.map((good) =>
        this.prismaService.goods.update({
          where: { goodsUuid: good.goodsUuid },
          data: { quantity: { decrement: good.goodsCount } },
        }),
      ),
      ...prepareOrder.prepareOrderedGoods.map((good) =>
        this.prismaService.goodsStats.upsert({
          where: { goodsUuid: good.goodsUuid },
          create: {
            goodsUuid: good.goodsUuid,
            salesCount: good.goodsCount,
            revenue: good.goodsPrice,
            netProfit: good.goodsPrice, // TODO: 순이익 처리 방침
          },
          update: {
            salesCount: {
              increment: good.goodsCount,
            },
            revenue: {
              increment: good.goodsPrice,
            },
            netProfit: {
              increment: good.goodsPrice, // TODO: 순이익 처리방침
            },
          },
        }),
      ),
    ]);

    return {
      reason: '',
      success: true,
      // orderUuid: order.orderUuid,
    };
  }
}
