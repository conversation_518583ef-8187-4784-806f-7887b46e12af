import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class PerTransactionTransferCompletionWebhook {
  @ApiProperty({
    description: '페이플 응답코드',
    example: 'A0000',
  })
  @IsString()
  @IsNotEmpty()
  result!: string;

  @ApiProperty({
    description: '응답 메시지',
    example: '처리 성공',
  })
  @IsString()
  @IsNotEmpty()
  message!: string;

  @ApiProperty({
    description: '파트너 ID',
    example: '6548264741426583803027',
  })
  @IsString()
  @IsNotEmpty()
  cst_id!: string;

  @ApiProperty({
    description: '파트너 하위 셀러의 ID',
    example: 'sub01',
  })
  @IsString()
  @IsNotEmpty()
  sub_id!: string;

  @ApiProperty({
    description: '중복 이체를 방지하기 위한 키',
    example: 'uo3h1708394911737306031340821',
  })
  @IsString()
  @IsNotEmpty()
  distinct_key!: string;

  @ApiProperty({
    description: '그룹키',
    example: 'QlJKZDZrNVBzbW9Yc1UzTWNIdW05dz09',
  })
  @IsString()
  @IsNotEmpty()
  group_key!: string;

  @ApiProperty({
    description: '인증 완료된 계좌의 빌링키',
    example: 'cb9d695e-c034-4eeb-9550-c53a9ab7c2e4',
  })
  @IsString()
  @IsNotEmpty()
  billing_tran_id!: string;

  @ApiProperty({
    description: '완료된 이체 건의 고유키',
    example: 'd2834aa2-11be-4a04-afce-9c0ca5ceacea',
  })
  @IsUUID()
  @IsNotEmpty()
  api_tran_id!: string;

  @ApiProperty({
    description: '금융기관으로부터 수신한 이체 완료 상세일시 (밀리세컨드)',
    example: '*****************',
  })
  @IsString()
  @IsNotEmpty()
  api_tran_dtm!: string;

  @ApiProperty({
    description: '금융기관과 통신을 위해 필요한 고유 키',
    example: 'M202112389U152040289',
  })
  @IsString()
  @IsNotEmpty()
  bank_tran_id!: string;

  @ApiProperty({
    description: '금융기관으로부터 수신한 인증 완료 일자',
    example: '********',
  })
  @IsString()
  @IsNotEmpty()
  bank_tran_date!: string;

  @ApiProperty({
    description: '금융기관 응답코드',
    example: '000',
  })
  @IsString()
  @IsNotEmpty()
  bank_rsp_code!: string;

  @ApiProperty({
    description: '금융기관 코드',
    example: '020',
  })
  @IsString()
  @IsNotEmpty()
  bank_code_std!: string;

  @ApiProperty({
    description: '금융기관 점별 코드',
    example: '0201234',
  })
  @IsString()
  @IsNotEmpty()
  bank_code_sub!: string;

  @ApiProperty({
    description: '금융기관명',
    example: '우리은행',
  })
  @IsString()
  @IsNotEmpty()
  bank_name!: string;

  @ApiProperty({
    description: '계좌번호',
    example: '****************',
  })
  @IsString()
  @IsNotEmpty()
  account_num!: string;

  @ApiProperty({
    description: '일부 가림 처리된 계좌번호',
    example: '****************',
  })
  @IsString()
  @IsNotEmpty()
  account_num_masked!: string;

  @ApiProperty({
    description: '예금주명',
    example: '김이플',
  })
  @IsString()
  @IsNotEmpty()
  account_holder_name!: string;

  @ApiProperty({
    description: '상대방 계좌 거래 내역에 표시될 수 있는 최대 6자까지의 문구',
    example: '테스트입니다',
  })
  @IsString()
  @IsOptional()
  print_content?: string;

  @ApiProperty({
    description: '이체 완료 금액',
    example: '1000',
  })
  @IsString()
  @IsNotEmpty()
  tran_amt!: string;
}
