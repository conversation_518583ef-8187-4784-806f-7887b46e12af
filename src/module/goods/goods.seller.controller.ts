import { User } from '@common/decorators/jwk.decorators';
import { RequestUser } from '@common/types/token.types';
import {
  Body,
  Controller,
  Delete,
  Get,
  Header,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { FastifyReply } from 'fastify';
import { GoodsCreateDto } from './dto/goods.create.dto';
import { GoodsListDto } from './dto/goods.list.dto';
import { GoodsUpdateDto } from './dto/goods.update.dto';
import { GoodsService } from './goods.service';

@ApiBearerAuth()
@ApiTags('Goods Seller')
@Controller('goods/seller')
export class GoodsSellerController {
  constructor(private readonly goodsService: GoodsService) {}

  @ApiOperation({
    summary: '상품 리스트',
    description: '',
  })
  @Get()
  goodsList(@User() user: RequestUser, @Query() dto: GoodsListDto) {
    return this.goodsService.goodsList(user, dto);
  }

  @ApiOperation({
    summary: 'Get Goods Detail',
    description: '',
  })
  @Get(':goodsUuid')
  goodsDetail(
    @User() user: RequestUser,
    @Param('goodsUuid') goodsUuid: string,
  ) {
    return this.goodsService.goodsDetail(user.id, goodsUuid);
  }

  @ApiOperation({
    summary: 'Get goods count for creator',
    description: '',
  })
  @Get('count')
  goodsCountForCreator(@User() user: RequestUser) {
    return this.goodsService.goodsCountForCreator(user);
  }

  @ApiOperation({
    summary: '상품 리스트 스트리밍',
    description:
      '대량의 상품 데이터를 스트림 형식으로 제공합니다. 많은 데이터를 한번에 스트림형식으로',
  })
  @Get('stream')
  @Header('Content-Type', 'application/json')
  @Header('Transfer-Encoding', 'chunked')
  async goodsListStream(@User() user: RequestUser, @Res() res: FastifyReply) {
    const stream = await this.goodsService.goodsListStream(user);

    // 스트림 시작을 알리는 헤더
    res.raw.write('{"items":[');

    let isFirst = true;

    stream.on('data', (chunk) => {
      // JSON 배열 형식을 유지하기 위해 쉼표 처리
      if (!isFirst) {
        res.raw.write(',');
      } else {
        isFirst = false;
      }

      // 개행 문자가 포함된 JSON 문자열에서 개행 제거
      res.raw.write(chunk.toString().replace(/\n$/, ''));
    });

    stream.on('end', () => {
      // 스트림 종료를 알리는 닫는 부분
      res.raw.write(']}');
      res.raw.end();
    });

    stream.on('error', (err) => {
      console.error('Stream error:', err);
      res.status(500).raw.end();
    });
  }

  @Post()
  goodsCreate(@User() user: RequestUser, @Body() dto: GoodsCreateDto) {
    return this.goodsService.goodsCreate(user, dto);
  }

  @Patch(':goodsUuid')
  goodsUpdate(
    @User() user: RequestUser,
    @Body() dto: GoodsUpdateDto,
    @Param('goodsUuid', ParseUUIDPipe) goodsUuid: string,
  ) {
    return this.goodsService.goodsUpdate(user, dto, goodsUuid);
  }

  @Delete(':goodsUuid')
  @ApiOperation({
    summary: '상품 삭제',
    description: '상품을 삭제합니다.',
  })
  goodsDelete(
    @User() user: RequestUser,
    @Param('goodsUuid', ParseUUIDPipe) goodsUuid: string,
  ) {
    return this.goodsService.goodsDelete(user, goodsUuid);
  }
}
