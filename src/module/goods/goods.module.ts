import { Modu<PERSON> } from '@nestjs/common';
import { CharacterModule } from './_/character/character.module';
import { GoodsCategoryModule } from './_/goods-category/goods-category.module';
import { GoodsSetModule } from './_/goods-set/goods-set.module';
import { SourceModule } from './_/source/source.module';
import { GoodsController } from './goods.controller';
import { GoodsSellerController } from './goods.seller.controller';
import { GoodsService } from './goods.service';

@Module({
  imports: [GoodsCategoryModule, CharacterModule, SourceModule, GoodsSetModule],
  controllers: [GoodsSellerController, GoodsController],
  providers: [GoodsService],
  exports: [GoodsCategoryModule, CharacterModule, SourceModule, GoodsSetModule],
})
export class GoodsModule {}
