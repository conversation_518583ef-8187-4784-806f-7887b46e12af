import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { GoodsSellingMethodEnum } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Length,
  Min,
  ValidateNested,
} from 'class-validator';

export class ParticipatedUserDto {
  @ApiProperty({
    description: 'userUuid',
    type: String,
    example: '00000000-0000-0000-0000-000000000000',
  })
  @IsUUID()
  @IsNotEmpty()
  userUuid!: string;

  @ApiProperty({
    description: 'ratio',
    type: Number,
    example: 0.5,
  })
  @IsInt()
  @Min(0)
  ratio!: number;
}

export class GoodsDto {
  @ApiProperty({
    description: '굿즈 uuid',
    type: String,
    example: '00000000-0000-0000-0000-000000000000',
  })
  @IsUUID()
  @IsNotEmpty()
  goodsUuid!: string;

  @ApiProperty({
    description: '종류',
    type: String,
    example: '00000000-0000-0000-0000-000000000000',
  })
  @IsUUID()
  @IsNotEmpty()
  goodsCategoryUuid!: string;

  @ApiProperty({
    description: '굿즈 이름',
    type: String,
    example: '하츠네 미쿠 아크릴 스탠드',
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 255)
  name!: string;

  @ApiProperty({
    description: '캐릭터 uuid',
    type: String,
    example: ['019758a8-d8e1-7f73-8c3d-08ac8fcc4f1d'],
    isArray: true,
  })
  @IsArray()
  @IsUUID('7', { each: true })
  @IsNotEmpty()
  characterUuids!: string[];

  // 굿즈 가격
  @ApiProperty({
    description: '굿즈 가격',
    type: Number,
    example: 15000,
  })
  @IsNotEmpty()
  @IsInt()
  price!: number;

  @ApiProperty({
    description: '재고 수량',
    type: Number,
    example: 50,
  })
  @IsNotEmpty()
  @IsInt()
  @Min(1, { message: '추가할 때 재고 수량은 최소 1개입니다.' })
  quantity!: number;

  @ApiProperty({
    description: '이미지 URL 리스트',
    type: String,
    example: ['https://static.sherry.gg/sherry/EbgpXHVVAAABFIS.jpeg'],
    isArray: true,
  })
  @IsNotEmpty()
  @IsString({ each: true })
  imageUrls!: string[];

  @ApiPropertyOptional({
    description: '굿즈 설명',
    type: [String],
    example: ['신상품입니다', '한정판 굿즈입니다'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  descriptions?: string[];

  @ApiProperty({
    description: '색상 태그',
    type: String,
    example: 'red',
  })
  @IsOptional()
  @IsString()
  colorTag?: string;

  @ApiProperty({
    description: '즐겨찾기 여부',
    type: Boolean,
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  favorite?: boolean;

  @ApiProperty({
    description: '참여 유저 UUID',
    type: [ParticipatedUserDto],
    example: [{ userUuid: '00000000-0000-0000-0000-000000000000', ratio: 100 }],
  })
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ParticipatedUserDto)
  participatedUsers!: ParticipatedUserDto[];

  @ApiPropertyOptional({
    description: '배송비',
    type: Number,
    example: 3000,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  deliveryFee?: number;

  @ApiPropertyOptional({
    description: '판매방법',
    type: GoodsSellingMethodEnum,
    enum: GoodsSellingMethodEnum,
    enumName: 'GoodsSellingMethodEnum',
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  sellingMethods?: GoodsSellingMethodEnum[];

  @ApiPropertyOptional({
    description: '활성화 여부',
    type: Boolean,
  })
  @IsOptional()
  @IsBoolean()
  isActivated?: boolean;
}

export class GoodsCreateDto {
  // 생성할 굿즈 리스트
  @ApiProperty({
    description: '생성할 굿즈 리스트',
    type: OmitType(GoodsDto, ['goodsUuid']),
    isArray: true,
  })
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => OmitType(GoodsDto, ['goodsUuid']))
  @ArrayMinSize(1, { message: '최소 1개의 굿즈가 필요합니다.' })
  // @ArrayMaxSize(100, { message: '최대 20개의 굿즈를 생성할 수 있습니다.' })
  goods!: Omit<GoodsDto, 'goodsUuid'>[];

  @ApiPropertyOptional({
    description: '부스 uuid (배열)',
    type: [String],
    example: ['0196ca79-531f-7ed2-96ae-5c42da3306fd'],
  })
  @IsUUID('7', { each: true })
  @IsArray()
  @IsOptional()
  boothUuids?: string[];
}
