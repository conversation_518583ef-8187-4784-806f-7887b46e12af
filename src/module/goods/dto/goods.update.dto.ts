import { ApiProperty, OmitType, PartialType } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString } from 'class-validator';
import { GoodsDto } from './goods.create.dto';

export class GoodsUpdateDto extends PartialType(
  OmitType(GoodsDto, ['goodsUuid']),
) {
  @ApiProperty({
    description: '굿즈 즐겨찾기 여부',
    type: Boolean,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  favorite: boolean = false;

  @ApiProperty({
    description: 'POS 작가 지정 색상 태그 (선택사항)',
    type: String,
    default: null,
  })
  @IsString()
  @IsOptional()
  colorTag?: string;
}
