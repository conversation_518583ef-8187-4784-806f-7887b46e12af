import { ApiProperty } from '@nestjs/swagger';
import {
  GoodsCategory,
  GoodsCurrencyEnum,
  GoodsSellingMethodEnum,
} from '@prisma/client';

export class SourceRes {
  @ApiProperty({
    description: '장르 uuid',
    example: '00000000-0000-0000-0000-000000000000',
  })
  sourceUuid!: string;

  @ApiProperty({
    description: '장르 이름',
    example: '장르 이름 (Genre Name)',
  })
  name!: string;
}
export class GoodsCharacterRes {
  @ApiProperty({
    description: '캐릭터 uuid',
    example: '00000000-0000-0000-0000-000000000000',
  })
  characterUuid!: string;

  @ApiProperty({
    description: '캐릭터 이름',
    example: '캐릭터 이름 (Character Name)',
  })
  name!: string;

  source!: SourceRes;
}

export class GoodsUserRes {
  @ApiProperty({
    description: '사용자 uuid',
    example: '00000000-0000-0000-0000-000000000000',
  })
  userUuid!: string;

  @ApiProperty({
    description: '사용자 이름',
    example: '사용자 이름 (User Name)',
  })
  name!: string;

  @ApiProperty({
    description:
      '배분 비율 (배분 비율은 0 ~ 100 사이의 정수) (Distribution ratio)',
    example: 20,
  })
  ratio!: number;
}

export class GoodsDetailResDto {
  @ApiProperty({
    description: '굿즈 uuid (goods uuid)',
    example: '00000000-0000-0000-0000-000000000000',
  })
  goodsUuid!: string;

  @ApiProperty({
    description: '굿즈 카테고리 uuid (goods category uuid)',
    example: '00000000-0000-0000-0000-000000000000',
  })
  goodsCategoryUuid!: string;

  @ApiProperty({
    description: '굿즈 이름 (goods name)',
    example: 'hustune miku acrylic stand',
  })
  name!: string;

  @ApiProperty({
    description: '굿즈 가격 (price)',
    example: 15000,
  })
  price!: number;

  @ApiProperty({
    description: '굿즈 수량 (quantity)',
    example: 100,
  })
  quantity!: number;

  @ApiProperty({
    description: '굿즈 이미지 URL 목록 (imageUrls)',
    example: [
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg',
    ],
  })
  imageUrls?: string[];

  @ApiProperty({
    description: '배송비 null 이면 배송상품이 아님 (shipPrice)',
    example: 3000,
    nullable: true,
  })
  shipPrice!: number | null;

  @ApiProperty({
    description: '합산 여부 (Whether the shipping cost is included)',
    example: true,
  })
  isSumUp?: boolean;

  @ApiProperty({
    description: '굿즈 설명 목록 (goods descriptions)',
    example: ['설명 1', '설명 2'],
  })
  descriptions?: string[];

  @ApiProperty({
    description: '굿즈 통화 (goods currency)',
    example: GoodsCurrencyEnum.KRW,
  })
  currency?: GoodsCurrencyEnum;

  @ApiProperty({
    description: '즐겨찾기 여부 (Whether the item is favorited)',
    example: true,
  })
  favorite?: boolean;

  @ApiProperty({
    description: '색상 태그 (color tag)',
    example: 'red',
  })
  colorTag?: string;

  @ApiProperty({
    description: '활성화 여부 (Whether the item is activated)',
    example: true,
  })
  isActivated?: boolean;

  @ApiProperty({
    description: '판매 방식 (selling methods)',
    enum: GoodsSellingMethodEnum,
    isArray: true,
    example: [GoodsSellingMethodEnum.ONLINE, GoodsSellingMethodEnum.IN_PLACE],
  })
  sellingMethods?: GoodsSellingMethodEnum[];

  @ApiProperty({
    description: '배송비 (delivery fee)',
    example: 3000,
  })
  deliveryFee?: number;

  @ApiProperty({
    description: '굿즈 카테고리 정보 (goods category)',
    isArray: true,
    example: [
      {
        goodsCategoryUuid: '00000000-0000-0000-0000-000000000000',
        name: '아크릴판 (Goods Category Name)',
      },
    ],
  })
  goodsCategory!: GoodsCategory[];

  @ApiProperty({
    description: '굿즈 캐릭터 (goods character)',
    isArray: true,
    type: GoodsCharacterRes,
  })
  goodsCharacter!: GoodsCharacterRes[];

  @ApiProperty({
    description: '굿즈 사용자 (goods user)',
    isArray: true,
    type: GoodsUserRes,
  })
  goodsUser!: GoodsUserRes[];

  @ApiProperty({
    description: '굿즈 판매 통계 (goods sales stats)',
    example: {
      viewCount: 1000,
      salesCount: 200,
      totalSalesAmount: 3000000,
    },
  })
  goodsStats?: {
    viewCount: number;
    salesCount: number;
    totalSalesAmount: number;
  };
}
