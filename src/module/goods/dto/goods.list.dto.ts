import { TransformArray } from '@common/decorators/transform-array.decorator';
import { PaginationDto } from '@common/dto/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';
import { GoodsSellingMethodEnum } from '@prisma/client';
import { IsArray, IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';

export class GoodsListForBoothInformationDto {
  @ApiProperty({
    description: '굿즈 이름',
    type: String,
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;
}

export class GoodsListDto extends PaginationDto {
  @ApiProperty({
    description: '굿즈 이름',
    type: String,
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: '종류 (굿즈 종류 uuid)',
    type: String,
    required: false,
    isArray: true,
  })
  @TransformArray()
  @IsUUID('7', { each: true })
  @IsArray()
  @IsOptional()
  goodsCategoryUuids?: string[];

  @ApiProperty({
    description: '캐릭터 (캐릭터 uuid)',
    type: String,
    required: false,
    isArray: true,
  })
  @TransformArray()
  @IsUUID('7', { each: true })
  @IsArray()
  @IsOptional()
  characterUuids?: string[];

  @ApiProperty({
    description: '부스 (부스 uuid)',
    type: String,
    required: false,
    isArray: true,
  })
  @TransformArray()
  @IsUUID('7', { each: true })
  @IsArray()
  @IsOptional()
  boothUuids?: string[];

  @ApiProperty({
    description: '판매 방법',
    type: GoodsSellingMethodEnum,
    enum: GoodsSellingMethodEnum,
    enumName: 'GoodsSellingMethodEnum',
    required: false,
    isArray: true,
  })
  @TransformArray()
  @IsEnum(GoodsSellingMethodEnum, { each: true })
  @IsArray()
  @IsOptional()
  sellingMethods?: GoodsSellingMethodEnum[];

  @ApiProperty({
    description: '상태',
    type: 'string',
    enum: ['active', 'inactive'],
    required: false,
  })
  @IsString()
  @IsOptional()
  status?: 'active' | 'inactive';
}
