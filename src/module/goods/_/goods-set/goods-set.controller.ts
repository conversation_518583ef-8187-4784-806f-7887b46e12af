import { User } from '@common/decorators/jwk.decorators';
import type { RequestUser } from '@common/types/token.types';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiTags,
} from '@nestjs/swagger';
import { GoodsSetCreateDto } from './dto/goods-set.create.dto';
import { GoodsSetListDto } from './dto/goods-set.list.dto';
import { GoodsSetUpdateDto } from './dto/goods-set.update.dto';
import { GoodsSetService } from './goods-set.service';

@ApiTags('Goods Set')
@ApiBearerAuth()
@Controller('goods-sets')
export class GoodsSetController {
  constructor(private readonly goodsSetService: GoodsSetService) {}

  @Get()
  @ApiOperation({ summary: '상품 세트 목록 조회' })
  async getGoodsSetList(
    @User() user: RequestUser,
    @Query() dto: GoodsSetListDto,
  ) {
    return this.goodsSetService.getGoodsSetList(user.id, dto);
  }

  @Get(':goodsSetUuid')
  @ApiOperation({ summary: '상품 세트 상세 조회' })
  async getGoodsSetDetail(
    @User() user: RequestUser,
    @Param('goodsSetUuid', ParseUUIDPipe) goodsSetUuid: string,
  ) {
    return this.goodsSetService.getGoodsSetDetail(user.id, goodsSetUuid);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '상품 세트 생성' })
  async createGoodsSet(
    @User() user: RequestUser,
    @Body() dto: GoodsSetCreateDto,
  ) {
    return this.goodsSetService.createGoodsSet(user.id, dto);
  }

  @Patch(':goodsSetUuid')
  @ApiOperation({ summary: '상품 세트 수정' })
  @ApiParam({
    name: 'goodsSetUuid',
    description: '수정할 상품 세트의 UUID',
    type: String,
  })
  async updateGoodsSet(
    @User() user: RequestUser,
    @Param('goodsSetUuid', ParseUUIDPipe) goodsSetUuid: string,
    @Body() dto: GoodsSetUpdateDto,
  ) {
    return this.goodsSetService.updateGoodsSet(user.id, goodsSetUuid, dto);
  }

  @Delete(':goodsSetUuid')
  @ApiOperation({ summary: '상품 세트 삭제' })
  async deleteGoodsSet(
    @User() user: RequestUser,
    @Param('goodsSetUuid', ParseUUIDPipe) goodsSetUuid: string,
  ) {
    await this.goodsSetService.deleteGoodsSet(user.id, goodsSetUuid);
  }
}
