import { Injectable } from '@nestjs/common';
import { PrismaService } from '@provider/database/prisma.service';
import type { GoodsSetCreateDto } from './dto/goods-set.create.dto';
import type { GoodsSetListDto } from './dto/goods-set.list.dto';
import type { GoodsSetUpdateDto } from './dto/goods-set.update.dto';

@Injectable()
export class GoodsSetService {
  constructor(private readonly prismaService: PrismaService) {}

  async getGoodsSetList(userUuid: string, dto: GoodsSetListDto) {
    return await this.prismaService.goodsSet.findMany({
      where: {
        GoodsSetPivot: {
          some: { goods: { goodsUser: { some: { userUuid } } } },
        },
        name: { contains: dto.name },
      },
      include: { GoodsSetPivot: { include: { goods: true } } },
    });
  }

  async getGoodsSetDetail(userUuid: string, goodsSetUuid: string) {
    const goodsSet = await this.prismaService.goodsSet.findFirst({
      where: {
        goodsSetUuid,
        GoodsSetPivot: {
          some: { goods: { goodsUser: { some: { userUuid } } } },
        },
      },
      include: { GoodsSetPivot: { include: { goods: true } } },
    });

    if (!goodsSet) {
      throw new Error('존재하지 않는 세트입니다.');
    }

    return goodsSet;
  }

  async createGoodsSet(userUuid: string, dto: GoodsSetCreateDto) {
    const goodsUuids = dto.goods.map((g) => g.goodsUuid);
    const userGoodsCount = await this.prismaService.goods.count({
      where: {
        goodsUuid: { in: goodsUuids },
        goodsUser: { some: { userUuid } },
      },
    });

    if (userGoodsCount !== goodsUuids.length) {
      throw new Error(
        '일부 상품이 사용자에게 속하지 않거나 존재하지 않습니다.',
      );
    }

    return await this.prismaService.goodsSet.create({
      data: {
        name: dto.name,
        price: dto.price,
        GoodsSetPivot: {
          create: dto.goods.map(({ goodsUuid, amount }) => ({
            goodsUuid,
            amount,
          })),
        },
      },
    });
  }

  async updateGoodsSet(
    userUuid: string,
    goodsSetUuid: string,
    dto: GoodsSetUpdateDto,
  ) {
    const goodsSet = await this.prismaService.goodsSet.findFirst({
      where: {
        goodsSetUuid,
        GoodsSetPivot: {
          some: { goods: { goodsUser: { some: { userUuid } } } },
        },
      },
    });
    if (!goodsSet) {
      throw new Error('존재하지 않는 세트입니다.');
    }

    const goodsUuids = dto.goods.map((g) => g.goodsUuid);
    const userGoodsCount = await this.prismaService.goods.count({
      where: {
        goodsUuid: { in: goodsUuids },
        goodsUser: { some: { userUuid } },
      },
    });
    if (userGoodsCount !== goodsUuids.length) {
      throw new Error(
        '일부 상품이 사용자에게 속하지 않거나 존재하지 않습니다.',
      );
    }

    return await this.prismaService.goodsSet.update({
      where: { goodsSetUuid },
      data: {
        name: dto.name,
        price: dto.price,
        GoodsSetPivot: {
          updateMany: dto.goods.map(({ goodsUuid, amount }) => ({
            where: { goodsUuid, goodsSetUuid },
            data: { goodsUuid, amount },
          })),
        },
      },
      include: { GoodsSetPivot: { include: { goods: true } } },
    });
  }

  async deleteGoodsSet(userUuid: string, goodsSetUuid: string) {
    const goodsSet = await this.prismaService.goodsSet.findFirst({
      where: {
        goodsSetUuid,
        GoodsSetPivot: {
          some: { goods: { goodsUser: { some: { userUuid } } } },
        },
      },
    });

    if (!goodsSet) {
      throw new Error('존재하지 않는 세트이거나 삭제 권한이 없습니다.');
    }

    // Delete related GoodsSetPivot entries first if cascade delete is not set up
    // Assuming cascade delete is configured in Prisma schema for GoodsSetPivot
    await this.prismaService.goodsSet.delete({
      where: { goodsSetUuid },
    });

    return { message: 'Goods set deleted successfully.' };
  }
}
