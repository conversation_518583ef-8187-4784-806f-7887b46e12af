import { GoodsDto } from '@module/goods/dto/goods.create.dto';
import { ApiProperty, OmitType, PickType } from '@nestjs/swagger';
import { IsArray, IsInt, IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class GoodsSetDto {
  @ApiProperty({
    description: '굿즈 세트 UUID',
    type: String,
    example: '00000000-0000-0000-0000-0000',
  })
  @IsUUID('7')
  @IsNotEmpty()
  goodsSetUuid!: string;

  @ApiProperty({
    description: '굿즈 세트 이름',
    type: String,
    example: '굿즈 세트 이름',
  })
  @IsString()
  @IsNotEmpty()
  name!: string;

  @ApiProperty({
    description: '굿즈 세트 가격',
    type: Number,
    example: 123124,
  })
  @IsInt()
  @IsNotEmpty()
  price!: number;

  @ApiProperty({
    description: '굿즈 세트에 포함되는 굿즈 목록',
    type: () => GoodsSetIncludeGoodsDto,
    example: [{ goodsUuid: '00000000-0000-0000-0000-0000', amount: 1 }],
  })
  @IsNotEmpty()
  @IsArray()
  goods!: GoodsSetIncludeGoodsDto[];
}

export class GoodsSetCreateDto extends OmitType(GoodsSetDto, [
  'goodsSetUuid',
]) {}

class GoodsSetIncludeGoodsDto extends PickType(GoodsDto, ['goodsUuid']) {
  @ApiProperty({
    description: '굿즈 수량',
    type: Number,
    example: 1,
  })
  @IsInt()
  @IsNotEmpty()
  amount!: number;
}
