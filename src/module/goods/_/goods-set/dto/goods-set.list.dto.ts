import { InfinityScroll } from '@common/dto/infinity-scroll';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';

export class GoodsSetListDto extends InfinityScroll<string> {
  @ApiProperty({
    description: 'goods set uuid (굿즈 세트 uuid)',
    type: String,
    required: false,
  })
  @IsUUID()
  @IsOptional()
  cursor: string | undefined;

  @ApiProperty({
    description: '세트명(검색용)',
    type: String,
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;
}
