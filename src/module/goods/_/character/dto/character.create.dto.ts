import { ApiProperty, PickType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';

export class CharacterDto {
  @ApiProperty({
    description: '캐릭터 이름',
    type: String,
    example: '하츠네 미쿠',
  })
  @IsString()
  @IsNotEmpty()
  name!: string;

  @ApiProperty({
    description: '캐릭터 이미지',
    type: String,
    example: 'https://sherry.gg/favicon.ico',
  })
  @IsString()
  @IsNotEmpty()
  image!: string;

  @ApiProperty({
    description: '원전 uuid',
    type: String,
    example: '00000000-0000-0000-0000-000000000000',
  })
  @IsUUID('7')
  @IsNotEmpty()
  sourceUuid!: string;

  @ApiProperty({
    description: '캐릭터 uuid',
    type: String,
    example: '00000000-0000-0000-0000-000000000000',
  })
  @IsUUID('7')
  @IsNotEmpty()
  characterUuid!: string;
}

export class CharacterCreateDto {
  @ApiProperty({
    description: '생성할 캐릭터 리스트',
    type: [CharacterDto],
    example: [
      {
        name: '하츠네 미쿠',
        sourceUuid: '00000000-0000-0000-0000-000000000000',
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PickType(CharacterDto, ['name', 'sourceUuid', 'image']))
  @IsNotEmpty()
  characters!: Pick<CharacterDto, 'name' | 'sourceUuid' | 'image'>[];
}
