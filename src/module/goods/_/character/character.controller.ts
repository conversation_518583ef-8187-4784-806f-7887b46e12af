import { Body, Controller, Get, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { CharacterService } from './character.service';
import { CharacterCreateDto } from './dto/character.create.dto';

@ApiBearerAuth()
@Controller('characters')
export class CharacterController {
  constructor(private readonly characterService: CharacterService) {}

  @Get()
  @ApiOperation({
    summary: '캐릭터 조회',
    description: '캐릭터 조회',
  })
  getCharacters() {
    return this.characterService.getCharacters();
  }

  @Post()
  @ApiOperation({
    summary: '캐릭터 생성',
    description: '캐릭터 생성',
  })
  createCharacter(@Body() dto: CharacterCreateDto) {
    return this.characterService.createCharacter(dto);
  }
}
