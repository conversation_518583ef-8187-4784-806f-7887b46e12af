import { SynonymService } from '@module/synonym/synonym.service';
import { Injectable } from '@nestjs/common';
import { PrismaService } from '@provider/database/prisma.service';
import type { CharacterCreateDto } from './dto/character.create.dto';

@Injectable()
export class CharacterService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly synonymService: SynonymService,
  ) {}

  async getCharacters() {
    return await this.prismaService.character.findMany({
      orderBy: { name: 'asc' },
    });
  }
  async createCharacter(dto: CharacterCreateDto) {
    const existCharacter = await this.prismaService.character.findFirst({
      where: {
        name: { in: dto.characters.map((character) => character.name) },
      },
    });

    if (existCharacter) {
      throw new Error('이미 존재하는 캐릭터입니다.');
    }

    const characters = await this.prismaService.character.createManyAndReturn({
      data: dto.characters.map((character) => ({
        name: character.name,
        sourceUuid: character.sourceUuid,
        image: character.image,
      })),
    });

    await Promise.all(
      characters.map((character) =>
        this.synonymService.createSynonyms({
          synonymModel: 'character',
          names: [character.name],
          targetUuid: character.characterUuid,
        }),
      ),
    );

    return characters;
  }
}
