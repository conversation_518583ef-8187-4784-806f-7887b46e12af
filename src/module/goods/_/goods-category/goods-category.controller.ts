import { IsPublic } from '@common/decorators/public.decorator';
import { Body, Controller, Get, Post } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import {
  GoodsCategoryCreateDto,
  GoodsCategoryDto,
} from './dto/goods-category.create.dto';
import { GoodsCategoryService } from './goods-category.service';

@ApiTags('Goods Category')
@Controller('goods-category')
@IsPublic()
export class GoodsCategoryController {
  constructor(private readonly goodsCategoryService: GoodsCategoryService) {}

  @Get()
  @ApiOperation({
    summary: '굿즈 카테고리 목록 조회',
    description: '모든 굿즈 카테고리 목록을 조회합니다.',
  })
  @ApiOkResponse({
    description: '굿즈 카테고리 목록',
    type: GoodsCategoryDto,
    isArray: true,
  })
  getGoodsCategories() {
    return this.goodsCategoryService.getGoodsCategories();
  }

  @Post()
  @ApiOperation({
    summary: '굿즈 카테고리 생성',
    description: '굿즈 카테고리를 생성합니다.',
  })
  @ApiOkResponse({
    description: '굿즈 카테고리 생성 성공',
    type: GoodsCategoryDto,
  })
  createGoodsCategory(@Body() dto: GoodsCategoryCreateDto) {
    return this.goodsCategoryService.createGoodsCategory(dto);
  }
}
