import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class GoodsCategoryDto {
  @ApiProperty({
    description: '굿즈 카테고리 UUID',
    type: String,
    example: '019665a3-0dde-7891-b090-cc2c45150d61',
  })
  @IsUUID('7', { message: '유효한 UUID 형식이 아닙니다.' })
  @IsNotEmpty({ message: '굿즈 카테고리 UUID는 필수 입력 항목입니다.' })
  goodsCategoryUuid!: string;

  @ApiProperty({
    description: '굿즈 카테고리 이름',
    type: String,
    example: '아크릴 스탠드',
  })
  @IsString({
    message: '굿즈 카테고리 이름은 문자열이어야 합니다.',
  })
  @IsNotEmpty({ message: '굿즈 카테고리 이름은 필수 입력 항목입니다.' })
  name!: string;
}

export class GoodsCategoryCreateDto {
  @ApiProperty({
    description: '굿즈 카테고리 이름',
    type: String,
    example: ['아크릴 스탠드'],
    isArray: true,
  })
  @IsArray()
  @IsString({
    each: true,
    message: '굿즈 카테고리 이름은 문자열이어야 합니다.',
  })
  @IsNotEmpty({ message: '굿즈 카테고리 이름은 필수 입력 항목입니다.' })
  name!: string[];
}
