import { SynonymService } from '@module/synonym/synonym.service';
import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '@provider/database/prisma.service';
import type { GoodsCategoryCreateDto } from './dto/goods-category.create.dto';

@Injectable()
export class GoodsCategoryService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly synonymService: SynonymService,
  ) {}

  async getGoodsCategories() {
    return await this.prismaService.goodsCategory.findMany();
  }

  async createGoodsCategory(dto: GoodsCategoryCreateDto) {
    const existCategory = await this.prismaService.goodsCategory.findFirst({
      where: { name: { in: dto.name } },
    });

    if (existCategory) {
      throw new BadRequestException('이미 존재하는 카테고리입니다.');
    }

    const goodsCategories =
      await this.prismaService.goodsCategory.createManyAndReturn({
        data: dto.name.map((name) => ({ name })),
      });

    await Promise.all(
      goodsCategories.map((category) =>
        this.synonymService.createSynonyms({
          synonymModel: 'goodsCategory',
          names: [category.name],
          targetUuid: category.goodsCategoryUuid,
        }),
      ),
    );

    return goodsCategories;
  }
}
