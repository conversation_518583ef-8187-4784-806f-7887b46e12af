import { SourceService } from './source.service';
import { Body, Controller, Get, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { SourceCreateDto } from './dto/source.dto';

@ApiBearerAuth()
@Controller('sources')
export class SourceController {
  constructor(private readonly sourceService: SourceService) {}

  @Get()
  @ApiOperation({
    summary: '원전 조회',
    description: '원전 조회',
  })
  getSources() {
    return this.sourceService.getSources();
  }

  @Post()
  @ApiOperation({
    summary: '원전 생성',
    description: '원전 생성',
  })
  createSource(@Body() dto: SourceCreateDto) {
    return this.sourceService.createSource(dto);
  }
}
