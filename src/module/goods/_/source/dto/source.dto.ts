import { ApiProperty, PickType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';

export class SourceDto {
  @ApiProperty({
    description: '원전 uuid',
    type: String,
    example: '00000000-0000-0000-0000-000000000000',
  })
  @IsUUID('7')
  sourceUuid!: string;

  @ApiProperty({
    description: '원전 이름',
    type: String,
    example: '보컬로이드',
  })
  @IsString()
  @IsNotEmpty()
  name!: string;

  @ApiProperty({
    description: '원전 이미지',
    type: String,
    example: 'https://sherry.gg/favicon.ico',
  })
  @IsString()
  @IsNotEmpty()
  image!: string;
}

export class SourceCreateDto {
  @ApiProperty({
    description: '생성할 장르 리스트',
    type: [SourceDto],
    example: [
      {
        name: '보컬로이드',
        image: 'https://sherry.gg/favicon.ico',
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PickType(SourceDto, ['name', 'image']))
  @IsNotEmpty()
  sources!: Pick<SourceDto, 'name' | 'image'>[];
}
