import { SynonymService } from '@module/synonym/synonym.service';
import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '@provider/database/prisma.service';
import type { SourceCreateDto } from './dto/source.dto';

@Injectable()
export class SourceService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly synonymService: SynonymService,
  ) {}

  async getSources() {
    return await this.prismaService.source.findMany();
  }
  async createSource(dto: SourceCreateDto) {
    const existSource = await this.prismaService.source.findFirst({
      where: { name: { in: dto.sources.map((source) => source.name) } },
    });

    if (existSource) {
      throw new BadRequestException('이미 존재하는 소스입니다.');
    }

    const source = await this.prismaService.source.createManyAndReturn({
      data: dto.sources.map((source) => ({
        name: source.name,
        image: source.image,
      })),
    });

    await Promise.all(
      source.map((source) =>
        this.synonymService.createSynonyms({
          synonymModel: 'source',
          names: [source.name],
          targetUuid: source.sourceUuid,
        }),
      ),
    );

    return source;
  }
}
