import { CookiesCsrfToken } from '@common/decorators/cookies.decorator';
import { IsPublic } from '@common/decorators/public.decorator';
import { Controller, Get, Param, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { GoodsListForBoothInformationDto } from './dto/goods.list.dto';
import { GoodsService } from './goods.service';

@ApiTags('Goods')
@Controller('goods')
export class GoodsController {
  constructor(private readonly goodsService: GoodsService) {}

  @ApiOperation({
    summary: '리뉴얼 부스 페이지 내 상품 리스트',
    description: '',
  })
  @IsPublic()
  @Get('booth-information/:boothUuid')
  getGoodsForBoothInformation(
    @Query() dto: GoodsListForBoothInformationDto,
    @Param('boothUuid') boothUuid: string,
  ) {
    return this.goodsService.getGoodsForBoothInformation({
      boothUuid,
      dto,
    });
  }

  @ApiOperation({
    summary: '굿즈 상세 정보',
    description: '',
  })
  @IsPublic()
  @Get(':goodsUuid')
  getGoodsDetail(
    @Param('goodsUuid') id: string,
    @CookiesCsrfToken() csrfToken?: string,
  ) {
    return this.goodsService.getGoodsDetail(id, csrfToken);
  }
}
