import { UnauthorizedUserMessage } from '@common/types/error-res.type';
import { RequestUser } from '@common/types/token.types';
import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { PrismaService } from '@provider/database/prisma.service';
import { RedisCacheService } from '@provider/redis/redis.service';
import { isDefined } from 'class-validator';
import { uniq } from 'lodash';
import { Readable } from 'stream';
import { GoodsCreateDto } from './dto/goods.create.dto';
import {
  GoodsListDto,
  GoodsListForBoothInformationDto,
} from './dto/goods.list.dto';
import { GoodsUpdateDto } from './dto/goods.update.dto';

@Injectable()
export class GoodsService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly redisCacheService: RedisCacheService,
  ) {}

  private async updateGoodsViewCount(goodsUuid: string, csrfToken: string) {
    const key = `goods:${goodsUuid}:${csrfToken}`;
    const views = await this.redisCacheService.get(key);
    if (views) {
      return false;
    }
    await this.redisCacheService.set(key, 'Exist', 60 * 60 * 24);
    return true;
  }
  async goodsDelete(user: RequestUser, goodsUuid: string) {
    const existGoods = await this.prismaService.goods.findUnique({
      where: {
        goodsUuid,
        goodsUser: {
          some: {
            userUuid: user.id,
          },
        },
      },
    });

    if (!existGoods) {
      throw new NotFoundException('존재하지 않는 굿즈입니다.');
    }
    // TODO: 굿즈 이전 판매기록이나 삭제 이전 확인해야 되는 정보 확인 필요
    return this.prismaService.goods.delete({
      where: { goodsUuid: existGoods.goodsUuid },
    });
  }

  async getGoodsDetail(id: string, csrfToken?: string) {
    const goods = await this.prismaService.goods.findUnique({
      where: { goodsUuid: id },
      include: {
        goodsCategory: true,
        goodsCharacterPivot: { include: { character: true } },
        goodsUser: { include: { user: true } },
        goodsBoothPivot: {
          include: { booth: true },
        },
        goodsStats: true,
      },
    });
    if (!goods) {
      throw new NotFoundException('존재하지 않는 굿즈입니다.');
    }

    if (csrfToken) {
      const 카운트올려 = await this.updateGoodsViewCount(
        goods.goodsUuid,
        csrfToken,
      );
      if (카운트올려) {
        await this.prismaService.goodsStats.update({
          where: { goodsUuid: goods.goodsUuid },
          data: { viewCount: { increment: 1 } },
        });
      }
    }

    return goods;
  }

  async goodsUpdate(user: RequestUser, dto: GoodsUpdateDto, goodsUuid: string) {
    const existGoods = await this.prismaService.goods.findUnique({
      where: {
        goodsUuid,
        goodsUser: {
          some: {
            userUuid: user.id,
          },
        },
      },
    });
    if (!existGoods) {
      throw new NotFoundException('존재하지 않는 굿즈입니다.');
    }

    await this.prismaService.$transaction([
      this.prismaService.goodsCharacterPivot.deleteMany({
        where: { goodsUuid: existGoods.goodsUuid },
      }),
      this.prismaService.goodsUserRatio.deleteMany({
        where: { goodsUuid: existGoods.goodsUuid },
      }),
      // 굿즈 업데이트
      this.prismaService.goods.update({
        where: { goodsUuid: existGoods.goodsUuid },
        data: {
          ...(isDefined(dto.goodsCategoryUuid) && {
            goodsCategoryUuid: dto.goodsCategoryUuid,
          }),
          ...(isDefined(dto.name) && { name: dto.name }),
          ...(isDefined(dto.price) && { price: dto.price }),
          ...(isDefined(dto.quantity) && { quantity: dto.quantity }),
          ...(isDefined(dto.imageUrls) && { imageUrls: dto.imageUrls }),
          ...(isDefined(dto.descriptions) && {
            descriptions: dto.descriptions,
          }),
          ...(isDefined(dto.favorite) && { favorite: dto.favorite }),
          ...(isDefined(dto.colorTag) && { colorTag: dto.colorTag }),
          ...(isDefined(dto.isActivated) && { isActivated: dto.isActivated }),
          ...(isDefined(dto.sellingMethods) && {
            sellingMethods: dto.sellingMethods,
          }),
          ...(isDefined(dto.deliveryFee) && { deliveryFee: dto.deliveryFee }),
        },
      }),
      ...(isDefined(dto.characterUuids)
        ? [
            this.prismaService.goodsCharacterPivot.createMany({
              data: dto.characterUuids.map((characterUuid) => ({
                goodsUuid: existGoods.goodsUuid,
                characterUuid,
              })),
            }),
          ]
        : []),
      ...(isDefined(dto.participatedUsers)
        ? [
            this.prismaService.goodsUserRatio.createMany({
              data: dto.participatedUsers.map((user) => ({
                goodsUuid: existGoods.goodsUuid,
                userUuid: user.userUuid,
                ratio: user.ratio,
              })),
            }),
          ]
        : []),
    ]);
  }
  async goodsList(user: RequestUser, dto: GoodsListDto) {
    const whereCondition = {
      goodsUser: { some: { userUuid: user.id } },
      ...(dto.name && { name: { contains: dto.name } }),
      ...(dto.sellingMethods?.length && {
        sellingMethods: { hasSome: dto.sellingMethods },
      }),
      ...(dto.goodsCategoryUuids?.length && {
        goodsCategoryUuid: { in: dto.goodsCategoryUuids },
      }),
      ...(dto.characterUuids?.length && {
        goodsCharacterPivot: {
          some: { characterUuid: { in: dto.characterUuids } },
        },
      }),
      ...(dto.boothUuids?.length && {
        goodsBoothPivot: { some: { boothUuid: { in: dto.boothUuids } } },
      }),
      ...(dto.status && { isActivated: dto.status === 'active' }),
    };

    const [goods, count] = await Promise.all([
      this.prismaService.goods.findMany({
        where: whereCondition,
        select: {
          goodsUuid: true,
          name: true,
          price: true,
          sellingMethods: true,
          isActivated: true,
          goodsCharacterPivot: { include: { character: true } },
          goodsBoothPivot: {
            include: { booth: true },
            where: dto.boothUuids?.length
              ? { boothUuid: { in: dto.boothUuids } }
              : undefined,
          },
          goodsCategory: { select: { name: true } },
          quantity: true,
        },
        orderBy: { goodsUuid: Prisma.SortOrder.desc },
        ...dto.toPrismaPaging(),
      }),
      this.prismaService.goods.count({
        where: whereCondition,
      }),
    ]);
    return dto.createResponse(goods, count);
  }

  async getGoodsForBoothInformation({
    boothUuid,
    dto,
  }: {
    boothUuid: string;
    dto: GoodsListForBoothInformationDto;
  }) {
    const goods = await this.prismaService.goodsBoothPivot.findMany({
      where: {
        boothUuid,
        ...(dto.name && { goods: { name: { contains: dto.name } } }),
      },
      include: {
        goods: {
          include: {
            goodsUser: {
              select: { user: { select: { handle: true, nickname: true } } },
            },
            goodsCategory: { select: { name: true } },
          },
        },
      },
    });

    return goods.map((pivot) => ({
      goods: pivot.goods,
      isPickupGoods: pivot.prepareCount > 0,
      isSoldOut: pivot.orderedCount >= pivot.quantity,
    }));
  }

  async goodsCountForCreator(user: RequestUser) {
    const [totalCount, activeCount] = await Promise.all([
      this.prismaService.goods.count({
        where: { goodsUser: { some: { userUuid: user.id } } },
      }),
      this.prismaService.goods.count({
        where: {
          goodsUser: { some: { userUuid: user.id } },
          isActivated: true,
        },
      }),
    ]);
    return { totalCount, activeCount };
  }

  // 대용량 데이터를 스트리밍 형태로 제공
  async goodsListStream(user: RequestUser): Promise<Readable> {
    const stream = new Readable({ objectMode: true });
    stream._read = () => {};

    let skip = 0;
    const batchSize = 1000;
    let hasMore = true;

    // 백그라운드에서 데이터를 가져와 스트림으로 전송
    const processInBatches = async () => {
      while (hasMore) {
        const batch = await this.prismaService.goods.findMany({
          where: {
            goodsUser: { some: { userUuid: user.id } },
          },
          include: {
            goodsCharacterPivot: { include: { character: true } },
            goodsBoothPivot: { select: { booth: { select: { name: true } } } },
          },
          skip,
          take: batchSize,
          orderBy: {
            goodsUuid: 'asc',
          },
        });

        if (batch.length === 0) {
          hasMore = false;
          stream.push(null); // 스트림 종료
          break;
        }

        for (const item of batch) {
          stream.push(JSON.stringify(item) + '\n');
        }

        skip += batch.length;
      }
    };

    processInBatches().catch((err) => {
      console.error('Error in stream processing:', err);
      stream.destroy(err);
    });

    return stream;
  }

  async goodsCreate(user: RequestUser, dto: GoodsCreateDto) {
    const existUser = await this.prismaService.user.findUnique({
      where: { userUuid: user.id },
    });
    if (!existUser) {
      throw new UnauthorizedException(UnauthorizedUserMessage);
    }
    const uuids = dto.goods.reduce(
      (
        acc: {
          categoryUuid: string[];
          characterUuid: string[];
        },
        item,
      ) => {
        acc.categoryUuid.push(item.goodsCategoryUuid);
        acc.characterUuid.push(...item.characterUuids);
        return acc;
      },
      {
        categoryUuid: [],
        characterUuid: [],
      },
    );
    const categoryUuid = uniq(uuids.categoryUuid);
    const characterUuid = uniq(uuids.characterUuid);
    const existCategory = await this.prismaService.goodsCategory.findMany({
      where: { goodsCategoryUuid: { in: categoryUuid } },
    });

    if (existCategory.length !== categoryUuid.length) {
      throw new BadRequestException('존재하지 않는 굿즈 종류입니다.');
    }

    const existCharacter = await this.prismaService.character.findMany({
      where: { characterUuid: { in: characterUuid } },
    });

    if (existCharacter.length !== characterUuid.length) {
      throw new BadRequestException('존재하지 않는 캐릭터입니다.');
    }

    await this.prismaService.$transaction((tx) =>
      Promise.all(
        dto.goods.map(async (item) => {
          const goods = await tx.goods.create({
            data: {
              goodsCategoryUuid: item.goodsCategoryUuid,
              name: item.name,
              price: item.price,
              quantity: item.quantity,
              imageUrls: item.imageUrls,
              isSumUp: false,
              descriptions: item.descriptions,
              goodsCharacterPivot: {
                createMany: {
                  data: item.characterUuids.map((characterUuid) => ({
                    characterUuid,
                  })),
                },
              },
              colorTag: item.colorTag,
              favorite: item.favorite,
              deliveryFee: item.deliveryFee,
              sellingMethods: item.sellingMethods,
              goodsUser: { createMany: { data: item.participatedUsers } },
              ...(dto.boothUuids && {
                goodsBoothPivot: {
                  createMany: {
                    data: dto.boothUuids.map((boothUuid) => ({
                      boothUuid,
                      quantity: 0,
                    })),
                  },
                },
              }),
            },
          });
          const goodsStats = tx.goodsStats.create({
            data: { goodsUuid: goods.goodsUuid },
          });
          return [goods, goodsStats];
        }),
      ),
    );
  }
  async goodsDetail(userUuid: string, goodsUuid: string) {
    const goods = await this.prismaService.goods.findUnique({
      where: {
        goodsUuid,
        goodsUser: { some: { userUuid } },
      },
      include: {
        goodsCategory: true,
        goodsCharacterPivot: { select: { character: true } },
        goodsUser: { include: { user: true } },
        goodsStats: {
          select: {
            viewCount: true,
            salesCount: true,
          },
        },
      },
    });
    if (!goods) {
      throw new BadRequestException('존재하지 않는 굿즈입니다.');
    }

    // 현재 굿즈의 판매량
    const currentSalesCount = goods.goodsStats?.salesCount ?? 0;

    // 전체 굿즈 수
    const totalGoodsCount = await this.prismaService.goods.count({});

    // 현재 굿즈보다 판매량이 높은 굿즈 수
    const higherSalesCount = await this.prismaService.goods.count({
      where: {
        goodsStats: {
          salesCount: { gt: currentSalesCount },
        },
      },
    });

    // 랭킹 계산 (1등부터 시작)
    const rank = higherSalesCount + 1;
    const rankPercentile =
      totalGoodsCount > 0 ? Math.round((rank / totalGoodsCount) * 100) : 0;
    // 판매 전환율
    const salesConversionRate =
      ((goods.goodsStats?.salesCount ?? 0) /
        (goods.goodsStats?.viewCount ?? 1)) *
      100;
    return {
      ...goods,
      goodsStats: {
        viewCount: goods.goodsStats?.viewCount ?? 0,
        salesCount: goods.goodsStats?.salesCount ?? 0,
        rankPercentile,
        salesConversionRate: isNaN(salesConversionRate)
          ? 0
          : Math.round(salesConversionRate * 100) / 100, // 소수점 둘째 자리까지 반올림
      },
    };
  }
}
