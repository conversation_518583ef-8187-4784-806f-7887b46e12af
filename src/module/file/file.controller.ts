import { IsPublic } from '@common/decorators/public.decorator';
import { Body, Controller, Put } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { StorageService } from '@provider/storage/storage.service';
import { FileUploadDto } from './dto/file.upload.dto';

// @ApiBearerAuth()
@ApiTags('파일 업로드')
@Controller('file')
export class FileController {
  constructor(private readonly storageService: StorageService) {}

  @IsPublic()
  @Put('upload')
  async uploadFile(@Body() dto: FileUploadDto) {
    return this.storageService.getPresignedUrl(dto.bucketName, dto.fileName);
  }
}
