import { ApiProperty } from '@nestjs/swagger';
import { BucketNames } from '@provider/storage/storage.service';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';

export class FileUploadDto {
  @ApiProperty({
    type: 'string',
    description: '파일 이름',
  })
  @IsString()
  @IsNotEmpty()
  fileName!: string;

  /// 버킷 이름
  @ApiProperty({
    type: 'string',
    description: ['버킷 이름', BucketNames.map((name) => name)].join('\\n'),
    enum: BucketNames,
  })
  @IsEnum(BucketNames)
  @IsNotEmpty()
  bucketName!: BucketNames;
}
