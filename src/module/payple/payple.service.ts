import { PaypleCardVerEnum } from '@common/types/payment.types';
import {
  AccountVerificationRes,
  PartnerAuthenticationRes,
  PaypleAuthenticationOutcome,
  PaypleCardRes,
  PaypleCreateLinkRequest,
  PaypleCreateLinkResponse,
  PayplePartnerRes,
  PaypleTransferRes,
  TransferExecutionRes,
  TransferRequestWaitingForBillingKeyRes,
} from '@common/types/payple.types';
import { AppConfigService } from '@config/app/config.service';
import { Injectable } from '@nestjs/common';
import { PayMethodEnum } from '@prisma/client';
import axios from 'axios';
import dayjs from 'dayjs';

@Injectable()
export class PaypleService {
  constructor(private readonly appConfigService: AppConfigService) {}

  // 결제 승인 요청
  requestApproval(arg: {
    PCD_AUTH_KEY: string;
    PCD_PAY_REQKEY: string;
    url: string;
    PCD_PAYER_ID?: string;
  }) {
    return axios<PaypleAuthenticationOutcome>({
      url: arg.url,
      method: 'post',
      data: {
        PCD_CST_ID: this.appConfigService.paypleCstId,
        PCD_CUST_KEY: this.appConfigService.paypleCustKey,
        PCD_AUTH_KEY: arg.PCD_AUTH_KEY,
        PCD_PAY_REQKEY: arg.PCD_PAY_REQKEY,
        PCD_PAYER_ID: arg.PCD_PAYER_ID,
      },
    });
  }

  private getPayple(payType: PayMethodEnum): PaypleCardVerEnum {
    let val = PaypleCardVerEnum.CARD;

    if (payType !== PayMethodEnum.card) {
      val = PaypleCardVerEnum.PASSWORD_BILLING;
    }

    return val;
  }

  getPgData(arg: {
    payType: PayMethodEnum;
    goodsTitle: string;
    totalPrice: number;
    orderUuid: string;
    name?: string;
    mobileNumber?: string;
    email?: string;
    payplePayerId?: string;
  }) {
    if (arg.totalPrice <= 0) {
      return null;
    }
    const obj: {
      clientKey: string;
      PCD_PAY_TYPE: string;
      PCD_PAY_WORK: string;
      PCD_CARD_VER: string;
      PCD_PAY_GOODS: string;
      PCD_PAY_TOTAL: number;
      PCD_RST_URL: string;
      PCD_PAY_OID: string;
      PCD_PAYER_NAME?: string;
      PCD_PAYER_HP?: string;
      PCD_PAYER_EMAIL?: string;
      PCD_PAY_TAXTOTAL?: number;
      PCD_SIMPLE_FLAG?: string;
      PCD_PAYER_AUTHTYPE?: string;
      PCD_PAYER_ID?: string;
    } = {
      clientKey: this.appConfigService.paypleClientKey,
      PCD_PAY_TYPE: arg.payType,
      // 승인 요청 방식이며 CERT 고정값으로 설정합니다.
      PCD_PAY_WORK: 'CERT',
      // 카드 결제수단 중 앱카드, 정기(빌링), 비밀번호 간편결제 방식을 선택합니다.
      PCD_CARD_VER: this.getPayple(arg.payType),
      // 상품명입니다.
      PCD_PAY_GOODS: arg.goodsTitle,
      // 총 결제금액입니다.
      PCD_PAY_TOTAL: arg.totalPrice,
      // 결제 정보가 성공적으로 입력된 경우, 인증 결과가 POST 방식으로 전송됩니다. http://${this.appConfigService.domain}
      PCD_RST_URL: `https://backend.${this.appConfigService.domain}/webhook/callback`,
      PCD_PAY_OID: arg.orderUuid,
      // 구매자 이름
      PCD_PAYER_NAME: arg.name,
      // 구매자 번호
      PCD_PAYER_HP: arg.mobileNumber,
      PCD_PAYER_EMAIL: arg.email,
      // PCD_PAY_TAXTOTAL: vatTaxCalculation(arg.totalPrice).vatAmount,
    };

    if (arg.payType === PayMethodEnum.account) {
      obj.PCD_SIMPLE_FLAG = 'Y';
      obj.PCD_PAYER_AUTHTYPE = 'pwd';
      obj.PCD_PAYER_ID = arg.payplePayerId;
    }
    // else if (arg.payType === PaymentMethodType.CARD_PWA) { // TODO: 현재 이건 없음 빌링키 결제임
    //   obj.PCD_PAY_TYPE = PaymentMethodType.CARD;
    //   obj.PCD_SIMPLE_FLAG = 'Y';
    //   obj.PCD_PAYER_AUTHTYPE = 'pwd';
    //   obj.PCD_PAYER_ID = arg.payplePayerId;
    // }

    return obj;
  }

  // getPgDataOtmo(arg: {
  //   payType: PaymentMethodType;
  //   goodsTitle: string;
  //   totalPrice: number;
  //   orderUuid: string;
  //   name?: string;
  //   mobileNumber?: string;
  //   email?: string;
  //   payplePayerId?: string;
  //   PCD_PAY_TAXTOTAL: number;
  // }) {
  //   if (arg.totalPrice <= 0) {
  //     return null;
  //   }
  //   const obj: {
  //     clientKey: string;
  //     PCD_PAY_TYPE: string;
  //     PCD_PAY_WORK: string;
  //     PCD_CARD_VER: string;
  //     PCD_PAY_GOODS: string;
  //     PCD_PAY_TOTAL: number;
  //     PCD_RST_URL: string;
  //     PCD_PAY_OID: string;
  //     PCD_PAYER_NAME?: string;
  //     PCD_PAYER_HP?: string;
  //     PCD_PAYER_EMAIL?: string;
  //     PCD_PAY_TAXTOTAL?: number;
  //     PCD_SIMPLE_FLAG?: string;
  //     PCD_PAYER_AUTHTYPE?: string;
  //     PCD_PAYER_ID?: string;
  //   } = {
  //     clientKey: this.appConfigService.paypleClientKey,
  //     PCD_PAY_TYPE: arg.payType,
  //     // 승인 요청 방식이며 CERT 고정값으로 설정합니다.
  //     PCD_PAY_WORK: 'CERT',
  //     // 카드 결제수단 중 앱카드, 정기(빌링), 비밀번호 간편결제 방식을 선택합니다.
  //     PCD_CARD_VER: this.getPayple(arg.payType),
  //     // 상품명입니다.
  //     PCD_PAY_GOODS: arg.goodsTitle,
  //     // 총 결제금액입니다.
  //     PCD_PAY_TOTAL: arg.totalPrice,
  //     // 결제 정보가 성공적으로 입력된 경우, 인증 결과가 POST 방식으로 전송됩니다. http://${this.appConfigService.domain}backend.${this.appConfigService.domain}
  //     PCD_RST_URL: '/webhook/callback/otmo',
  //     PCD_PAY_OID: arg.orderUuid,
  //     // 구매자 이름
  //     PCD_PAYER_NAME: arg.name,
  //     // 구매자 번호
  //     PCD_PAYER_HP: arg.mobileNumber,
  //     PCD_PAYER_EMAIL: arg.email,
  //     PCD_PAY_TAXTOTAL: arg.PCD_PAY_TAXTOTAL,
  //   };

  //   if (arg.payType === PaymentMethodType.TRANSFER) {
  //     obj.PCD_SIMPLE_FLAG = 'Y';
  //     obj.PCD_PAYER_AUTHTYPE = 'pwd';
  //     obj.PCD_PAYER_ID = arg.payplePayerId;
  //   } else if (arg.payType === PaymentMethodType.CARD_PWA) {
  //     obj.PCD_PAY_TYPE = PaymentMethodType.CARD;
  //     obj.PCD_SIMPLE_FLAG = 'Y';
  //     obj.PCD_PAYER_AUTHTYPE = 'pwd';
  //     obj.PCD_PAYER_ID = arg.payplePayerId;
  //   }

  //   return obj;
  // }

  // 파트너 인증 요청 (계좌 및 국내 카드 취소요청때쓰는)
  // view === true 면 조회용 Key 발급 false면 취소용 키를 발급받습니다.
  getPartnersKey(PCD_PAY_WORK?: 'PUSERINFO' | 'LINKREG') {
    const url =
      this.appConfigService.env !== 'production'
        ? 'https://democpay.payple.kr/php/auth.php'
        : 'https://cpay.payple.kr/php/auth.php';
    console.log('auth url', url);

    return axios.post<PayplePartnerRes>(
      url,
      {
        cst_id: this.appConfigService.paypleCstId,
        custKey: this.appConfigService.paypleCustKey,
        ...(PCD_PAY_WORK
          ? { PCD_PAY_WORK: PCD_PAY_WORK }
          : { PCD_PAYCANCEL_FLAG: 'Y' }),
      },
      {
        headers: {
          referer: process.env.BASE_URL || 'https://backend.sherry.gg',
        },
      },
    );
  }

  refundPayment(arg: {
    url: string;
    cst_id: string;
    custKey: string;
    AuthKey: string;
    orderId: string;
    createdAt: Date;
    refundPrice: number;
  }) {
    return axios.post<{
      PCD_PAY_RST?: string;
    }>(arg.url, {
      PCD_CST_ID: arg.cst_id,
      PCD_CUST_KEY: arg.custKey,
      PCD_AUTH_KEY: arg.AuthKey,
      PCD_REFUND_KEY: this.appConfigService.paypleRefundKey,
      PCD_PAYCANCEL_FLAG: 'Y',
      PCD_PAY_OID: arg.orderId,
      PCD_PAY_DATE: dayjs(arg.createdAt).format('YYYYMMDD'),
      PCD_REFUND_TOTAL: arg.refundPrice,
    });
  }

  async getCardInfo(arg: {
    PCD_CST_ID: string;
    PCD_CUST_KEY: string;
    PCD_AUTH_KEY: string;
    PCD_PAYER_ID: string;
  }) {
    const url =
      this.appConfigService.env !== 'production'
        ? 'https://democpay.payple.kr/php/cPayUser/api/cPayUserAct.php?ACT_=PUSERINFO'
        : 'https://cpay.payple.kr/php/cPayUser/api/cPayUserAct.php?ACT_=PUSERINFO';
    const cardRes = await axios.post<PaypleCardRes>(url, {
      PCD_CST_ID: arg.PCD_CST_ID,
      PCD_CUST_KEY: arg.PCD_CUST_KEY,
      PCD_AUTH_KEY: arg.PCD_AUTH_KEY,
      PCD_PAYER_ID: arg.PCD_PAYER_ID,
    });
    return {
      // 은행 이름
      cardType: cardRes.data.PCD_PAY_CARDNAME,
      // 카드 번호
      cardNumber: cardRes.data.PCD_PAY_CARDNUM,
    };
  }

  async getTransferInfo(arg: {
    PCD_CST_ID: string;
    PCD_CUST_KEY: string;
    PCD_AUTH_KEY: string;
    PCD_PAYER_ID: string;
  }) {
    const url =
      this.appConfigService.env !== 'production'
        ? 'https://democpay.payple.kr/php/cPayUser/api/cPayUserAct.php?ACT_=PUSERINFO'
        : 'https://cpay.payple.kr/php/cPayUser/api/cPayUserAct.php?ACT_=PUSERINFO';
    const transferRes = await axios.post<PaypleTransferRes>(url, {
      PCD_CST_ID: arg.PCD_CST_ID,
      PCD_CUST_KEY: arg.PCD_CUST_KEY,
      PCD_AUTH_KEY: arg.PCD_AUTH_KEY,
      PCD_PAYER_ID: arg.PCD_PAYER_ID,
    });
    return {
      // 은행이름
      bankName: transferRes.data.PCD_PAY_BANKNAME,
      // 개인 법인
      bankType: transferRes.data.PCD_PAY_BANKACCTYPE,
      // 카드 번호
      bankNum: transferRes.data.PCD_PAY_BANKNUM,
    };
  }

  /**
   * 계좌 인증 요청을 수행합니다. 해당 요청은 파트너 인증 및 계좌 인증을 위한 정보를 처리합니다.
   *
   * @param {Object} arg - 계좌 인증을 위한 요청 인자 객체
   * @param {string} arg.cst_id - 파트너 인증을 위한 ID. 라이브 ID는 계약 완료 후 발급됩니다. (예: "test")
   * @param {string} arg.custKey - 파트너 인증을 위한 키. 라이브 키는 계약 완료 후 발급됩니다. (보안에 유의 필요, 예: "abcd1234567890")
   * @param {string} arg.bank_code_std - 금융기관 코드 (예: "020")
   * @param {string} arg.account_num - 계좌번호 (13자리, 예: "****************")
   * @param {string} arg.account_holder_info_type - 예금주 구분 코드. 0은 개인/개인사업자, 6은 법인사업자 (예: "0")
   * @param {string} arg.account_holder_info - 예금주 생년월일(개인, 개인사업자) 또는 사업자번호(법인사업자). 예: "880212" (개인)
   * @param {string} arg.sub_id - 파트너 하위 셀러의 ID (예: "sub01")
   * @param {string} token - 파트너 인증 후 응답된 access_token
   *
   * @return {Promise<AccountVerificationRes>} 계좌 인증 데이터.
   *
   * @example
   * const response = await accountVerificationRequest({
   *   cst_id: "test",
   *   custKey: "abcd1234567890",
   *   bank_code_std: "020",
   *   account_num: "****************",
   *   account_holder_info_type: "0",
   *   account_holder_info: "880212",
   *   sub_id: "sub01"
   * });
   */
  async accountVerificationRequest(
    arg: {
      cst_id: string;
      custKey: string;
      bank_code_std: string;
      account_num: string;
      account_holder_info_type: string;
      account_holder_info: string;
      sub_id: string;
    },
    token: string,
  ): Promise<AccountVerificationRes> {
    const url = new URL(
      '/inquiry/real_name',
      this.appConfigService.env !== 'production'
        ? 'https://demohub.payple.kr'
        : 'https://hub.payple.kr',
    );
    const accountRes = await axios.post<AccountVerificationRes>(
      url.toString(),
      arg,
      { headers: { Authorization: `Bearer ${token}` } },
    );

    return accountRes.data;
  }

  /**
   * 빌링키로 이체 대기 요청. 계좌 인증이 완료된 계좌에서 이체를 요청합니다.
   *
   * @param {Object} arg - 이체 요청에 필요한 인자들
   * @param {string} arg.cst_id - 파트너 인증을 위한 ID. 라이브 ID는 계약 완료 후 발급됩니다. (예: "test")
   * @param {string} arg.custKey - 파트너 인증을 위한 키. 라이브 키는 계약 완료 후 발급됩니다. 외부에 노출되면 안되는 정보입니다. 보안에 유의하세요. (예: "abcd1234567890")
   * @param {string} arg.billing_tran_id - 계좌 인증이 완료된 계좌의 빌링키 (예: "cb9d695e-c034-4eeb-9550-c53a9ab7c2e4")
   * @param {string} arg.tran_amt - 이체할 금액. 테스트 시에는 1000으로 고정되어야 합니다. (예: "1000")
   * @param {string} arg.sub_id - 파트너 하위 셀러의 ID (예: "sub01")
   * @param {string} arg.distinct_key - 중복 이체를 방지하는 키. 미입력 시 자동 발급됩니다. (예: "c9z116521650283...")
   * @param {string} arg.print_content - 상대방 계좌 거래 내역에 표시될 최대 6자의 문구. 미입력 시 파트너 상호명으로 표시됩니다. (예: "테스트입니다")
   * @param {string} token - 파트너 인증 후 응답된 access_token
   *
   * @return {Promise<TransferRequestWaitingForBillingKeyRes>} 인증 대기 리스폰스
   *
   * @example
   * const result = await transferRequestWaitingForBillingKey({
   *   cst_id: "test",
   *   custKey: "abcd1234567890",
   *   billing_tran_id: "cb9d695e-c034-4eeb-9550-c53a9ab7c2e4",
   *   tran_amt: "1000",
   *   sub_id: "sub01",
   *   distinct_key: "c9z116521650283...",
   *   print_content: "테스트입니다"
   * });
   */
  async transferRequestWaitingForBillingKey(
    arg: {
      cst_id: string;
      custKey: string;
      billing_tran_id: string;
      tran_amt: string;
      sub_id: string;
      distinct_key: string;
      print_content: string;
    },
    token: string,
  ): Promise<TransferRequestWaitingForBillingKeyRes> {
    const url =
      this.appConfigService.env !== 'production'
        ? 'https://demohub.payple.kr/transfer/request'
        : 'https://hub.payple.kr/transfer/request';
    const waitingRes = await axios.post<TransferRequestWaitingForBillingKeyRes>(
      url,
      {
        cst_id: arg.cst_id,
        custKey: arg.custKey,
        billing_tran_id: arg.billing_tran_id,
        tran_amt: arg.tran_amt,
        sub_id: arg.sub_id,
        distinct_key: arg.distinct_key,
        print_content: arg.print_content,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );

    return waitingRes.data;
  }

  /**
   * 이체 실행 요청. 이체 대기 요청 후 수신한 그룹키를 이용해 이체를 실행하거나 대기 중인 이체를 취소합니다.
   *
   * @param {Object} arg - 이체 실행 요청에 필요한 인자들
   * @param {string} arg.cst_id - 파트너 인증을 위한 ID. 라이브 ID는 계약 완료 후 발급됩니다. (예: "test")
   * @param {string} arg.custKey - 파트너 인증을 위한 키. 라이브 키는 계약 완료 후 발급됩니다. 보안에 유의하세요. (예: "abcd1234567890")
   * @param {string} arg.group_key - 이체 대기 요청 후 수신한 그룹키로 이체 실행을 진행합니다. (예: "QlJKZDZrNVBzbW9Yc1UzTWNIdW05dz09")
   * @param {string} arg.billing_tran_id - 계좌의 빌링키. 특정 빌링키 또는 "ALL"을 사용하여 이체 실행을 진행합니다. (예: "cb9d695e-c034-4eeb-9550-c53a9ab7c2e4" 또는 "ALL")
   * @param {string} arg.execute_type - 이체 실행 구분값. "NOW" 즉시 이체 실행, "CANCEL" 대기 중인 이체 취소 (예: "NOW")
   * @param {string} [arg.webhook_url] - 이체 실행 결과를 수신할 주소. 테스트 시에만 필요합니다. (예: "http://your-test-domain.com")
   * @param {string} token - 파트너 인증 후 응답된 access_token
   *
   * @return {Promise<TransferExecutionRes>} 이체 실행 요청에 대한 결과를 처리하는 Promise 객체. 결과는 추후 구현에 따라 처리됩니다.
   *
   * @example
   * const result = await transferExecutionRequest({
   *   cst_id: "test",
   *   custKey: "abcd1234567890",
   *   group_key: "QlJKZDZrNVBzbW9Yc1UzTWNIdW05dz09",
   *   billing_tran_id: "cb9d695e-c034-4eeb-9550-c53a9ab7c2e4",
   *   execute_type: "NOW",
   *   webhook_url: "http://your-test-domain.com"
   * });
   */
  async transferExecutionRequest(
    arg: {
      cst_id: string;
      custKey: string;
      group_key: string;
      billing_tran_id: string;
      execute_type: string;
      webhook_url?: string;
    },
    token: string,
  ): Promise<TransferExecutionRes> {
    const url =
      this.appConfigService.env !== 'production'
        ? 'https://demohub.payple.kr/transfer/execute'
        : 'https://hub.payple.kr/transfer/execute';
    const waitingRes = await axios.post<TransferExecutionRes>(
      url,
      {
        cst_id: arg.cst_id,
        custKey: arg.custKey,
        group_key: arg.group_key,
        billing_tran_id: arg.billing_tran_id,
        execute_type: arg.execute_type,
        webhook_url: arg.webhook_url,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );

    return waitingRes.data;
  }

  /**
   * 파트너 인증 요청 이후 정산을 위한 토큰 발행
   *
   */
  async partnerAuthenticationRequest() {
    const url =
      this.appConfigService.env !== 'production'
        ? 'https://demohub.payple.kr/oauth/token'
        : 'https://hub.payple.kr/oauth/token';

    const tokenRes = await axios.post<PartnerAuthenticationRes>(url, {
      cst_id: this.appConfigService.paypleCstId,
      custKey: this.appConfigService.paypleClientKey,
      code: this.appConfigService.paypleCode,
    });

    return tokenRes.data;
  }

  async paypleCreateLink(
    arg: PaypleCreateLinkRequest,
  ): Promise<PaypleCreateLinkResponse> {
    const url =
      this.appConfigService.env !== 'production'
        ? 'https://democpay.payple.kr/php/link/api/LinkRegAct.php?ACT_=LINKREG'
        : 'https://cpay.payple.kr/php/link/api/LinkRegAct.php?ACT_=LINKREG';

    const params = new URLSearchParams();
    Object.entries(arg).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key.toUpperCase(), String(value));
      }
    });

    // axios.post 호출에 헤더 추가
    const linkRes = await axios.post<PaypleCreateLinkResponse>(url, params, {
      headers: {
        referer: process.env.BASE_URL || 'https://backend.sherry.gg',
      },
    });

    return linkRes.data;
  }
}
