<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>결제 성공</title>
    <style>
        body {
            font-family: 'Apple SD Gothic Neo', 'Noto Sans KR', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 500px;
            width: 90%;
            background-color: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
        }
        .success-icon {
            width: 80px;
            height: 80px;
            background-color: #4CAF50;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 20px;
        }
        .success-icon svg {
            width: 40px;
            height: 40px;
        }
        h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 15px;
        }
        .message {
            font-size: 16px;
            line-height: 1.6;
            color: #666;
            margin-bottom: 25px;
        }
        .order-info {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: left;
        }
        .order-info p {
            margin: 8px 0;
            display: flex;
            justify-content: space-between;
        }
        .order-info p span:first-child {
            color: #888;
        }
        .btn {
            display: inline-block;
            background-color: #3f51b5;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
            text-decoration: none;
            margin: 5px;
        }
        .btn:hover {
            background-color: #303f9f;
        }
        .btn-outline {
            background-color: transparent;
            border: 1px solid #3f51b5;
            color: #3f51b5;
        }
        .btn-outline:hover {
            background-color: #f0f3ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#ffffff">
                <path d="M0 0h24v24H0z" fill="none"/>
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
        </div>
        <h1>결제가 완료되었습니다!</h1>
        <p class="message">
            고객님의 주문이 성공적으로 처리되었습니다.<br>
            아래 주문 정보를 확인해 주세요.
        </p>
        
        <div class="order-info">
            <p><span>주문번호</span> <span id="orderNumber">{{ orderNumber }}</span></p>
            <p><span>상품명</span> <span id="productName">{{ productName }}</span></p>
            <p><span>결제금액</span> <span id="amount">{{ amount }}원</span></p>
            <p><span>결제일시</span> <span id="paymentDate">{{ paymentDate }}</span></p>
            <p><span>결제방법</span> <span id="paymentMethod">{{ paymentMethod }}</span></p>
        </div>

        <div class="buttons">
            <a href="/orders/detail/{{ orderNumber }}" class="btn">주문 상세보기</a>
            <a href="/" class="btn btn-outline">홈으로 돌아가기</a>
        </div>
    </div>

    <script>
        // 결제 정보를 파라미터에서 가져오는 함수
        function getParameterByName(name) {
            const url = new URL(window.location.href);
            return url.searchParams.get(name);
        }

        // 페이지 로드 시 실행
        document.addEventListener('DOMContentLoaded', function() {
            // URL 파라미터에서 결제 정보 가져오기 (백엔드에서 전달한 데이터)
            const orderNumber = getParameterByName('orderNumber') || '주문 정보 없음';
            const productName = getParameterByName('productName') || '상품 정보 없음';
            const amount = getParameterByName('amount') || '0';
            const paymentDate = getParameterByName('paymentDate') || new Date().toLocaleString();
            const paymentMethod = getParameterByName('paymentMethod') || '결제 방법 정보 없음';

            // 정보를 페이지에 표시
            document.getElementById('orderNumber').textContent = orderNumber;
            document.getElementById('productName').textContent = productName;
            document.getElementById('amount').textContent = amount + '원';
            document.getElementById('paymentDate').textContent = paymentDate;
            document.getElementById('paymentMethod').textContent = paymentMethod;
        });
    </script>
</body>
</html>