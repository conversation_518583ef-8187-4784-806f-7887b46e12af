import { toBase64Url } from '@common/helpers/to-base64-url';
import { UnauthorizedUserMessage } from '@common/types/error-res.type';
import { RequestUser, UserRole } from '@common/types/token.types';
import { AppConfigService } from '@config/app/config.service';
import { OauthConfigService } from '@config/oauth/config.service';
import { AuthenticationService } from '@module/jwk/authentication.service';
import { UserService } from '@module/user/user.service';
import {
  ForbiddenException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { SnsPlatformNameEnum } from '@prisma/client';
import { PrismaService } from '@provider/database/prisma.service';
import axios, { AxiosResponse } from 'axios';
import { OAuth2Client } from 'google-auth-library';
import { google } from 'googleapis';
import type { AppleOauthDto } from './dto/apple.oauth.dto';
import type { GoogleOauthDto } from './dto/google.oauth.dto';
import type { OauthAppleSignUpDto } from './dto/oauth.apple-signup.dto';
import type { OauthGoogleSignUpDto } from './dto/oauth.google-signup.dto';

interface AppleAxios {
  access_token: string;
  token_type: string;
  expires_in: string;
  refresh_token: string;
  id_token: string;
}

@Injectable()
export class OauthService {
  constructor(
    private readonly jwt: JwtService,
    private readonly userService: UserService,
    private readonly oauthConfigService: OauthConfigService,
    private readonly authenticationService: AuthenticationService,
    private readonly prismaService: PrismaService,
    private readonly appConfigService: AppConfigService,
  ) {}
  private readonly appleHeaders = {
    'Content-Type': 'application/x-www-form-urlencoded',
  };
  private readonly encoder = new TextEncoder();

  private async googleGetUserInfo(tokens: {
    accessToken: string;
    refreshToken?: string;
  }) {
    const oauth2Client = this.getGoogleOAuth2(
      `${this.appConfigService.baseUrl}/auth/google/login`,
      // `${this.appConfigService.baseUrl}/auth/google/callback`,
    );

    // console.log('tokens', tokens);

    oauth2Client.setCredentials({
      access_token: tokens.accessToken,
      refresh_token: tokens.refreshToken,
    });

    const oauth2Client2 = google.oauth2({
      auth: oauth2Client,
      version: 'v2',
    });

    const googleUserMe = await oauth2Client2.userinfo.v2.me.get();
    if (!googleUserMe.data.id) {
      return null;
    }

    return {
      type: SnsPlatformNameEnum.google,
      oauthId: googleUserMe.data.id,
      nickname: googleUserMe.data.name ?? '',
      email: googleUserMe.data.email ?? undefined,
      profileImg: googleUserMe.data.picture ?? undefined,
      realName: `${googleUserMe.data.family_name} ${googleUserMe.data.given_name}`,
      refreshToken: tokens.refreshToken ?? undefined,
    };
  }

  private async appleGetUserInfo(token: { refreshToken: string }) {
    const secret = await this.makeJWT();
    const response = await axios.post<AppleAxios>(
      'https://appleid.apple.com/auth/token',
      new URLSearchParams({
        client_id: this.oauthConfigService.appleClientID,
        client_secret: secret,
        refresh_token: token.refreshToken,
        grant_type: 'refresh_token',
      }),
      {
        headers: this.appleHeaders,
      },
    );
    const idTokenDecoded = this.jwt.decode(response.data.id_token);
    return {
      type: SnsPlatformNameEnum.apple,
      oauthId: idTokenDecoded.sub,
      nickname: idTokenDecoded.given_name,
      email: idTokenDecoded.email,
      profileImg: idTokenDecoded.picture,
      realName: `${idTokenDecoded.given_name} ${idTokenDecoded.family_name}`,
      refreshToken: token.refreshToken ?? undefined,
    };
  }

  // 아마 이부분은 크게 신경안써도 될듯 프론트에서 처리
  getGoogleOAuth2(url?: string): OAuth2Client {
    return new google.auth.OAuth2(
      this.oauthConfigService.googleClientId,
      this.oauthConfigService.googleClientSecret,
      url ?? `${this.appConfigService.baseUrl}/auth/google/callback`,
    );
  }

  google() {
    // `${this.appConfigService.baseUrl}/auth/google/login`,
    const oauth2Client = this.getGoogleOAuth2();
    // generate a url that asks permissions for Blogger and Google Calendar scopes
    const scopes = ['email', 'profile'];

    return oauth2Client.generateAuthUrl({
      scope: scopes,
      access_type: 'offline',
    });
  }

  async googleLogin(tokens: GoogleOauthDto) {
    const userInfo = await this.googleGetUserInfo(tokens);
    if (!userInfo) {
      throw new UnauthorizedException(UnauthorizedUserMessage);
    }
    const user = await this.userService.findUser({
      type: userInfo.type,
      oauthId: userInfo.oauthId,
    });
    if (!user) {
      throw new ForbiddenException('회원 가입이 필요합니다.!');
    }
    if (tokens.refreshToken) {
      // 리프레시 토큰이 있다면 업데이트
      await this.prismaService.snsOauth.update({
        where: {
          userId_snsPlatformId: {
            userId: user.userUuid,
            snsPlatformId: user.snsPlatformId,
          },
        },
        data: {
          refreshToken: tokens.refreshToken,
        },
      });
    }

    return this.authenticationService.getTokens({
      userId: user.userUuid,
      role: UserRole.USER,
      snsPlatformId: user.snsPlatformId,
    });
  }

  // Test용
  async googleCallBackTest(
    param: {
      code: string;
      scope: string;
      authuser: string;
      prompt: string;
    },
    seller: boolean,
  ) {
    const oauth2Client = this.getGoogleOAuth2(
      seller === false
        ? `${this.appConfigService.baseUrl}/auth/google/callback`
        : `${this.appConfigService.baseUrl}/auth/google/callback/seller`,
    );
    const googleTokenRes = await oauth2Client.getToken(param.code);
    const oauth2Client2 = google.oauth2('v2');
    console.log(googleTokenRes, 'ㅇㅇㅇ');

    if (!googleTokenRes.tokens.access_token) {
      throw new UnauthorizedException(UnauthorizedUserMessage);
    }
    const googleTokenRes2 = await oauth2Client2.userinfo.v2.me
      .get({
        oauth_token: googleTokenRes.tokens.access_token,
      })
      .catch((error) => {
        console.error('google Auth Error', error);
        throw new UnauthorizedException(UnauthorizedUserMessage);
      });
    if (googleTokenRes2.data.id) {
      const updateData = await this.userService.ifNotPresentCreate({
        type: SnsPlatformNameEnum.google,
        oauthId: googleTokenRes2.data.id,
        nickname: googleTokenRes2.data.name ?? '',
        email: googleTokenRes2.data.email ?? undefined,
        profileImg: googleTokenRes2.data.picture ?? undefined,
        realName: `${googleTokenRes2.data.family_name}${googleTokenRes2.data.given_name}`,
        seller: seller,
      });
      return this.authenticationService.getTokens({
        userId: updateData.user.userUuid,
        role: updateData.role,
        snsPlatformId: updateData.snsPlatformId,
      });
    } else {
      throw new UnauthorizedException(UnauthorizedUserMessage);
    }
  }

  // 콜백 url 인데 크게 신경안써도 될듯
  private readonly appleCallBackUrl =
    'https://steady-apparent-mouse.ngrok-free.app/auth/apple/callback';

  // 아마 이부분은 크게 신경안써도 될듯 프론트에서 처리
  async apple() {
    const clientID = this.oauthConfigService.appleClientID;
    const callbackURL = this.appleCallBackUrl;
    console.log(
      `https://appleid.apple.com/auth/authorize?client_id=${clientID}&redirect_uri=${callbackURL}&response_type=code id_token&state=Initial&scope=name email&response_mode=form_post`,
    );
    return `https://appleid.apple.com/auth/authorize?client_id=${clientID}&redirect_uri=${callbackURL}&response_type=code id_token&state=Initial&scope=name email&response_mode=form_post`;
  }

  async appleAuth(code: string, idToken: string) {
    const clientId = this.oauthConfigService.appleClientID;
    const callbackURL = this.appleCallBackUrl;
    const idTokenDecoded: {
      iss: string;
      aud: string;
      exp: number;
      iat: number;
      // 유저의 고유 id 입니다.
      sub: string;
      c_hash: string;
      email: string;
      email_verified: boolean;
      is_private_email: boolean;
      auth_time: number;
      nonce_supported: boolean;
    } = await this.jwt
      .verifyAsync(idToken, {
        secret: this.oauthConfigService.googleClientSecret,
        algorithms: ['ES256'],
      })
      .catch((error) => {
        console.error('Apple ID Token Verification Error:', error);
        throw new UnauthorizedException('애플 로그인 인증에 실패했습니다.');
      });
    const sec = await this.makeJWT();

    // 나회원 가입했어.!
    const { data } = await axios.post<
      AppleAxios,
      AxiosResponse<AppleAxios>,
      {
        code: string;
        client_id: string;
        client_secret: string;
        grant_type: string;
        redirect_uri: string;
      }
    >(
      'https://appleid.apple.com/auth/token',
      {
        code,
        client_id: clientId,
        client_secret: sec,
        grant_type: 'authorization_code',
        redirect_uri: callbackURL,
      },
      {
        headers: this.appleHeaders,
      },
    );

    const updateData = await this.userService.findUser({
      type: SnsPlatformNameEnum.apple,
      oauthId: idTokenDecoded.sub,
    });
    if (!updateData) {
      throw new UnauthorizedException('애플 로그인 인증에 실패했습니다.');
    }
    if (data.refresh_token) {
      // 리프레시 토큰이 있다면 업데이트
      await this.prismaService.snsOauth.update({
        where: {
          userId_snsPlatformId: {
            userId: updateData.userUuid,
            snsPlatformId: updateData.snsPlatformId,
          },
        },
        data: {
          refreshToken: data.refresh_token,
        },
      });
    }

    return this.authenticationService.getTokens({
      userId: updateData.userUuid,
      role: UserRole.SELLER,
      snsPlatformId: updateData.snsPlatformId,
    });
  }

  async appleLogin(dto: AppleOauthDto) {
    const user = await this.userService.findUser({
      type: SnsPlatformNameEnum.apple,
      oauthId: dto.providerAccountId,
    });
    if (!user) {
      throw new ForbiddenException('회원 가입이 필요합니다.!');
    }
    if (dto.refreshToken) {
      // 리프레시 토큰이 있다면 업데이트
      await this.prismaService.snsOauth.update({
        where: {
          userId_snsPlatformId: {
            userId: user.userUuid,
            snsPlatformId: user.snsPlatformId,
          },
        },
        data: {
          refreshToken: dto.refreshToken,
        },
      });
    }

    return this.authenticationService.getTokens({
      userId: user.userUuid,
      role: UserRole.USER,
      snsPlatformId: user.snsPlatformId,
    });
  }

  async makeJWT() {
    const binaryDerString = atob(this.oauthConfigService.applePrivateKey);
    const binaryDer = new Uint8Array(
      [...binaryDerString].map((c) => c.charCodeAt(0)),
    );
    const privateKey = await crypto.subtle.importKey(
      'pkcs8',
      binaryDer.buffer,
      { name: 'ECDSA', namedCurve: 'P-256' },
      true,
      ['sign'],
    );

    const teamId = this.oauthConfigService.appleTeamId;
    const parts = [
      toBase64Url(
        this.encoder.encode(
          JSON.stringify({
            alg: 'ES256',
            kid: this.oauthConfigService.appleKeyID,
          }),
        ),
      ),
      toBase64Url(
        this.encoder.encode(
          JSON.stringify({
            iss: teamId,
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + 120,
            aud: 'https://appleid.apple.com',
            sub: this.oauthConfigService.appleClientID,
          }),
        ),
      ),
    ];
    // Sign with your team ID and key ID information.
    const signature = await crypto.subtle.sign(
      { name: 'ECDSA', hash: 'SHA-256' },
      privateKey,
      this.encoder.encode(parts.join('.')),
    );

    return parts.concat(toBase64Url(signature)).join('.');
  }

  async refreshToken(requestUser: RequestUser) {
    const user = await this.prismaService.user.findFirst({
      where: {
        userUuid: requestUser.id,
      },
      select: {
        userUuid: true,
      },
    });
    if (!user) {
      throw new UnauthorizedException('다시 로그인 해주세요.');
    }

    // TODO: 셀러 확인
    return this.authenticationService.getTokens({
      userId: user.userUuid,
      role: UserRole.USER,
      snsPlatformId: requestUser.snsPlatformId,
    });
  }

  async unsubscribe(user: RequestUser) {
    const accUser = await this.prismaService.user.findUnique({
      where: {
        userUuid: user.id,
      },
      select: {
        userUuid: true,
        snsOauth: {
          select: {
            snsUid: true,
            refreshToken: true,
            snsPlatform: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });
    if (!accUser) {
      return;
    }
    // TODO: 여기 수정 필요 만약 리프레쉬 토큰이 만료되었다면 해당 로그인 처리
    // for (const sns of accUser.snsOauth) {
    //   if (sns) {
    //     switch (sns.snsPlatform.name) {
    //       case SnsPlatformNameEnum.google:
    //         if (sns.refreshToken) {
    //           await this.googleRevoke(sns.refreshToken);
    //         }
    //         break;
    //       case SnsPlatformNameEnum.apple:
    //         if (sns.refreshToken) {
    //           await this.appleRevoke(sns.refreshToken);
    //         }
    //         break;
    //       default:
    //         break;
    //     }
    //   }
    // }

    // 유저 데이터 날리기 전에 부스등 데이터 처리 필요 합니다.
    // await this.prismaService.snsOauth.deleteMany({
    //   where: {
    //     userId: user.id,
    //   },
    // });
    // await this.prismaService.user.delete({
    //   where: {
    //     id: user.id,
    //   },
    // });
  }

  async googleRevoke(token: string) {
    const oauth2Client = this.getGoogleOAuth2();
    await oauth2Client.revokeToken(token);
  }

  async appleRevoke(token: string) {
    const clientId = 'com.doubleD.shuttleTestApp.login'; // 여기에 클라이언트 ID를 넣으세요.
    await axios
      .post(
        'https://appleid.apple.com/auth/revoke',
        {
          client_id: clientId,
          client_secret: this.makeJWT(),
          token,
          token_type_hint: 'refresh_token',
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      )
      .catch((error) => console.log(error));
  }

  async googleSignUp(dto: OauthGoogleSignUpDto) {
    const googleUserInfo = await this.googleGetUserInfo({
      accessToken: dto.accessToken,
      refreshToken: dto.refreshToken,
    });
    if (!googleUserInfo) {
      throw new UnauthorizedException(UnauthorizedUserMessage);
    }
    const userInfo = {
      type: SnsPlatformNameEnum.google,
      oauthId: googleUserInfo.oauthId,
      nickname: dto.nickname,
      email: dto.email ?? googleUserInfo.email,
      profileImg: dto.profileImage ?? googleUserInfo.profileImg,
      realName: googleUserInfo.realName,
      refreshToken: dto.refreshToken,
    };

    const user = await this.userService.ifNotPresentCreate({
      type: SnsPlatformNameEnum.google,
      oauthId: userInfo.oauthId,
      nickname: userInfo.nickname,
      email: dto.email,
      profileImg: userInfo.profileImg,
      realName: userInfo.realName,
      refreshToken: userInfo.refreshToken,
      handle: dto.handle,
    });

    return {
      userUuid: user.user.userUuid,
      token: this.authenticationService.getTokens({
        userId: user.user.userUuid,
        role: user.role,
        snsPlatformId: user.snsPlatformId,
      }),
    };
  }

  async appleSignUp(dto: OauthAppleSignUpDto) {
    const user = await this.userService.ifNotPresentCreate({
      type: SnsPlatformNameEnum.apple,
      oauthId: dto.providerAccountId,
      nickname: dto.nickname,
      email: dto.email,
      profileImg: dto.profileImage,
      realName: dto.name,
      refreshToken: dto.accessToken,
      handle: dto.handle,
    });

    return {
      userUuid: user.user.userUuid,
      token: this.authenticationService.getTokens({
        userId: user.user.userUuid,
        role: user.role,
        snsPlatformId: user.snsPlatformId,
      }),
    };
  }

  async getCurrentSnsRefreshToken(user: RequestUser) {
    const snsOauth = await this.prismaService.snsOauth.findUnique({
      where: {
        userId_snsPlatformId: {
          userId: user.id,
          snsPlatformId: user.snsPlatformId,
        },
      },
      select: {
        refreshToken: true,
      },
    });

    return snsOauth?.refreshToken ?? null;
  }
}
