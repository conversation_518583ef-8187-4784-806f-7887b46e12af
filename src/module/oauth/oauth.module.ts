import { BoothModule } from '@module/booth/booth.module';
import { Mo<PERSON><PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { OauthController } from './oauth.controller';
import { OauthOutController } from './oauth.out.controller';
import { OauthService } from './oauth.service';

@Module({
  imports: [JwtModule, BoothModule],
  controllers: [OauthController, OauthOutController],
  providers: [OauthService],
})
export class OauthModule {}
