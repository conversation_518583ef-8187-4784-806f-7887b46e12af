import { User } from '@common/decorators/jwk.decorators';
import { RequestUser } from '@common/types/token.types';
import { Controller, Get, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { OauthService } from './oauth.service';

@ApiTags('User')
@Controller('auth')
export class OauthOutController {
  constructor(private readonly oauthService: OauthService) {}

  @ApiOperation({
    summary: '회원 탈퇴',
    description: 'TODO:앞의 인증기능, 유저 데이터 날리기 부스 굿즈 등등',
  })
  @ApiBearerAuth()
  @Post('unsubscribe')
  unsubscribe(@User() user: RequestUser) {
    return this.oauthService.unsubscribe(user);
  }

  @ApiOperation({
    summary: '현재 sns 리프레쉬토큰 가져오기 (sns refresh token)',
    description: '현재 sns 리프레쉬토큰 가져오기 (sns refresh token)',
  })
  @ApiBearerAuth()
  @ApiTags('Auth')
  @Get('sns/refresh-token')
  async getCurrentSnsRefreshToken(@User() user: RequestUser) {
    return this.oauthService.getCurrentSnsRefreshToken(user);
  }
}
