import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class AppleOauthDto {
  @ApiProperty({
    description: '애플 엑세스 토큰 (apple Access Token)',
    example: 'ac9bfb60....0.pvyu....',
  })
  @IsString()
  @IsNotEmpty()
  accessToken!: string;

  @ApiProperty({
    description: '애플 리프레시 토큰 (apple Refresh Token)',
    example: 'rca48eb775dc7481....0.pvyu....',
    required: false,
  })
  @IsString()
  @IsOptional()
  refreshToken!: string;

  @ApiProperty({
    description: '애플 사용자 ID (apple User ID)',
    example: '000584.....0811',
  })
  @IsString()
  @IsNotEmpty()
  providerAccountId!: string;
}
