import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { AppleOauthDto } from './apple.oauth.dto';

export class OauthAppleSignUpDto extends AppleOauthDto {
  @ApiProperty({
    description: '닉네임 (nickname)',
    example: '홍길동',
  })
  @IsString()
  @IsNotEmpty()
  nickname!: string;

  @ApiProperty({
    description: '이름 (name)',
    example: '홍길동',
  })
  @IsString()
  @IsNotEmpty()
  name!: string;

  @ApiProperty({
    description: '이메일 (email)',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  email!: string;

  @ApiProperty({
    description: '프로필 이미지 (profileImage)',
    example: 'https://example.com/profile.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  profileImage?: string;

  @ApiProperty({
    description: 'handle (handle)',
    example: 'sherry',
  })
  @IsString()
  @IsNotEmpty()
  handle!: string;
}
