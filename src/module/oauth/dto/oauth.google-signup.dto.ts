import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { GoogleOauthDto } from './google.oauth.dto';

export class OauthGoogleSignUpDto extends GoogleOauthDto {
  @ApiProperty({
    description: '닉네임 (nickname)',
    example: '홍길동',
  })
  @IsString()
  @IsNotEmpty()
  nickname!: string;

  @ApiProperty({
    description: '이메일 (email)',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  email!: string;

  @ApiProperty({
    description: '프로필 이미지 (profileImage)',
    example: 'https://example.com/profile.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  profileImage?: string;

  @ApiProperty({
    description:
      'handle 중복 체크 해야됩니다. 중복값이 있다면 409 Error.  (handle userId if duplicate 409 Error)',
    example: 'sherry',
  })
  @IsString()
  @IsNotEmpty()
  handle!: string;
}
