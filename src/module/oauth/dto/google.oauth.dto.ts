import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class GoogleOauthDto {
  @ApiProperty({
    description: '구글 엑세스 토큰 (google Access Token)',
    example: 'ya29.a0ARrdaM...',
  })
  @IsString()
  @IsNotEmpty()
  accessToken!: string;

  @ApiProperty({
    description: '구글 리프레시 토큰 (google Refresh Token)',
    example: '1//0gD1a0ARrdaM...',
    required: false,
  })
  @IsString()
  @IsOptional()
  refreshToken!: string;
}
