import { User } from '@common/decorators/jwk.decorators';
import { IsPublic } from '@common/decorators/public.decorator';
import { RefreshTokenGuard } from '@common/guards/refresh-token.guard';
import { RequestUser } from '@common/types/token.types';
import { BoothCreatorService } from '@module/booth/booth.creator.service';
import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AppleOauthDto } from './dto/apple.oauth.dto';
import { GoogleOauthDto } from './dto/google.oauth.dto';
import { OauthAppleSignUpDto } from './dto/oauth.apple-signup.dto';
import { OauthGoogleSignUpDto } from './dto/oauth.google-signup.dto';
import { OauthService } from './oauth.service';

@ApiTags('Auth')
@IsPublic()
@Controller('auth')
export class OauthController {
  // TODO: 로그인 실패 페이지 주소 받아야됨
  fileUrl = 'https://sherry.gg/fail';
  constructor(
    private readonly oauthService: OauthService,
    private readonly boothCreatorService: BoothCreatorService,
  ) {}

  @ApiOperation({
    summary: '구글 로그인을 위한 Url',
    description: '구글 로그인 Url',
    deprecated: true,
  })
  @Get('google')
  google() {
    return {
      url: this.oauthService.google(),
    };
  }

  @ApiOperation({
    summary: '회원가입 (google sign up)',
    description: '회원가입 (google sign up)',
  })
  @Post('google/signup')
  async signUp(@Body() dto: OauthGoogleSignUpDto) {
    const { userUuid, token } = await this.oauthService.googleSignUp(dto);
    this.boothCreatorService.upsertOnlineBooth(userUuid, {
      name: dto.nickname,
      bannerImageUrl: '',
      infoBlocks: [''],
      goodsBoothPivot: [],
    });
    return token;
  }

  @ApiOperation({
    summary: '구글 로그인 (google login)',
    description: '구글 로그인 (google login)',
  })
  @Post('google/login')
  async googleLogin(@Body() param: GoogleOauthDto) {
    return this.oauthService.googleLogin(param);
  }

  @ApiOperation({
    summary: '구글 콜백 테스트용 (dev google callback test)',
    description: '구글 콜백으로 들어오는 파라미터들로 토큰을 받기',
    deprecated: true,
  })
  @Get('google/callback')
  async googleCallBackLanding(
    @Query()
    param: {
      code: string;
      scope: string;
      authuser: string;
      prompt: string;
    },
  ) {
    return this.oauthService.googleCallBackTest(param, false);
  }

  @ApiOperation({
    summary: '애플 로그인을 위한 Url',
    description: '애플 로그인 Url',
    deprecated: true,
  })
  @Get('apple')
  async apple() {
    return {
      url: await this.oauthService.apple(),
    };
  }

  // 에플 콜백이 이렇게 와요!
  @ApiOperation({
    summary: '애플 login (apple login)',
    description:
      '애플 콜백으로 들어오는 파라미터들을 전부 똑같이 넣어주세요. (apple login)',
  })
  @Post('apple/login')
  async appleAuthRedirect(
    // @Body()
    // body: {
    //   code: string;
    //   id_token: string;
    //   state: string;
    //   user:
    //     | { name: { firstName: string; lastName: string }; email: string }
    //     | undefined;
    //   error: string | undefined;
    // },
    @Body() dto: AppleOauthDto,
  ) {
    return this.oauthService.appleLogin(dto);
  }

  @ApiOperation({
    summary: '애플 회원가입 (apple sign up)',
    description: '애플 회원가입 (apple sign up)',
  })
  @Post('apple/signup')
  async appleSignUp(@Body() dto: OauthAppleSignUpDto) {
    const { userUuid, token } = await this.oauthService.appleSignUp(dto);
    this.boothCreatorService.upsertOnlineBooth(userUuid, {
      name: dto.nickname,
      bannerImageUrl: '',
      infoBlocks: [''],
      goodsBoothPivot: [],
    });
    return token;
  }

  @ApiOperation({
    summary: '리프래쉬 토큰 (refresh token)',
    description: '엑세스 토큰 갱신 (refresh token)',
  })
  @ApiBearerAuth()
  @Post('refresh')
  @UseGuards(RefreshTokenGuard)
  async refreshToken(@User() user: RequestUser) {
    return this.oauthService.refreshToken(user);
  }
}
