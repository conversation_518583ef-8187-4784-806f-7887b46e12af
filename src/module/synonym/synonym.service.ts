import { Injectable } from '@nestjs/common';
import { PrismaService } from '@provider/database/prisma.service';

type FilterKeysEndingWith<T, Suffix extends string> = {
  [K in keyof T]: K extends `${infer Prefix}${Suffix}` ? Prefix : never;
}[keyof T];

type SynonymModelName = FilterKeysEndingWith<PrismaService, 'Synonym'>;

@Injectable()
export class SynonymService {
  constructor(private readonly prismaService: PrismaService) {}

  createSynonyms({
    synonymModel,
    names,
    targetUuid,
  }: {
    synonymModel: SynonymModelName;
    names: string[];
    targetUuid: string;
  }): Promise<void> {
    return (
      this.prismaService[`${synonymModel}Synonym`] as unknown as {
        createMany: (args: {
          data: Array<{
            synonym: string;
            [key: string]: string;
          }>;
        }) => Promise<void>;
      }
    ).createMany({
      data: names.map((name) => ({
        synonym: name,
        [`${synonymModel}Uuid`]: targetUuid,
      })),
    });
  }
}
