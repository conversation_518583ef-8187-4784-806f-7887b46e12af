import { IntersectionType, PartialType, PickType } from '@nestjs/swagger';
import { BoothDto } from './booth.dto';

export class BoothUpdateDto extends IntersectionType(
  PartialType(
    PickType(BoothDto, [
      'name',
      'locationTexts',
      'bannerImageUrl',
      'infoBlocks',
      'participatedDates',
      'eventUuid',
    ]),
  ),
  PickType(BoothDto, ['goodsBoothPivot']),
) {}

export class DefaultBoothUpdateDto extends IntersectionType(
  PickType(BoothDto, [
    'name',
    'bannerImageUrl',
    'infoBlocks',
    'goodsBoothPivot',
  ]),
) {}
