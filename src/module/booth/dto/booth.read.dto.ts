import { IsTransFromStringToBooleanType } from '@common/decorators/is-trans-from-boolean';
import { PaginationDto } from '@common/dto/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';

export class GetParticipatedBoothsQueryDto extends PaginationDto {
  @ApiProperty({
    description: 'Booth name',
    type: String,
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'UUID string of Event',
    type: String,
    required: false,
  })
  @IsUUID('7')
  @IsOptional()
  eventUuid?: string;

  @ApiProperty({
    description: 'Is POS request',
    type: Boolean,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsTransFromStringToBooleanType()
  isPos?: boolean;
}
