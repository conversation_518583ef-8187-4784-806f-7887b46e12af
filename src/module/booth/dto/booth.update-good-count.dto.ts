import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsInt,
  IsNotEmpty,
  IsUUID,
  Min,
  ValidateNested,
} from 'class-validator';

export class BoothGoodsDto {
  @ApiProperty({
    description: '상품 uuid',
    type: String,
  })
  @IsUUID()
  @IsNotEmpty()
  goodsUuid!: string;

  @ApiProperty({
    description: '상품 수량',
    type: Number,
    required: true,
  })
  @Type(() => Number)
  @IsNotEmpty()
  @IsInt()
  @Min(1, { message: '상품 수량은 최소 1개 이상이어야 합니다.' })
  goodsCount!: number;
}
export class BoothUpdateGoodsCountDto {
  @ApiProperty({
    description: '업데이트할 굿즈 리스트',
    type: [BoothGoodsDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BoothGoodsDto)
  goodsList!: BoothGoodsDto[];
}
