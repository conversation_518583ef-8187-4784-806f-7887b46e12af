import { TicketModule } from '@module/ticket/ticket.module';
import { Module } from '@nestjs/common';
import { <PERSON><PERSON>ontroller } from './booth.controller';
import { <PERSON><PERSON><PERSON>Controller } from './booth.creator.controller';
import { BoothCreatorService } from './booth.creator.service';
import { BoothService } from './booth.service';

@Module({
  imports: [TicketModule],
  controllers: [BoothController, BoothCreatorController],
  providers: [BoothService, BoothCreatorService],
  exports: [BoothCreatorService],
})
export class BoothModule {}
