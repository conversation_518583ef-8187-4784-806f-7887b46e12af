import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '@provider/database/prisma.service';

@Injectable()
export class BoothService {
  constructor(private readonly prismaService: PrismaService) {}

  async getBoothByBoothUuid(boothUuid: string) {
    const booth = await this.prismaService.booth.findUnique({
      where: { boothUuid },
      include: {
        boothParticipatedUser: {
          select: {
            user: {
              include: {
                bankAccount: {
                  where: { isSettlement: true },
                  include: { bank: { select: { name: true } } },
                },
                kakaopayInfo: { select: { kakao_uid: true } },
              },
            },
          },
        },
        event: true,
        goodsBoothPivot: {
          select: {
            goods: true,
            orderedCount: true,
            quantity: true,
            prepareCount: true,
            advanceCount: true,
          },
        },
        boothSourcePivot: {
          select: { source: true },
        },
      },
    });

    if (!booth) {
      throw new BadRequestException('존재하지 않는 부스입니다.');
    }

    return {
      ...booth,
      goodsBoothPivot: booth.goodsBoothPivot.map((pivot) => ({
        goods: pivot.goods,
        isPickupGoods: pivot.prepareCount > 0,
        isSoldOut:
          pivot.quantity - pivot.prepareCount - pivot.advanceCount <= 0,
      })),
    };
  }
}
