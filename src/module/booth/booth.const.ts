export const boothExample = {
  boothUuid: '01968546-400d-7cb0-805b-5c0508ffbf54',
  eventUuid: '00000000-0000-0000-0000-000000000000',
  name: 'default',
  participatedDates: ['2025-05-09T00:00:00.000Z'],
  locationTexts: ['A06', 'A07', 'A08', 'A09'],
  genres: [],
  bannerImageUrl:
    'https://lh3.googleusercontent.com/Yz5p7UmphbjQkeDnvliryK3cy1Ky5J9m_NzgfwJvX52it5oUqMwcv3I8_crlJj4Dmx-ZFrPrc6SB0ofathOlmns=w1920-h1080-rw-sm-pa-nu-v0',
  infoBlocks: ['정보 블록 1', '정보 블록 2'],
  createdAt: '2025-04-30T17:07:03.772Z',
  boothParticipatedUser: [
    {
      user: {
        userUuid: '01968546-400d-7cb0-805b-5c0508ffbf54',
        handle: '14645b5cf3a4',
        profileImg:
          'https://lh3.googleusercontent.com/a/ACg8ocIB29q1_Ru9mxKaykG3IjD1MGUrKY9bznFowfLa4jTZc-j0VA=s96-c',
        nickname: 'sweech Inc',
        explainText: null,
        realName: 'undefinedsweech Inc',
        email: '<EMAIL>',
        createdAt: '2025-04-30T05:58:15.822Z',
      },
    },
  ],
  event: {
    eventUuid: '00000000-0000-0000-0000-000000000000',
    placeUuid: '00000000-0000-0000-0000-000000000000',
    eventName: '내가 하늘에 서겟다',
    eventDate: ['2025-05-16T15:00:00.000Z', '2025-05-17T15:00:00.000Z'],
    eventLocation: '테스트',
    eventImg: '테스트',
    eventLandingUrl: '테스트',
    eventTicketUrl: '테스트',
    createdAt: '2025-04-30T23:45:36.000Z',
  },
  eventMap: [],
  goodsBoothPivot: [
    {
      goods: {
        goodsUuid: '019692de-0c0b-7780-be32-184e3b7fd13c',
        goodsCategoryUuid: '01968566-03ec-78b3-9746-b79c3adf5c69',
        name: '테스트 굿즈 5',
        price: 100,
        salesCount: 31,
        imageUrls: ['https://static.sherry.gg/sherry/products/jwak.webp'],
        isSumUp: false,
        descriptions: [],
        currency: 'krw',
        favorite: false,
        colorTag: null,
        deletedAt: null,
      },
      orderedCount: 0,
      salesCount: 0,
    },
  ],
};
