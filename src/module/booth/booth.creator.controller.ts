import { User } from '@common/decorators/jwk.decorators';
import { RequestUser } from '@common/types/token.types';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { BoothCreatorService } from './booth.creator.service';
import { BoothCreateDto } from './dto/booth.create.dto';
import { BoothDto, GetGoodsForBoothManagementDto } from './dto/booth.dto';
import { GetParticipatedBoothsQueryDto } from './dto/booth.read.dto';
import { BoothUpdateGoodsCountDto } from './dto/booth.update-good-count.dto';
import { BoothUpdateDto, DefaultBoothUpdateDto } from './dto/booth.update.dto';

@ApiTags('Booth Creator')
@ApiBearerAuth()
@Controller('/creator/booths')
export class BoothCreatorController {
  constructor(private readonly boothCreatorService: BoothCreatorService) {}

  @ApiOperation({ summary: 'Get participated booths' })
  @ApiOkResponse({ type: [BoothDto] })
  @Get()
  async getParticipatedBooths(
    @User() user: RequestUser,
    @Query() dto: GetParticipatedBoothsQueryDto,
  ) {
    return this.boothCreatorService.getBoothsForCreator({
      userUuid: user.id,
      dto,
    });
  }

  @ApiOperation({ summary: 'Get booth detail' })
  @ApiOkResponse({
    type: BoothDto,
    example: {
      name: '온라인 부스',
      event: {
        name: '179회 서울코믹월드',
        eventUuid: '019758a8-d8cb-7d23-8165-9ea72e895f3e',
      },
      boothParticipatedUser: [
        { user: { nickname: 'sweech Inc', handle: '7239b4808d8d' } },
      ],
      participatedDates: ['2025-07-12T11:28:03.698Z'],
      locationTexts: ['QU14', 'QU15'],
      bannerImageUrl: 'https://example.com/banner.jpg',
      infoBlocks: [
        'booth bio text',
        'https://example.com/booth-info-image.jpg',
        'https://example.com/booth-info-image.jpg',
      ],
      type: 'OFFLINE',
      color: '#797979',
    },
  })
  @Get(':boothUuid')
  async getBoothDetail(
    @User() user: RequestUser,
    @Param('boothUuid') boothUuid: string,
  ) {
    return this.boothCreatorService.getBooth(user.id, boothUuid);
  }

  @ApiOperation({
    summary: 'Get goods for booth management',
    description: `
    quantity : total number of items.
    boothGoodsQuantity : number of items being sold in all booths currently.
    goodsBoothPivot.quantity : number of items being sold in my booth currently. (it's include prepareCount and advanceCount. so when check actual quantity for selling available items, it should be like this : quantity - prepareCount - advanceCount)
    goodsBoothPivot.prepareCount : count for items pending to paid. ( before payment is completly finished, item will be pending so it won't be sold during payment process )
    goodsBoothPivot.advanceCount : number of items pre-paid in my booth currently.
    `,
  })
  @ApiOkResponse({ type: BoothDto })
  @Get(':boothUuid/goods')
  async getGoodsForBoothManagement(
    @User() user: RequestUser,
    @Param('boothUuid', ParseUUIDPipe) boothUuid: string,
    @Query() dto: GetGoodsForBoothManagementDto,
  ) {
    return this.boothCreatorService.getGoodsForBoothManagement(
      user.id,
      dto,
      boothUuid,
    );
  }

  @ApiOperation({ summary: 'Create booth' })
  @ApiOkResponse({ type: BoothDto })
  @Post()
  async createBooth(@User() user: RequestUser, @Body() dto: BoothCreateDto) {
    return this.boothCreatorService.createBooth(user.id, dto);
  }

  @ApiOperation({ summary: 'Update online booth' })
  @ApiOkResponse({ type: BoothDto })
  @Put()
  async putOnlineBooth(
    @User() user: RequestUser,
    @Body() dto: DefaultBoothUpdateDto,
  ) {
    return this.boothCreatorService.upsertOnlineBooth(user.id, dto);
  }

  @ApiOperation({ summary: 'Update booth' })
  @ApiOkResponse({ type: BoothDto })
  @Patch(':boothUuid')
  async patchBoothByBoothUuid(
    @User() user: RequestUser,
    @Param('boothUuid') boothUuid: string,
    @Body() dto: BoothUpdateDto,
  ) {
    return this.boothCreatorService.updateBoothByBoothUuid(
      user.id,
      boothUuid,
      dto,
    );
  }

  @ApiOperation({ summary: 'Update booth' })
  @ApiOkResponse({ type: BoothDto })
  @Patch(':boothUuid/goodsCount')
  async patchBoothGoodCount(
    @User() user: RequestUser,
    @Param('boothUuid') boothUuid: string,
    @Body() dto: BoothUpdateGoodsCountDto,
  ) {
    return this.boothCreatorService.patchBoothGoodCount(
      user.id,
      boothUuid,
      dto,
    );
  }

  @ApiOperation({ summary: 'Delete booth' })
  @ApiOkResponse({ type: BoothDto })
  @Delete(':boothUuid')
  async deleteBoothByBoothUuid(
    @User() user: RequestUser,
    @Param('boothUuid') boothUuid: string,
  ) {
    return this.boothCreatorService.deleteBoothByBoothUuid(user.id, boothUuid);
  }
}
