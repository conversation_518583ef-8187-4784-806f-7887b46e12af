import { TicketService } from '@module/ticket/ticket.service';
import { BadRequestException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { PrismaService } from '@provider/database/prisma.service';
import { BoothCreatorService } from './booth.creator.service';
import { afterEach } from 'node:test';

describe('Booth Soft Delete Functionality', () => {
  let boothCreatorService: BoothCreatorService;

  const mockPrismaService = {
    booth: {
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
      findMany: jest.fn(),
    },
    goodsBoothPivot: {
      updateMany: jest.fn(),
    },
    $transaction: jest.fn(),
    $queryRaw: jest.fn(),
    $executeRaw: jest.fn(),
  };

  const mockTicketService = {
    syncTicket: jest.fn(),
  };

  const mockBooth = {
    boothUuid: 'test-booth-uuid',
    eventUuid: 'test-event-uuid',
    name: 'Test Booth',
    participatedDates: [new Date('2024-01-01')],
    locationTexts: ['A1'],
    bannerImageUrl: 'https://example.com/banner.jpg',
    infoBlocks: ['Test info'],
    createdAt: new Date(),
    coordinates: [],
    color: '#797979',
    type: 'OFFLINE',
    deletedAt: null,
    boothParticipatedUser: [
      {
        userUuid: 'test-user-uuid',
        isAdmin: true,
        user: { nickname: 'Test User', handle: 'testuser' },
      },
    ],
    event: { name: 'Test Event', eventUuid: 'test-event-uuid' },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BoothCreatorService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: TicketService,
          useValue: mockTicketService,
        },
      ],
    }).compile();

    boothCreatorService = module.get<BoothCreatorService>(BoothCreatorService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('deleteBoothByBoothUuid', () => {
    it('should soft delete a booth successfully', async () => {
      // Arrange
      const mockTransaction = jest.fn().mockImplementation(async (callback) => {
        const mockTx = {
          booth: {
            findUnique: jest.fn().mockResolvedValue(mockBooth),
            delete: jest.fn().mockResolvedValue(mockBooth),
          },
          goodsBoothPivot: {
            updateMany: jest.fn().mockResolvedValue({ count: 1 }),
          },
        };
        return callback(mockTx);
      });
      mockPrismaService.$transaction = mockTransaction;
      mockTicketService.syncTicket.mockResolvedValue(undefined);

      // Act
      await boothCreatorService.deleteBoothByBoothUuid('test-user-uuid', 'test-booth-uuid');

      // Assert
      expect(mockTransaction).toHaveBeenCalled();
      expect(mockTicketService.syncTicket).toHaveBeenCalledWith('test-user-uuid', 'test-booth-uuid', []);
    });

    it('should throw BadRequestException when booth does not exist', async () => {
      // Arrange
      const mockTransaction = jest.fn().mockImplementation(async (callback) => {
        const mockTx = {
          booth: {
            findUnique: jest.fn().mockResolvedValue(null),
          },
        };
        // The callback should throw the exception
        return callback(mockTx);
      });
      mockPrismaService.$transaction = mockTransaction;

      // Act & Assert
      await expect(
        boothCreatorService.deleteBoothByBoothUuid('test-user-uuid', 'test-booth-uuid')
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('getBoothWithSoftDeleted', () => {
    it('should get booth excluding soft-deleted by default', async () => {
      // Arrange
      mockPrismaService.booth.findFirst.mockResolvedValue(mockBooth);

      // Act
      const result = await boothCreatorService.getBoothWithSoftDeleted(
        'test-user-uuid',
        'test-booth-uuid',
        false
      );

      // Assert
      expect(mockPrismaService.booth.findFirst).toHaveBeenCalledWith({
        where: {
          boothUuid: 'test-booth-uuid',
          boothParticipatedUser: { some: { userUuid: 'test-user-uuid', isAdmin: true } },
        },
        select: expect.objectContaining({
          boothUuid: true,
          name: true,
          type: true,
          color: true,
        }),
      });
      expect(result).toEqual(mockBooth);
    });

    it('should get booth including soft-deleted when specified', async () => {
      // Arrange
      mockPrismaService.booth.findFirst.mockResolvedValue(mockBooth);

      // Act
      const result = await boothCreatorService.getBoothWithSoftDeleted(
        'test-user-uuid',
        'test-booth-uuid',
        true
      );

      // Assert
      expect(mockPrismaService.booth.findFirst).toHaveBeenCalledWith({
        where: {
          boothUuid: 'test-booth-uuid',
          boothParticipatedUser: { some: { userUuid: 'test-user-uuid', isAdmin: true } },
          deletedAt: 'all',
        },
        select: expect.objectContaining({
          boothUuid: true,
          name: true,
          type: true,
          color: true,
        }),
      });
      expect(result).toEqual(mockBooth);
    });
  });

  describe('restoreBoothByBoothUuid', () => {
    it('should restore a soft-deleted booth successfully', async () => {
      // Arrange
      const mockTransaction = jest.fn().mockImplementation(async (callback) => {
        const mockTx = {
          $queryRaw: jest.fn().mockResolvedValue([{
            booth_uuid: 'test-booth-uuid',
            name: 'Test Booth',
            participated_dates: [new Date('2024-01-01')],
            deleted_at: new Date(),
          }]),
          $executeRaw: jest.fn().mockResolvedValue(1),
        };
        return callback(mockTx);
      });
      mockPrismaService.$transaction = mockTransaction;
      mockTicketService.syncTicket.mockResolvedValue(undefined);

      // Act
      const result = await boothCreatorService.restoreBoothByBoothUuid(
        'test-user-uuid',
        'test-booth-uuid'
      );

      // Assert
      expect(mockTransaction).toHaveBeenCalled();
      expect(mockTicketService.syncTicket).toHaveBeenCalled();
      expect(result).toEqual({ message: '부스가 복원되었습니다.' });
    });

    it('should throw BadRequestException when soft-deleted booth does not exist', async () => {
      // Arrange
      const mockTransaction = jest.fn().mockImplementation(async (callback) => {
        const mockTx = {
          $queryRaw: jest.fn().mockResolvedValue([]),
        };
        return callback(mockTx);
      });
      mockPrismaService.$transaction = mockTransaction;

      // Act & Assert
      await expect(
        boothCreatorService.restoreBoothByBoothUuid('test-user-uuid', 'test-booth-uuid')
      ).rejects.toThrow(BadRequestException);
    });
  });


});
