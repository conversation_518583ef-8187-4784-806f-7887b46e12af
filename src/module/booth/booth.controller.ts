import { IsPublic } from '@common/decorators/public.decorator';
import { Controller, Get, Param } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { boothExample } from './booth.const';
import { BoothService } from './booth.service';
import { BoothDto } from './dto/booth.dto';

@ApiTags('Booth')
@ApiBearerAuth()
@Controller('booths')
export class BoothController {
  constructor(private readonly boothService: BoothService) {}

  @ApiOperation({ summary: 'Get booth information by boothUuid' })
  @ApiOkResponse({
    type: BoothDto,
    example: boothExample,
  })
  @IsPublic()
  @Get(':boothUuid')
  async getBoothInformation(@Param('boothUuid') boothUuid: string) {
    return this.boothService.getBoothByBoothUuid(boothUuid);
  }
}
