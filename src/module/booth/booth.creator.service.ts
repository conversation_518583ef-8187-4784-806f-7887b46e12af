import { ONLINE_EVENT_AND_LOCATION_UUID } from '@common/lib/const';
import { getStartDateOfDay } from '@common/lib/get-date-of-day';
import { TicketService } from '@module/ticket/ticket.service';
import { BadRequestException, Injectable } from '@nestjs/common';
import { BoothTypeEnum, Prisma } from '@prisma/client';
import { PrismaService } from '@provider/database/prisma.service';
import { isDefined } from 'class-validator';
import dayjs from 'dayjs';
import type { BoothCreateDto } from './dto/booth.create.dto';
import type { GetGoodsForBoothManagementDto } from './dto/booth.dto';
import type { GetParticipatedBoothsQueryDto } from './dto/booth.read.dto';
import { BoothUpdateGoodsCountDto } from './dto/booth.update-good-count.dto';
import type {
  BoothUpdateDto,
  DefaultBoothUpdateDto,
} from './dto/booth.update.dto';

@Injectable()
export class BoothCreatorService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly ticketService: TicketService,
  ) {}

  async getBoothsForCreator({
    userUuid,
    dto,
  }: {
    userUuid: string;
    dto: GetParticipatedBoothsQueryDto;
  }) {
    const whereCondition: Prisma.BoothWhereInput = {
      boothParticipatedUser: {
        some: { userUuid, ...(dto.isPos ? {} : { isAdmin: true }) },
      },
      ...(isDefined(dto.name) && { name: dto.name }),
      ...(isDefined(dto.eventUuid) && { eventUuid: dto.eventUuid }),
    };
    const [booths, count] = await this.prismaService.$transaction([
      this.prismaService.booth.findMany({
        select: {
          boothUuid: true,
          name: true,
          boothParticipatedUser: {
            select: {
              isAdmin: true,
              user: { select: { nickname: true } },
            },
          },
          event: { select: { name: true } },
          participatedDates: true,
          type: true,
          locationTexts: true,
        },
        where: whereCondition,
        ...dto.toPrismaPaging(),
      }),
      this.prismaService.booth.count({
        where: whereCondition,
      }),
    ]);
    return dto.createResponse(booths, count);
  }

  async getBooth(userUuid: string, boothUuid: string) {
    const booth = await this.prismaService.booth.findUnique({
      where: {
        boothUuid,
        boothParticipatedUser: { some: { userUuid, isAdmin: true } },
      },
      select: {
        boothUuid: true,
        name: true,
        event: { select: { name: true, eventUuid: true } },
        boothParticipatedUser: {
          select: { user: { select: { nickname: true, handle: true } } },
        },
        participatedDates: true,
        locationTexts: true,
        bannerImageUrl: true,
        infoBlocks: true,
        type: true,
        color: true,
      },
    });
    if (!booth) {
      throw new BadRequestException('존재하지 않는 부스입니다.');
    }
    return booth;
  }

  async createBooth(userUuid: string, dto: BoothCreateDto) {
    if (
      dto.participatedDates.some((date) => dayjs(date).isBefore(new Date()))
    ) {
      throw new BadRequestException('participatedDates must be in the future.');
    }
    // 트랜잭션을 사용하여 부스 생성과 티켓 동기화를 원자적으로 처리
    return this.prismaService.$transaction(async (tx) => {
      // 이벤트 존재 여부 확인
      const event = await tx.event.findUnique({
        where: { eventUuid: dto.eventUuid },
      });
      if (!event) {
        throw new BadRequestException('존재하지 않는 행사입니다.');
      }

      // 부스 생성
      const booth = await tx.booth.create({
        data: {
          eventUuid: dto.eventUuid,
          name: dto.name,
          locationTexts: dto.locationTexts || [],
          bannerImageUrl: dto.bannerImageUrl || '', // FIXME: 나중에 기본 배너 이미지 추가 필요
          infoBlocks: dto.infoBlocks || [],
          boothParticipatedUser: {
            create: { userUuid, isAdmin: true, createdAt: new Date() },
          },
          goodsBoothPivot: {
            createMany: {
              data: dto.goodsBoothPivot.map((pivot) => ({
                goodsUuid: pivot.goodsUuid,
                quantity: pivot.amount,
              })),
            },
          },
        },
      });

      // 부스 생성 시 티켓 동기화
      // ConflictException(티켓 부족) 발생 시 전체 트랜잭션이 롤백됨
      await this.ticketService.syncTicket(
        userUuid,
        booth.boothUuid,
        dto.participatedDates,
        tx,
      );

      return {
        boothUuid: booth.boothUuid,
      };
    });
  }

  async updateBoothByBoothUuid(
    userUuid: string,
    boothUuid: string,
    { goodsBoothPivot, ...dto }: BoothUpdateDto,
  ) {
    const existBooth = await this.prismaService.booth.findUnique({
      where: {
        boothUuid,
        boothParticipatedUser: { some: { userUuid, isAdmin: true } },
      },
    });

    if (!existBooth) {
      throw new BadRequestException('존재하지 않는 부스입니다.');
    }

    await this.prismaService.$transaction(async (tx) => {
      await tx.booth.update({
        where: { boothUuid },
        data: dto,
      });

      // participatedDates가 변경된 경우 티켓 동기화
      if (dto.participatedDates) {
        const newParticipatedDates = dto.participatedDates.map((date) =>
          getStartDateOfDay(date),
        );
        await this.ticketService.syncTicket(
          userUuid,
          boothUuid,
          newParticipatedDates,
        );
      }
      const beforeGoodsBoothPivot = await tx.goodsBoothPivot.findMany({
        where: { boothUuid },
      });
      const goodsBoothPivotUuidsToDelete = beforeGoodsBoothPivot
        .filter(
          (beforePivot) =>
            !goodsBoothPivot.some(
              (pivot) => pivot.goodsUuid === beforePivot.goodsUuid,
            ),
        )
        .map((pivot) => pivot.goodsUuid);
      await tx.goodsBoothPivot.deleteMany({
        where: { boothUuid, goodsUuid: { in: goodsBoothPivotUuidsToDelete } },
      });
      await Promise.all(
        goodsBoothPivot.map((pivot) =>
          tx.goodsBoothPivot.upsert({
            where: {
              goodsUuid_boothUuid: {
                goodsUuid: pivot.goodsUuid,
                boothUuid,
              },
            },
            create: {
              boothUuid,
              goodsUuid: pivot.goodsUuid,
              quantity: pivot.amount,
            },
            update: {
              quantity: pivot.amount,
              deletedAt: null,
            },
          }),
        ),
      );
    });
  }

  async upsertOnlineBooth(userUuid: string, dto: DefaultBoothUpdateDto) {
    await this.prismaService.booth.upsert({
      where: { boothUuid: userUuid },
      create: {
        boothUuid: userUuid,
        eventUuid: ONLINE_EVENT_AND_LOCATION_UUID,
        name: dto.name,
        participatedDates: [new Date()],
        locationTexts: [],
        bannerImageUrl: dto.bannerImageUrl || '', // FIXME: 나중에 기본 배너 이미지 추가 필요
        infoBlocks: dto.infoBlocks,
        boothParticipatedUser: {
          create: { userUuid, isAdmin: true, createdAt: new Date() },
        },
        type: BoothTypeEnum.ONLINE,
      },
      update: {
        name: dto.name,
        bannerImageUrl: dto.bannerImageUrl,
        infoBlocks: dto.infoBlocks,
      },
    });
  }

  async getGoodsForBoothManagement(
    userUuid: string,
    dto: GetGoodsForBoothManagementDto,
    boothUuid: string,
  ) {
    const whereCondition: Prisma.GoodsWhereInput = {
      OR: [
        {
          goodsUser: { some: { userUuid } },
          goodsBoothPivot: { some: { boothUuid } },
        },
      ],
      ...(dto.name && { name: { contains: dto.name } }),
      ...(dto.sellingMethods?.length && {
        sellingMethods: { hasSome: dto.sellingMethods },
      }),
      ...(dto.goodsCategoryUuids?.length && {
        goodsCategoryUuid: { in: dto.goodsCategoryUuids },
      }),
      ...(dto.characterUuids?.length && {
        goodsCharacterPivot: {
          some: { characterUuid: { in: dto.characterUuids } },
        },
      }),
    };
    const [goods, count] = await this.prismaService.$transaction([
      this.prismaService.goods.findMany({
        where: whereCondition,
        include: {
          goodsCharacterPivot: { include: { character: true } },
          goodsCategory: { select: { name: true } },
          goodsBoothPivot: {
            where: { boothUuid },
            select: {
              quantity: true,
              prepareCount: true,
              advanceCount: true,
              sellingMethods: true,
            },
          },
        },
        orderBy: { goodsUuid: Prisma.SortOrder.desc },
        ...dto.toPrismaPaging(),
      }),
      this.prismaService.goods.count({
        where: whereCondition,
      }),
    ]);
    return dto.createResponse(goods, count);
  }

  async deleteBoothByBoothUuid(userUuid: string, boothUuid: string) {
    await this.prismaService.$transaction(async (tx) => {
      const existBooth = await tx.booth.findUnique({
        where: {
          boothUuid,
          boothParticipatedUser: { some: { userUuid, isAdmin: true } },
        },
      });

      if (!existBooth) {
        throw new BadRequestException('존재하지 않는 부스입니다.');
      }

      // 부스 삭제 전 티켓 동기화 (빈 배열로 모든 날짜 제거)
      await this.ticketService.syncTicket(userUuid, boothUuid, []);

      // Soft delete related GoodsBoothPivot records first
      await tx.goodsBoothPivot.updateMany({
        where: { boothUuid },
        data: { deletedAt: new Date() },
      });

      // Soft delete the booth - the middleware will automatically convert this to setting deletedAt
      await tx.booth.delete({
        where: { boothUuid },
      });
    });
  }

  /**
   * Get booth including soft-deleted records
   * @param userUuid User UUID
   * @param boothUuid Booth UUID
   * @param includeSoftDeleted Whether to include soft-deleted records
   */
  async getBoothWithSoftDeleted(
    userUuid: string,
    boothUuid: string,
    includeSoftDeleted = false,
  ) {
    // Use findFirst with proper where conditions for soft delete support
    const whereCondition: Prisma.BoothWhereInput = {
      boothUuid,
      boothParticipatedUser: { some: { userUuid, isAdmin: true } },
      ...(includeSoftDeleted && { deletedAt: 'all' as any }), // Special value to include all records
    };

    const booth = await this.prismaService.booth.findFirst({
      where: whereCondition,
      select: {
        boothUuid: true,
        name: true,
        event: { select: { name: true, eventUuid: true } },
        boothParticipatedUser: {
          select: { user: { select: { nickname: true, handle: true } } },
        },
        participatedDates: true,
        locationTexts: true,
        bannerImageUrl: true,
        infoBlocks: true,
        type: true,
        color: true,
        // Note: deletedAt will be available after Prisma client regeneration
      },
    });

    if (!booth) {
      throw new BadRequestException('존재하지 않는 부스입니다.');
    }

    return booth;
  }

  /**
   * Restore a soft-deleted booth
   * @param userUuid User UUID
   * @param boothUuid Booth UUID
   */
  async restoreBoothByBoothUuid(userUuid: string, boothUuid: string) {
    // Use transaction to ensure atomicity
    return this.prismaService.$transaction(async (tx) => {
      // First check if the booth exists and is actually soft-deleted
      // Use raw query to bypass middleware and check deletedAt directly
      const existBooth = await tx.$queryRaw<
        Array<{
          booth_uuid: string;
          name: string;
          participated_dates: Date[];
          deleted_at: Date | null;
        }>
      >`
        SELECT b.booth_uuid, b.name, b.participated_dates, b.deleted_at
        FROM booth b
        INNER JOIN booth_participated_user bpu ON b.booth_uuid = bpu.booth_uuid
        WHERE b.booth_uuid = ${boothUuid}::uuid
          AND bpu.user_uuid = ${userUuid}::uuid
          AND bpu.is_admin = true
          AND b.deleted_at IS NOT NULL
      `;

      if (!existBooth || existBooth.length === 0) {
        throw new BadRequestException('존재하지 않는 삭제된 부스입니다.');
      }

      const booth = existBooth[0];

      // Restore related GoodsBoothPivot records first
      await tx.$executeRaw`
        UPDATE goods_booth_pivot
        SET deleted_at = NULL
        WHERE booth_uuid = ${boothUuid}::uuid
          AND deleted_at IS NOT NULL
      `;

      // Restore the booth by setting deletedAt to null
      await tx.$executeRaw`
        UPDATE booth
        SET deleted_at = NULL
        WHERE booth_uuid = ${boothUuid}::uuid
      `;

      // Re-sync tickets after restoration
      await this.ticketService.syncTicket(
        userUuid,
        boothUuid,
        booth.participated_dates,
      );

      return { message: '부스가 복원되었습니다.' };
    });
  }

  async patchBoothGoodCount(
    id: string,
    boothUuid: string,
    dto: BoothUpdateGoodsCountDto,
  ) {
    const goodsList = await this.prismaService.goods.findMany({
      where: {
        goodsUuid: {
          in: dto.goodsList.map((item) => item.goodsUuid),
        },
        goodsUser: {
          some: {
            userUuid: id,
          },
        },
        goodsBoothPivot: {
          some: {
            boothUuid: boothUuid,
          },
        },
      },
      select: {
        goodsUuid: true,
        quantity: true,
        boothGoodsQuantity: true,
        goodsBoothPivot: {
          where: { boothUuid: boothUuid },
          include: { booth: true },
        },
      },
    });

    if (goodsList.length !== dto.goodsList.length) {
      throw new BadRequestException('상품이 존재하지 않습니다.');
    }
    const { boothPivotUpdate, goodsUpdate } = dto.goodsList.reduce(
      (
        cu: {
          boothPivotUpdate: Prisma.GoodsBoothPivotUpdateArgs[];
          goodsUpdate: Prisma.GoodsUpdateArgs[];
        },
        item,
      ) => {
        const good = goodsList.find(
          (good) => good.goodsUuid === item.goodsUuid,
        );
        if (!good) {
          throw new BadRequestException(`상품이 존재하지 않습니다. [${item}]`);
        }
        const boothGoodsPivot = good.goodsBoothPivot[0];
        if (!boothGoodsPivot) {
          throw new BadRequestException(
            `부스에 등록되지 않은 상품입니다. [${item.goodsUuid}]`,
          );
        }
        if (good.quantity - good.boothGoodsQuantity - item.goodsCount < 0) {
          throw new BadRequestException(
            `판매 수량이 초과되었습니다. [${item.goodsUuid}]`,
          );
        }
        const count = boothGoodsPivot.quantity - item.goodsCount;
        cu.boothPivotUpdate.push({
          where: {
            goodsUuid_boothUuid: {
              goodsUuid: item.goodsUuid,
              boothUuid: boothGoodsPivot.boothUuid,
            },
          },
          data: {
            quantity: item.goodsCount,
          },
        });
        cu.goodsUpdate.push({
          where: { goodsUuid: item.goodsUuid },
          data: {
            boothGoodsQuantity: {
              increment: count,
            },
          },
        });

        return cu;
      },
      {
        boothPivotUpdate: [],
        goodsUpdate: [],
      },
    );

    await this.prismaService.$transaction([
      ...boothPivotUpdate.map((update) =>
        this.prismaService.goodsBoothPivot.update(update),
      ),
      ...goodsUpdate.map((update) => this.prismaService.goods.update(update)),
    ]);
  }
}
