import { UserRole } from '@common/types/token.types';
import { AppConfigService } from '@config/app/config.service';
import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class AuthenticationService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly appConfigService: AppConfigService,
  ) {}

  async getTokens(arg: {
    userId: string;
    role: UserRole;
    snsPlatformId: number;
  }) {
    const now = new Date();
    const accessTokenExpiresIn = new Date(
      now.getTime() + this.appConfigService.accessTokenExpire * 1000,
    );
    const refreshTokenExpiresIn = new Date(
      now.getTime() + this.appConfigService.refreshTokenExpire * 1000,
    );
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(
        {
          sub: arg.userId,
          r: arg.role,
          s: arg.snsPlatformId,
        },
        {
          algorithm: 'RS256',
          issuer: this.appConfigService.jwtIssuer,
          privateKey: this.appConfigService.jwtPrivate,
          expiresIn: this.appConfigService.accessTokenExpire,
        },
      ),
      this.jwtService.signAsync(
        {
          sub: arg.userId,
          h: '여기에 뭐가 들어갈지 아직 생각하지 못했음',
        },
        {
          algorithm: 'RS256',
          issuer: this.appConfigService.jwtIssuer,
          privateKey: this.appConfigService.jwtPrivate,
          expiresIn: this.appConfigService.refreshTokenExpire,
        },
      ),
    ]);

    return {
      accessToken,
      accessTokenExpiresIn,
      refreshToken,
      refreshTokenExpiresIn,
    };
  }
}
