import { ApiProperty } from '@nestjs/swagger';
import { PayMethodEnum } from '@prisma/client';
import { IsIn, IsNotEmpty, IsUUID } from 'class-validator';
import { PaymentCreateDefaultDto } from './payment.create.default.dto';

export class PaymentCreateDirectDto extends PaymentCreateDefaultDto {
  @ApiProperty({
    description: `결제 타입 - ${PayMethodEnum.cash}: cash, ${PayMethodEnum.directTransfer}: 직접 계좌 결제, ${PayMethodEnum.share}: 무료 나눔처리`,
    enum: [
      PayMethodEnum.cash,
      PayMethodEnum.directTransfer,
      PayMethodEnum.share,
    ],
  })
  @IsIn([PayMethodEnum.cash, PayMethodEnum.directTransfer, PayMethodEnum.share])
  @IsNotEmpty()
  payMethod!: Pick<
    typeof PayMethodEnum,
    'cash' | 'directTransfer' | 'share'
  >[keyof Pick<typeof PayMethodEnum, 'cash' | 'directTransfer' | 'share'>];

  @ApiProperty({
    description: 'UUID of Booth',
    type: String,
  })
  @IsUUID()
  @IsNotEmpty()
  boothUuid!: string;
}
