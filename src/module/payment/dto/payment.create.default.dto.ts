import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsInt,
  IsNotEmpty,
  IsUUID,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

export class PaymentGoods {
  @ApiProperty({
    description: '상품 uuid',
    type: String,
  })
  @IsUUID()
  @IsNotEmpty()
  goodsUuid!: string;

  @ApiProperty({
    description: '상품 수량',
    type: Number,
    required: true,
  })
  @Type(() => Number)
  @IsNotEmpty()
  @IsInt()
  @Min(1, { message: '상품 수량은 최소 1개 이상이어야 합니다.' })
  goodsCount!: number;
}

export class PaymentCreateDefaultDto {
  @ApiProperty({
    description: '상품 리스트',
    type: [PaymentGoods],
    required: true,
  })
  @IsNotEmpty()
  @ArrayMinSize(1, { message: '상품 리스트는 최소 1개 이상이어야 합니다.' })
  @Type(() => PaymentGoods)
  @ValidateNested({ each: true })
  goodsList!: PaymentGoods[];
}

export class PaymentCustomerGoods extends PaymentGoods {
  // 부스 uuid
  @ApiProperty({
    description: [
      '부스 UUID \\n 현장수령의 경우 필수',
      'Booth uuid from payment created \\n Required for on-site pickup',
    ].join('\\n'),
    type: String,
    required: false,
  })
  @IsUUID('7')
  @IsNotEmpty()
  @ValidateIf((o) => o.isPickup)
  boothUuid?: string;
}

export class PaymentCreateDefaultCustomerDto {
  @ApiProperty({
    description: '상품 리스트',
    type: [PaymentGoods],
    required: true,
  })
  @IsNotEmpty()
  @ArrayMinSize(1, { message: '상품 리스트는 최소 1개 이상이어야 합니다.' })
  @Type(() => PaymentGoods)
  @ValidateNested({ each: true })
  goodsList!: PaymentCustomerGoods[];
}
