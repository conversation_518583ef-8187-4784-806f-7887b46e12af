import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';
import { PaymentCreateDefaultDto } from './payment.create.default.dto';

export class PaymentCreateLinkDto extends PaymentCreateDefaultDto {
  @ApiProperty({
    description: 'Booth uuid from payment created',
    type: String,
    required: true,
  })
  @IsUUID('7')
  @IsNotEmpty()
  boothUuid!: string;
}
