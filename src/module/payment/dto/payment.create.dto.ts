import { ApiProperty } from '@nestjs/swagger';
import { PayMethodEnum } from '@prisma/client';
import {
  IsIn,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateIf,
} from 'class-validator';
import { PaymentCreateDefaultCustomerDto } from './payment.create.default.dto';

export class PaymentCreate extends PaymentCreateDefaultCustomerDto {
  @ApiProperty({
    description: [
      `결제 타입 - ${PayMethodEnum.account}: 계좌 (Currently not supported.)`,
      `${PayMethodEnum.card}: 카드 결제`,
      'Payment type - account: bank transfer (Currently not supported), card: card payment',
    ].join('\\n'),
    enum: [PayMethodEnum.account, PayMethodEnum.card],
  })
  @IsIn([PayMethodEnum.account, PayMethodEnum.card])
  @IsNotEmpty()
  payMethod!: Pick<typeof PayMethodEnum, 'account' | 'card'>[keyof Pick<
    typeof PayMethodEnum,
    'account' | 'card'
  >];

  // 현장수령 여부
  @ApiProperty({
    description: ['현장수령 여부', 'Whether on-site pickup is available'].join(
      '\\n',
    ),
    type: Boolean,
    required: false,
    default: true,
  })
  @IsOptional()
  isPickup = true;

  @ApiProperty({
    description: ['받는 사람 이름', 'Recipient name'].join('\\n'),
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  @ValidateIf((o) => !o.isPickup)
  recipientName?: string;

  @ApiProperty({
    description: ['받는 사람 전화번호', 'Recipient phone number'].join('\\n'),
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  @ValidateIf((o) => !o.isPickup)
  recipientPhone?: string;

  @ApiProperty({
    description: ['받는 사람 주소', 'Recipient address'].join('\\n'),
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  @ValidateIf((o) => !o.isPickup)
  recipientAddress?: string;

  @ApiProperty({
    description: ['우편번호', 'Postal code'].join('\\n'),
    type: String,
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  @ValidateIf((o) => !o.isPickup)
  postalCode?: string;

  @ApiProperty({
    description: [
      '배송시 요청사항 (선택사항) \\n 현장 수령이 아니면 무시됩니다.',
      'Delivery request (optional) \\n Will be ignored if not for on-site pickup.',
    ].join('\\n'),
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString()
  comment?: string;
}
