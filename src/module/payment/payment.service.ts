import { vatTaxCalculation } from '@common/helpers/vat-tax-calculation.helper';
import { uuidv7 } from '@common/lib/uuidv7';
import { RequestUser } from '@common/types/token.types';
import { PaypleService } from '@module/payple/payple.service';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
} from '@nestjs/common';
import { PayMethodEnum, PaymentStatusEnum, Prisma } from '@prisma/client';
import { PrismaService } from '@provider/database/prisma.service';
import dayjs from 'dayjs';
import { compact } from 'lodash';
import { PaymentConfirmDto } from './dto/payment-confirm-dto';
import { PaymentPageDto } from './dto/payment-page.dto';
import { PaymentCreateLinkDto } from './dto/payment.create-link.dto';
import { PaymentCreateDirectDto } from './dto/payment.create.direct.dto';
import { PaymentCreate } from './dto/payment.create.dto';

@Injectable()
export class PaymentService {
  constructor(
    private readonly paypleService: PaypleService,
    private readonly prismaService: PrismaService,
  ) {}

  async paymentDirect(user: RequestUser, dto: PaymentCreateDirectDto) {
    const goodsList = await this.prismaService.goods.findMany({
      where: {
        goodsUuid: {
          in: dto.goodsList.map((item) => item.goodsUuid),
        },
        goodsUser: {
          some: {
            userUuid: user.id,
          },
        },
        goodsBoothPivot: {
          some: {
            boothUuid: dto.boothUuid,
          },
        },
      },
      select: {
        goodsUuid: true,
        price: true,
        name: true,
        quantity: true,
        boothGoodsQuantity: true,
        goodsUser: true,
        goodsBoothPivot: {
          where: { boothUuid: dto.boothUuid },
          include: { booth: true },
        },
      },
    });

    if (goodsList.length !== dto.goodsList.length) {
      throw new BadRequestException('상품이 존재하지 않습니다.');
    }
    const salesOrder: Prisma.SalesOrderCreateManyInput[] = [];
    const paymentTitle =
      goodsList.length > 1
        ? `${goodsList[0].name} 외 ${goodsList.length - 1}개`
        : goodsList[0].name;
    const orderId = uuidv7();

    let totalPrice = 0;
    const prepareGoods: Prisma.OrderedGoodsCreateManyInput[] =
      dto.goodsList.flatMap((item) => {
        const good = goodsList.find(
          (good) => good.goodsUuid === item.goodsUuid,
        );
        if (!good) {
          throw new BadRequestException(
            `상품이 존재하지 않습니다. [${item.goodsUuid}]`,
          );
        }
        const boothGoodsPivot = good.goodsBoothPivot[0];
        if (!boothGoodsPivot) {
          throw new BadRequestException(
            `부스에 등록되지 않은 상품입니다. [${item.goodsUuid}]`,
          );
        }
        if (
          boothGoodsPivot.quantity -
            boothGoodsPivot.prepareCount -
            boothGoodsPivot.advanceCount -
            item.goodsCount <
          0
        ) {
          throw new BadRequestException(
            `판매 수량이 초과되었습니다. [${item.goodsUuid}]`,
          );
        }
        totalPrice += good.price * item.goodsCount;
        return good.goodsUser.map((goodsUser) => {
          const goodsPrice =
            good.price * item.goodsCount * (goodsUser.ratio / 100);
          const existSalesOrder = salesOrder.find(
            (order) => order.userUuid === goodsUser.userUuid,
          );
          if (existSalesOrder) {
            existSalesOrder.totalGoodsPrice += goodsPrice;
            existSalesOrder.totalVatPrice +=
              vatTaxCalculation(goodsPrice).vatAmount;
          } else {
            salesOrder.push({
              userUuid: goodsUser.userUuid,
              totalGoodsPrice: goodsPrice,
              totalVatPrice: vatTaxCalculation(goodsPrice).vatAmount,
              orderUuid: orderId,
              orderStatus: PaymentStatusEnum.ORDER_COMPLETE,
              orderTitle: paymentTitle,
              payMethod: PayMethodEnum.card,
            });
          }
          return {
            orderUuid: orderId,
            deliveryStatus: null,
            goodsOrderStatus: PaymentStatusEnum.ORDER_COMPLETE,
            goodsCount: item.goodsCount,
            goodsUuid: item.goodsUuid,
            goodsName: good.name,
            goodsPrice: goodsPrice,
            vatPrice: vatTaxCalculation(goodsPrice).vatAmount,
            sellerUuid: goodsUser.userUuid,
            ratio: goodsUser.ratio,
            boothUuid: dto.boothUuid,
            boothName: good.goodsBoothPivot[0].booth.name,
          };
        });
      });

    const [prepareOrder] = await this.prismaService.$transaction([
      this.prismaService.order.create({
        data: {
          orderUuid: orderId,
          linkKey: null,
          orderTitle: paymentTitle,
          totalGoodsPrice: totalPrice,
          totalVatPrice: totalPrice * 0.1,
          totalDeliveryPrice: null,
          payMethod: dto.payMethod,
          orderStatus: PaymentStatusEnum.ORDER_COMPLETE,
        },
      }),
      this.prismaService.salesOrder.createMany({
        data: salesOrder,
      }),
      this.prismaService.orderedGoods.createMany({
        data: prepareGoods,
      }),
    ]);

    return {
      orderUuid: prepareOrder.orderUuid,
    };
  }

  async paymentCreateLink(user: RequestUser, dto: PaymentCreateLinkDto) {
    const goodsList = await this.prismaService.goods.findMany({
      where: {
        goodsUuid: {
          in: dto.goodsList.map((item) => item.goodsUuid),
        },
        goodsUser: {
          some: {
            userUuid: user.id,
          },
        },
        goodsBoothPivot: {
          some: {
            boothUuid: dto.boothUuid,
          },
        },
      },
      select: {
        goodsUuid: true,
        price: true,
        name: true,
        quantity: true,
        boothGoodsQuantity: true,
        goodsUser: true,
        goodsBoothPivot: {
          where: { boothUuid: dto.boothUuid },
          include: { booth: true },
        },
      },
    });

    if (goodsList.length !== dto.goodsList.length) {
      throw new BadRequestException('상품이 존재하지 않습니다.');
    }
    const salesOrder: Prisma.PrepareSalesOrderCreateManyInput[] = [];
    const paymentTitle =
      goodsList.length > 1
        ? `${goodsList[0].name} 외 ${goodsList.length - 1}개`
        : goodsList[0].name;
    const orderId = uuidv7();

    const paymentExplain = goodsList.map((item) => item.name).join(', ');
    let totalPrice = 0;
    const prepareGoods: Prisma.PrepareOrderedGoodsCreateManyInput[] =
      dto.goodsList.flatMap((item) => {
        const good = goodsList.find(
          (good) => good.goodsUuid === item.goodsUuid,
        );
        if (!good) {
          throw new BadRequestException(
            `상품이 존재하지 않습니다. [${item.goodsUuid}]`,
          );
        }
        const boothGoodsPivot = good.goodsBoothPivot[0];
        if (!boothGoodsPivot) {
          throw new BadRequestException(
            `부스에 등록되지 않은 상품입니다. [${item.goodsUuid}]`,
          );
        }
        if (
          boothGoodsPivot.quantity -
            boothGoodsPivot.prepareCount -
            boothGoodsPivot.advanceCount -
            item.goodsCount <
          0
        ) {
          throw new BadRequestException(
            `판매 수량이 초과되었습니다. [${item.goodsUuid}]`,
          );
        }
        totalPrice += good.price * item.goodsCount;
        return good.goodsUser.map((user) => {
          const goodsPrice = good.price * item.goodsCount * (user.ratio / 100);
          const existSalesOrder = salesOrder.find(
            (order) => order.userUuid === user.userUuid,
          );
          if (existSalesOrder) {
            existSalesOrder.totalGoodsPrice += goodsPrice;
            existSalesOrder.totalVatPrice +=
              vatTaxCalculation(goodsPrice).vatAmount;
          } else {
            salesOrder.push({
              userUuid: user.userUuid,
              totalGoodsPrice: goodsPrice,
              totalVatPrice: vatTaxCalculation(goodsPrice).vatAmount,
              orderUuid: orderId,
              orderStatus: PaymentStatusEnum.ORDER_COMPLETE,
              orderTitle: paymentTitle,
              payMethod: PayMethodEnum.card,
            });
          }
          return {
            orderUuid: orderId,
            deliveryStatus: null,
            goodsOrderStatus: PaymentStatusEnum.ORDER_COMPLETE,
            goodsCount: item.goodsCount,
            goodsUuid: item.goodsUuid,
            goodsName: good.name,
            goodsPrice: goodsPrice,
            vatPrice: vatTaxCalculation(goodsPrice).vatAmount,
            sellerUuid: user.userUuid,
            ratio: user.ratio,
            boothUuid: dto.boothUuid,
            boothName: good.goodsBoothPivot[0].booth.name,
          };
        });
      });

    const key = (await this.paypleService.getPartnersKey('LINKREG')).data;

    const link = await this.paypleService.paypleCreateLink({
      pcd_auth_key: key.AuthKey,
      pcd_cst_id: key.cst_id,
      pcd_cust_key: key.custKey,
      pcd_pay_goods: paymentTitle,
      pcd_pay_goods_explain: paymentExplain,
      pcd_pay_istax: 'Y',
      pcd_pay_total: totalPrice,
      pcd_pay_type: 'card',
      pcd_pay_work: 'LINKREG',
      pcd_card_ver: '02',
      pcd_goods_amount: 1,
      pcd_link_parameter: `sellerUuid=${user.id}`,
      pcd_link_expiredate: dayjs()
        .add(1.5, 'hour')
        .locale('ko')
        .format('YYYYMMDDHH'), // 가능 범위: 30분 ~ 1시간 30분
      pcd_link_memo: 'https://sherry.gg',
    });
    if (!link.PCD_LINK_URL) {
      throw new InternalServerErrorException('Payple link creation failed');
    }
    const [prepareOrder] = await this.prismaService.$transaction([
      this.prismaService.prepareOrder.create({
        data: {
          orderUuid: orderId,
          linkKey: link.PCD_LINK_KEY,
          orderTitle: paymentTitle,
          totalGoodsPrice: totalPrice,
          totalVatPrice: totalPrice * 0.1,
          totalDeliveryPrice: null,
          payMethod: PayMethodEnum.card,
          orderStatus: PaymentStatusEnum.ORDER_COMPLETE,
        },
      }),
      this.prismaService.prepareSalesOrder.createMany({
        data: salesOrder,
      }),
      this.prismaService.prepareOrderedGoods.createMany({
        data: prepareGoods,
      }),
    ]);

    return {
      url: link.PCD_LINK_URL + '?OID=' + prepareOrder.orderUuid,
      orderUuid: prepareOrder.orderUuid,
    };
  }
  async paymentConfirm(user: RequestUser, dto: PaymentConfirmDto) {
    const prepareOrder = await this.prismaService.prepareOrder.findUnique({
      where: {
        orderUuid: dto.orderUuid,
        payMethod: PayMethodEnum.card, // TODO: 현재는 카드결제만 준비되어 있습니다.
      },
      include: {
        prepareOrderedGoods: true,
      },
    });

    let exist = true;
    let completed = false;

    if (!prepareOrder) {
      const order = await this.prismaService.order.findUnique({
        where: {
          orderUuid: dto.orderUuid,
        },
      });
      exist = false;
      if (order) {
        exist = true;
        completed = true;
      }
    }
    return {
      exist,
      completed,
    };
  }

  async paymentOrderId(user: RequestUser, dto: PaymentCreate) {
    // 1. 변수 선언을 메소드 상단으로 이동 (변수 선언 순서 문제 해결)
    const salesOrder: Prisma.PrepareSalesOrderCreateManyInput[] = [];
    const orderUuid = uuidv7();
    let totalPrice = 0;

    // 2. 트랜잭션으로 재고 검증과 주문 생성을 원자적으로 처리 (Race Condition 방지)
    const result = await this.prismaService.$transaction(async (tx) => {
      // 상품 정보 조회
      const goodsList = await tx.goods.findMany({
        where: {
          OR: dto.goodsList.map((item) => ({
            goodsUuid: item.goodsUuid,
            ...(item.boothUuid
              ? { goodsBoothPivot: { some: { boothUuid: item.boothUuid } } }
              : {}),
          })),
        },
        select: {
          goodsUuid: true,
          price: true,
          name: true,
          quantity: true,
          boothGoodsQuantity: true,
          goodsUser: true,
        },
      });

      if (goodsList.length !== dto.goodsList.length) {
        throw new BadRequestException('상품이 존재하지 않습니다.');
      }

      // 부스 상품 정보 조회 (pickup인 경우)
      const boothUuids = compact(
        dto.goodsList.map((item) => {
          if (item.boothUuid) {
            return {
              boothUuid: item.boothUuid,
              goodsUuid: item.goodsUuid,
            };
          }
          return undefined;
        }),
      );

      const boothGoodsPivotList = boothUuids.length
        ? await tx.goodsBoothPivot.findMany({
            where: {
              OR: boothUuids.map((item) => ({
                boothUuid: item.boothUuid,
                goodsUuid: item.goodsUuid,
              })),
            },
            select: {
              boothUuid: true,
              goodsUuid: true,
              quantity: true,
              prepareCount: true,
              advanceCount: true,
              booth: {
                select: {
                  name: true,
                },
              },
            },
          })
        : [];

      // 재고 검증 및 주문 데이터 생성
      const prepareGoods: Prisma.PrepareOrderedGoodsCreateManyInput[] = [];

      for (const item of dto.goodsList) {
        let boothName: string | undefined;
        const good = goodsList.find(
          (good) => good.goodsUuid === item.goodsUuid,
        );

        if (!good) {
          throw new BadRequestException(
            `상품이 존재하지 않습니다. [${item.goodsUuid}]`,
          );
        }

        // 재고 검증 로직
        if (dto.isPickup) {
          if (item.boothUuid === undefined) {
            throw new BadRequestException('부스 UUID가 필요합니다.');
          }

          const boothGoodsPivot = boothGoodsPivotList.find(
            (pivot) =>
              pivot.boothUuid === item.boothUuid &&
              pivot.goodsUuid === item.goodsUuid,
          );

          if (!boothGoodsPivot) {
            throw new BadRequestException(
              `부스에 등록되지 않은 상품입니다. [${item.goodsUuid}]`,
            );
          }

          boothName = boothGoodsPivot.booth.name;

          // 트랜잭션 내에서 실시간 재고 확인
          const currentBoothPivot = await tx.goodsBoothPivot.findUnique({
            where: {
              goodsUuid_boothUuid: {
                goodsUuid: item.goodsUuid,
                boothUuid: item.boothUuid,
              },
            },
            select: {
              quantity: true,
              prepareCount: true,
              advanceCount: true,
            },
          });

          if (!currentBoothPivot) {
            throw new BadRequestException(
              `부스 상품 정보를 찾을 수 없습니다. [${item.goodsUuid}]`,
            );
          }

          const availableQuantity =
            currentBoothPivot.quantity -
            currentBoothPivot.prepareCount -
            currentBoothPivot.advanceCount;

          if (availableQuantity < item.goodsCount) {
            throw new BadRequestException(
              `판매 수량이 초과되었습니다. 사용 가능: ${availableQuantity}, 요청: ${item.goodsCount} [${item.goodsUuid}]`,
            );
          }

          // 재고 차감 (prepareCount 증가)
          await tx.goodsBoothPivot.update({
            where: {
              goodsUuid_boothUuid: {
                goodsUuid: item.goodsUuid,
                boothUuid: item.boothUuid,
              },
            },
            data: {
              prepareCount: {
                increment: item.goodsCount,
              },
            },
          });
        } else {
          // 온라인 주문의 경우 실시간 재고 확인
          const currentGoods = await tx.goods.findUnique({
            where: { goodsUuid: item.goodsUuid },
            select: {
              quantity: true,
              boothGoodsQuantity: true,
            },
          });

          if (!currentGoods) {
            throw new BadRequestException(
              `상품 정보를 찾을 수 없습니다. [${item.goodsUuid}]`,
            );
          }

          const availableQuantity =
            currentGoods.quantity - currentGoods.boothGoodsQuantity;

          if (availableQuantity < item.goodsCount) {
            throw new BadRequestException(
              `판매 수량이 초과되었습니다. 사용 가능: ${availableQuantity}, 요청: ${item.goodsCount} [${item.goodsUuid}]`,
            );
          }

          // 재고 차감 (boothGoodsQuantity 증가)
          await tx.goods.update({
            where: { goodsUuid: item.goodsUuid },
            data: {
              boothGoodsQuantity: {
                increment: item.goodsCount,
              },
            },
          });
        }

        totalPrice += good.price * item.goodsCount;

        // 판매자별 주문 데이터 생성
        const goodsOrderItems = good.goodsUser.map((goodsUser) => {
          const goodsPrice =
            good.price * item.goodsCount * (goodsUser.ratio / 100);
          const existSalesOrder = salesOrder.find(
            (order) => order.userUuid === goodsUser.userUuid,
          );

          if (existSalesOrder) {
            existSalesOrder.totalGoodsPrice += goodsPrice;
            existSalesOrder.totalVatPrice +=
              vatTaxCalculation(goodsPrice).vatAmount;
          } else {
            salesOrder.push({
              userUuid: goodsUser.userUuid,
              totalGoodsPrice: goodsPrice,
              totalVatPrice: vatTaxCalculation(goodsPrice).vatAmount,
              orderUuid,
              orderStatus: PaymentStatusEnum.ORDER_COMPLETE,
              orderTitle:
                goodsList.length > 1
                  ? `${goodsList[0].name} 외 ${goodsList.length - 1}개`
                  : goodsList[0].name,
              payMethod: PayMethodEnum.card,
            });
          }

          return {
            orderUuid,
            deliveryStatus: null,
            goodsOrderStatus: PaymentStatusEnum.ORDER_COMPLETE,
            goodsCount: item.goodsCount,
            goodsUuid: item.goodsUuid,
            goodsName: good.name,
            goodsPrice: goodsPrice,
            vatPrice: vatTaxCalculation(goodsPrice).vatAmount,
            sellerUuid: goodsUser.userUuid,
            ratio: goodsUser.ratio,
            boothUuid: item.boothUuid,
            boothName: boothName,
          };
        });

        prepareGoods.push(...goodsOrderItems);
      }

      // 주문 제목 생성
      const paymentTitle =
        goodsList.length > 1
          ? `${goodsList[0].name} 외 ${goodsList.length - 1}개`
          : goodsList[0].name;

      // 주문 생성
      const prepareOrder = await tx.prepareOrder.create({
        data: {
          orderUuid,
          userUuid: user.id,
          orderTitle: paymentTitle,
          totalGoodsPrice: totalPrice,
          totalVatPrice: vatTaxCalculation(totalPrice).vatAmount,
          totalDeliveryPrice: null,
          payMethod: PayMethodEnum.card,
          orderStatus: PaymentStatusEnum.ORDER_COMPLETE,
        },
      });

      // 판매 주문 생성
      if (salesOrder.length > 0) {
        await tx.prepareSalesOrder.createMany({
          data: salesOrder,
        });
      }

      // 주문 상품 생성
      if (prepareGoods.length > 0) {
        await tx.prepareOrderedGoods.createMany({
          data: prepareGoods,
        });
      }

      return { prepareOrder, paymentTitle };
    });

    // 결제 페이지 데이터 반환
    return {
      orderUuid: result.prepareOrder.orderUuid,
      payple: this.paypleService.getPgData({
        goodsTitle: result.paymentTitle,
        orderUuid: result.prepareOrder.orderUuid,
        payType: dto.payMethod,
        totalPrice: result.prepareOrder.totalGoodsPrice,
        // email: user.email ?? undefined,
        // mobileNumber: 여기 들어갈꺼 정해저야지 ㅎ,
        // name: user.realName ?? undefined,
        // payplePayerId: dto.change ? undefined : payerId, // 빌링키 결제
      }),
    };
  }

  async pgData(dto: PaymentPageDto) {
    const prepareOrder = await this.prismaService.prepareOrder.findUnique({
      where: {
        orderUuid: dto.orderUuid,
      },
    });

    if (!prepareOrder) {
      const order = await this.prismaService.order.findUnique({
        where: {
          orderUuid: dto.orderUuid,
        },
      });
      if (order) {
        throw new BadRequestException('이미 결제된 주문입니다.'); // TODO 409 Error
      } else {
        throw new BadRequestException('존재하지 않는 주문입니다.');
      }
    }

    return this.paypleService.getPgData({
      orderUuid: prepareOrder.orderUuid,
      totalPrice: prepareOrder.totalGoodsPrice,
      goodsTitle: prepareOrder.orderTitle,
      payType: prepareOrder.payMethod,
    });
  }

  // WIP 웹훅에서 사용할거라 일단 남겨둠
  //  await this.prismaService.$transaction([
  //   this.prismaService.order.create({
  //     data: {
  //       payMethod: prepareOrder.payMethod,
  //       orderUuid: prepareOrder.orderUuid,
  //       linkKey: prepareOrder.linkKey,
  //       userUuid: prepareOrder.userUuid,
  //       orderTitle: prepareOrder.orderTitle,
  //       totalGoodsPrice: prepareOrder.totalGoodsPrice,
  //       totalVatPrice: prepareOrder.totalVatPrice,
  //       totalDeliveryPrice: prepareOrder.totalDeliveryPrice,
  //       orderStatus: prepareOrder.orderStatus,
  //       orderedGoods: {
  //         createMany: {
  //           data: prepareOrder.prepareOrderedGoods.map((item) => {
  //             return {
  //               orderedGoodsUuid: item.orderedGoodsUuid,
  //               deliveryStatus: item.deliveryStatus,
  //               goodsOrderStatus: item.goodsOrderStatus,
  //               goodsCount: item.goodsCount,
  //               goodsUuid: item.goodsUuid,
  //               goodsName: item.goodsName,
  //               goodsPrice: item.goodsPrice,
  //               vatPrice: item.vatPrice,
  //             };
  //           }),
  //         },
  //       },
  //     },
  //   }),
  //   this.prismaService.prepareOrder.delete({
  //     where: {
  //       orderUuid: prepareOrder.orderUuid,
  //     },
  //   }),
  //   ...prepareOrder.prepareOrderedGoods.map((orderGoods) => {
  //     return this.prismaService.goodsBoothPivot.upsert({
  //       where: {
  //         goodsUuid_boothUuid: {
  //           goodsUuid: orderGoods.goodsUuid,
  //           boothUuid: user.id, // TODO 부스 id 유저 Id
  //         },
  //       },
  //       create: {
  //         goodsUuid: orderGoods.goodsUuid,
  //         boothUuid: user.id, // TODO 부스 id 유저 Id
  //         salesCount: orderGoods.goodsCount,
  //       },
  //       update: {
  //         salesCount: {
  //           increment: orderGoods.goodsCount,
  //         },
  //         prepareCount: {
  //           decrement: orderGoods.goodsCount,
  //         },
  //       },
  //     });
  //   }),
  // ]);
}
