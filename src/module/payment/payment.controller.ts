import { ignoreTransformInterceptor } from '@common/decorators/ignore-transform-interceptor.decorator';
import { User } from '@common/decorators/jwk.decorators';
import { IsPublic } from '@common/decorators/public.decorator';
import { RequestUser } from '@common/types/token.types';
import { Body, Controller, Get, Header, Post, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { readFileSync } from 'node:fs';
import { PaymentConfirmDto } from './dto/payment-confirm-dto';
import { PaymentPageDto } from './dto/payment-page.dto';
import { PaymentCreateLinkDto } from './dto/payment.create-link.dto';
import { PaymentCreateDirectDto } from './dto/payment.create.direct.dto';
import { PaymentCreate } from './dto/payment.create.dto';
import { PaymentService } from './payment.service';

@ApiBearerAuth()
@Controller('payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @ApiOperation({
    summary: '굿즈 링크 결제 생성 판매자가 직접 익명의 유저에게 판매',
    description:
      '링크 결제를 생성합니다. 해당 링크로 QR를 생성하면 됩니다. 기한은 1시간, 1번결제 만됩니다.',
  })
  @Post('link')
  paymentCreateLink(
    @User() user: RequestUser,
    @Body() dto: PaymentCreateLinkDto,
  ) {
    return this.paymentService.paymentCreateLink(user, dto);
  }

  @ApiOperation({
    summary: '링크 결제 완료 확인',
    description: '해당 주문번호를 통해서 주문이 완료되었는지 확인하는 API',
  })
  @Get('confirm')
  paymentConfirm(@User() user: RequestUser, @Query() dto: PaymentConfirmDto) {
    return this.paymentService.paymentConfirm(user, dto);
  }

  @ApiOperation({
    summary: '직접 결제',
    description: '직접 결제 API입니다.',
  })
  @Post('direct')
  paymentDirect(
    @User() user: RequestUser,
    @Body() dto: PaymentCreateDirectDto,
  ) {
    return this.paymentService.paymentDirect(user, dto);
  }

  @ApiOperation({
    summary: '고객 결제 API',
    description: '고객 결제 API입니다.',
  })
  @Post()
  payment(@User() user: RequestUser, @Body() dto: PaymentCreate) {
    return this.paymentService.paymentOrderId(user, dto);
  }

  @ApiOperation({
    summary: '직접 결제',
    description: '직접 결제 API입니다.',
  })
  @IsPublic()
  @ignoreTransformInterceptor()
  @Header('Content-Type', 'text/html')
  @Get()
  async paymentGet(@Query() dto: PaymentPageDto) {
    const pgData = await this.paymentService.pgData(dto);
    if (!pgData) {
      return '0 원 에러 처리해야됩니다.';
    }
    const renderingPag = readFileSync(
      'src/module/payple/view/order.html',
      'utf8',
    );
    // 여기 리팩토링 필요보임 일단 되네
    // Object.entries(pgData).forEach(([key, value]) => {
    //   console.log(`{{${key}}}`);

    //   renderingPag.replace(key, `${value ?? ''}`);
    // });
    const prodMode = 'cpay';
    // if (isTest === 'test') {
    //   prodMode = 'democpay';
    // }
    return renderingPag
      .replace('{{clientKey}}', pgData.clientKey)
      .replace('{{PCD_PAY_TYPE}}', pgData.PCD_PAY_TYPE)
      .replace('{{PCD_PAY_WORK}}', pgData.PCD_PAY_WORK)
      .replace('{{PCD_CARD_VER}}', pgData.PCD_CARD_VER)
      .replace('{{PCD_PAY_GOODS}}', pgData.PCD_PAY_GOODS)
      .replace('{{PCD_PAY_TOTAL}}', pgData.PCD_PAY_TOTAL.toString())
      .replace('{{PCD_RST_URL}}', pgData.PCD_RST_URL)
      .replace('{{PCD_PAY_OID}}', pgData.PCD_PAY_OID)
      .replace('{{PCD_PAYER_NAME}}', pgData.PCD_PAYER_NAME ?? '')
      .replace('{{PCD_PAYER_HP}}', pgData.PCD_PAYER_HP ?? '')
      .replace('{{PCD_PAYER_EMAIL}}', pgData.PCD_PAYER_EMAIL ?? '')
      .replace(
        '{{PCD_PAY_TAXTOTAL}}',
        pgData.PCD_PAY_TAXTOTAL?.toString() ?? '',
      )
      .replace('{{PCD_SIMPLE_FLAG}}', pgData.PCD_SIMPLE_FLAG ?? '')
      .replace('{{PCD_PAYER_AUTHTYPE}}', pgData.PCD_PAYER_AUTHTYPE ?? '')
      .replace('{{PCD_PAYER_ID}}', pgData.PCD_PAYER_ID ?? '')
      .replace('{{MODE}}', prodMode);
  }
}
