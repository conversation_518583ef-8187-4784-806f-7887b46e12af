import { User } from '@common/decorators/jwk.decorators';
import type { RequestUser } from '@common/types/token.types';
import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { FindOrdersDto } from './dto/find-orders.dto';
import { SalesSummaryResponseDto } from './dto/sales-summary-response.dto';
import { OrderCreatorService } from './sales-order.service';

@Controller('sales-orders')
@ApiBearerAuth()
export class OrderCreatorController {
  constructor(private readonly orderCreatorService: OrderCreatorService) {}

  @Get()
  @ApiOperation({ summary: 'Find Order Histories' })
  async findOrders(@Query() dto: FindOrdersDto, @User() user: RequestUser) {
    return this.orderCreatorService.getSalesOrdersForTable(dto, user.id);
  }

  @Get('/summary')
  @ApiOperation({ summary: 'Get sales summary cards' })
  @ApiOkResponse({ type: SalesSummaryResponseDto })
  async getSalesSummary(
    @User() user: RequestUser,
    @Query() dto: FindOrdersDto,
  ) {
  return this.orderCreatorService.getSalesSummary(dto, user.id);
}


  @Get(':orderUuid')
  @ApiOperation({ summary: 'Get Order History Detail' })
  async getOrderHistory(
    @Param('orderUuid') orderUuid: string,
    @User() user: RequestUser,
  ) {
    return this.orderCreatorService.getSalesOrderHistory(orderUuid, user.id);
  }

  @Get(':orderUuid/joint-settlement')
  @ApiOperation({ summary: 'Get Joint Goods Settlement Information' })
  async getJointGoodsSettlement(
    @Param('orderUuid') orderUuid: string,
    @User() user: RequestUser,
  ) {
    return this.orderCreatorService.getJointGoodsSettlement(orderUuid, user.id);
  }

  // Additional endpoints that might be needed based on the Figma actions
  @Post(':orderUuid/goods/:orderedGoodsUuid/refund')
  @ApiOperation({ summary: 'Process Refund for Individual Item' })
  async processItemRefund(
    @Param('orderUuid') orderUuid: string,
    @Param('orderedGoodsUuid') orderedGoodsUuid: string,
    @Body() refundData: { reason: string; amount?: number },
    @User() user: RequestUser,
  ) {
    // TODO: Implement refund logic
    // This would need to be implemented based on your refund processing logic
    return { message: 'Refund processing endpoint - needs implementation' };
  }

  @Post(':orderUuid/goods/:orderedGoodsUuid/exchange')
  @ApiOperation({ summary: 'Process Exchange for Individual Item' })
  async processItemExchange(
    @Param('orderUuid') orderUuid: string,
    @Param('orderedGoodsUuid') orderedGoodsUuid: string,
    @Body() exchangeData: { reason: string; newGoodsUuid?: string },
    @User() user: RequestUser,
  ) {
    // TODO: Implement exchange logic
    // This would need to be implemented based on your exchange processing logic
    return { message: 'Exchange processing endpoint - needs implementation' };
  }

  @Post(':orderUuid/goods/:orderedGoodsUuid/cancel')
  @ApiOperation({ summary: 'Cancel Individual Item' })
  async cancelItem(
    @Param('orderUuid') orderUuid: string,
    @Param('orderedGoodsUuid') orderedGoodsUuid: string,
    @Body() cancelData: { reason: string },
    @User() user: RequestUser,
  ) {
    // TODO: Implement cancellation logic
    // This would need to be implemented based on your cancellation processing logic
    return { message: 'Cancellation processing endpoint - needs implementation' };
  }
}