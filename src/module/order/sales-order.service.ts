import { falseFilter } from '@common/lib/false-filter';
import { endUuidv7, startUuidv7, uuidv7ToDate } from '@common/lib/uuidv7';
import { BadRequestException, Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { PrismaService } from '@provider/database/prisma.service';
import { isDefined } from 'class-validator';
import { FindOrdersDto } from './dto/find-orders.dto';

@Injectable()
export class OrderCreatorService {
  constructor(private readonly prismaService: PrismaService) {}

  async getSalesOrdersForTable(dto: FindOrdersDto, userUuid: string) {
    const whereCondition: any = {
      userUuid,
      AND: falseFilter([
        isDefined(dto.orderUuid) && { orderUuid: dto.orderUuid },
        isDefined(dto.orderStatus) && { orderStatus: dto.orderStatus },
        isDefined(dto.startAt) && {
          orderUuid: { gte: startUuidv7(dto.startAt) },
        },
        isDefined(dto.endAt) && {
          orderUuid: { lte: endUuidv7(dto.endAt) },
        },
        isDefined(dto.boothUuids) && {
          orderedGoods: {
            some: {
              boothUuid: { in: dto.boothUuids },
            },
          },
        },
        isDefined(dto.goodsUuids) && {
          orderedGoods: {
            some: {
              goodsUuid: { in: dto.goodsUuids },
            },
          },
        },
      ]),
    };

    const [orders, count] = await Promise.all([
      this.prismaService.salesOrder.findMany({
        select: {
          orderUuid: true,
          orderStatus: true,
          orderTitle: true,
          payMethod: true,
          cardType: true,
          cardNumber: true,
          payout: true,
          refundedTotalGoodsPrice: true,
          refundedTotalVatPrice: true,
          refundedTotalDeliveryPrice: true,
          orderedGoods: {
            select: {
              orderedGoodsUuid: true,
              goodsUuid: true,
              goodsName: true,
              goodsPrice: true,
              vatPrice: true,
              goodsCount: true,
              ratio: true,
              boothUuid: true,
              boothName: true,
              deliveryStatus: true,
              exchangeStatus: true,
              goodsOrderStatus: true,
              refundedGoodsPrice: true,
              refundedVatPrice: true,
              exchangeRequestDate: true,
              exchangeReason: true,
              exchangeConfirmationDate: true,
              cancellationRequestDate: true,
              cancellationReason: true,
              cancellationConfirmationDate: true,
              sellerUuid: true,
            },
          },
          totalGoodsPrice: true,
          totalVatPrice: true,
          totalDeliveryPrice: true,
          order: {
            select: {
              user: {
                select: {
                  userUuid: true,
                  nickname: true,
                  handle: true,
                  profileImg: true,
                  email: true,
                  realName: true,
                },
              },
              linkKey: true,
            },
          },
        },
        where: whereCondition,
        ...dto.toPrismaPaging(),
        orderBy: { orderUuid: Prisma.SortOrder.desc },
      }),
      this.prismaService.salesOrder.count({ where: whereCondition }),
    ]);

    return dto.createResponse(
      orders.map((order) => ({
        orderUuid: order.orderUuid,
        orderStatus: order.orderStatus,
        orderTitle: order.orderTitle,
        payMethod: order.payMethod,
        cardType: order.cardType,
        cardNumber: order.cardNumber,
        payout: order.payout,

        // Customer information
        customer: order.order.user
          ? {
              userUuid: order.order.user.userUuid,
              nickname: order.order.user.nickname,
              handle: order.order.user.handle,
              profileImg: order.order.user.profileImg,
              email: order.order.user.email,
              realName: order.order.user.realName,
            }
          : null,

        // Booth and goods information
        goodsNames: Array.from(
          new Set(order.orderedGoods.map((goods) => goods.goodsName)),
        ),
        boothNames: Array.from(
          new Set(order.orderedGoods.map((goods) => goods.boothName)),
        ),

        // Financial information
        totalGoodsPrice: order.totalGoodsPrice,
        totalVatPrice: order.totalVatPrice,
        totalDeliveryPrice: order.totalDeliveryPrice,
        totalPrice:
          order.totalGoodsPrice +
          order.totalVatPrice +
          (order.totalDeliveryPrice ?? 0),

        // Refund information
        refundedTotalGoodsPrice: order.refundedTotalGoodsPrice,
        refundedTotalVatPrice: order.refundedTotalVatPrice,
        refundedTotalDeliveryPrice: order.refundedTotalDeliveryPrice,
        totalRefundedAmount:
          (order.refundedTotalGoodsPrice ?? 0) +
          (order.refundedTotalVatPrice ?? 0) +
          (order.refundedTotalDeliveryPrice ?? 0),

        // Settlement information
        settlementAmount:
          order.totalGoodsPrice +
          order.totalVatPrice +
          (order.totalDeliveryPrice ?? 0) -
          ((order.refundedTotalGoodsPrice ?? 0) +
            (order.refundedTotalVatPrice ?? 0) +
            (order.refundedTotalDeliveryPrice ?? 0)),

        // Order items with individual status
        orderedGoods: order.orderedGoods.map((goods) => ({
          orderedGoodsUuid: goods.orderedGoodsUuid,
          goodsUuid: goods.goodsUuid,
          goodsName: goods.goodsName,
          goodsPrice: goods.goodsPrice,
          vatPrice: goods.vatPrice,
          goodsCount: goods.goodsCount,
          ratio: goods.ratio,
          boothUuid: goods.boothUuid,
          boothName: goods.boothName,
          sellerUuid: goods.sellerUuid,
          deliveryStatus: goods.deliveryStatus,
          exchangeStatus: goods.exchangeStatus,
          goodsOrderStatus: goods.goodsOrderStatus,
          refundedGoodsPrice: goods.refundedGoodsPrice,
          refundedVatPrice: goods.refundedVatPrice,

          // Individual item actions availability
          canRefund:
            goods.goodsOrderStatus === 'ORDER_COMPLETE' &&
            !goods.refundedGoodsPrice,
          canExchange:
            goods.goodsOrderStatus === 'ORDER_COMPLETE' &&
            !goods.exchangeStatus,
          canCancel: goods.goodsOrderStatus === 'ORDER_COMPLETE',

          // Exchange/Cancellation tracking
          exchangeRequestDate: goods.exchangeRequestDate,
          exchangeReason: goods.exchangeReason,
          exchangeConfirmationDate: goods.exchangeConfirmationDate,
          cancellationRequestDate: goods.cancellationRequestDate,
          cancellationReason: goods.cancellationReason,
          cancellationConfirmationDate: goods.cancellationConfirmationDate,

          // Settlement calculation for individual item
          itemTotalPrice:
            (goods.goodsPrice + goods.vatPrice) * goods.goodsCount,
          itemSettlementAmount:
            (goods.goodsPrice + goods.vatPrice) * goods.goodsCount -
            ((goods.refundedGoodsPrice ?? 0) + (goods.refundedVatPrice ?? 0)),
          settlementPercentage: goods.ratio,
        })),

        // Overall order summary
        totalItemCount: order.orderedGoods.reduce(
          (sum, goods) => sum + goods.goodsCount,
          0,
        ),
        uniqueGoodsCount: order.orderedGoods.length,
        hasRefunds: order.orderedGoods.some(
          (goods) => (goods.refundedGoodsPrice ?? 0) > 0,
        ),
        hasExchanges: order.orderedGoods.some((goods) => goods.exchangeStatus),
        hasCancellations: order.orderedGoods.some(
          (goods) => goods.cancellationRequestDate,
        ),

        // Status indicators
        overallStatus: this.determineOverallStatus(order.orderedGoods),

        createdAt: uuidv7ToDate(order.orderUuid),

        // Link key for payment tracking
        linkKey: order.order.linkKey,
      })),
      count,
    );
  }

  async getSalesSummary(dto: FindOrdersDto, userUuid: string) {
    const where: Prisma.SalesOrderWhereInput = {
      userUuid,
      AND: falseFilter([
        isDefined(dto.startAt) && {
          orderUuid: { gte: startUuidv7(dto.startAt) },
        },
        isDefined(dto.endAt) && {
          orderUuid: { lte: endUuidv7(dto.endAt) },
        },
      ]),
    };

    const orders = await this.prismaService.salesOrder.findMany({
      where,
      select: {
        totalGoodsPrice: true,
        totalVatPrice: true,
        totalDeliveryPrice: true,
        refundedTotalGoodsPrice: true,
        refundedTotalVatPrice: true,
        refundedTotalDeliveryPrice: true,
        payout: true,
      },
    });

    let salesAmount = 0;
    let refundAmount = 0;
    let jointGoodsRemainingAmount = 0;

    for (const o of orders) {
      const orderTotal =
        o.totalGoodsPrice + o.totalVatPrice + (o.totalDeliveryPrice ?? 0);

      const refundTotal =
        (o.refundedTotalGoodsPrice ?? 0) +
        (o.refundedTotalVatPrice ?? 0) +
        (o.refundedTotalDeliveryPrice ?? 0);

      salesAmount += orderTotal;
      refundAmount += refundTotal;

      if (o.payout === null) {
        jointGoodsRemainingAmount += orderTotal - refundTotal;
      }
    }

    const revenue = salesAmount - refundAmount;
    const numberOfSales = orders.length;

    return {
      salesAmount,
      revenue,
      jointGoodsRemainingAmount,
      numberOfSales,
    };
  }

  async getSalesOrderHistory(orderUuid: string, userUuid: string) {
    const order = await this.prismaService.salesOrder.findUnique({
      select: {
        orderUuid: true,
        orderStatus: true,
        orderTitle: true,
        payMethod: true,
        cardType: true,
        cardNumber: true,
        payout: true,
        refundedTotalGoodsPrice: true,
        refundedTotalVatPrice: true,
        refundedTotalDeliveryPrice: true,
        orderedGoods: {
          select: {
            orderedGoodsUuid: true,
            goodsUuid: true,
            goodsName: true,
            goodsPrice: true,
            vatPrice: true,
            goodsCount: true,
            ratio: true,
            boothUuid: true,
            boothName: true,
            sellerUuid: true,
            deliveryStatus: true,
            exchangeStatus: true,
            goodsOrderStatus: true,
            refundedGoodsPrice: true,
            refundedVatPrice: true,
            exchangeRequestDate: true,
            exchangeReason: true,
            exchangeConfirmationDate: true,
            cancellationRequestDate: true,
            cancellationReason: true,
            cancellationConfirmationDate: true,
          },
        },
        totalGoodsPrice: true,
        totalVatPrice: true,
        totalDeliveryPrice: true,
        order: {
          select: {
            user: {
              select: {
                userUuid: true,
                nickname: true,
                handle: true,
                profileImg: true,
                email: true,
                realName: true,
              },
            },
            linkKey: true,
          },
        },
      },
      where: {
        orderUuid_userUuid: {
          orderUuid,
          userUuid,
        },
      },
    });

    if (!order) {
      throw new BadRequestException('Order not found');
    }

    return {
      orderUuid: order.orderUuid,
      orderStatus: order.orderStatus,
      orderTitle: order.orderTitle,
      payMethod: order.payMethod,
      cardType: order.cardType,
      cardNumber: order.cardNumber,
      payout: order.payout,

      // Customer information
      customer: order.order.user
        ? {
            userUuid: order.order.user.userUuid,
            nickname: order.order.user.nickname,
            handle: order.order.user.handle,
            profileImg: order.order.user.profileImg,
            email: order.order.user.email,
            realName: order.order.user.realName,
          }
        : null,

      // Booth information
      boothNames: Array.from(
        new Set(order.orderedGoods.map((goods) => goods.boothName)),
      ),

      // Financial summary
      totalGoodsPrice: order.totalGoodsPrice,
      totalVatPrice: order.totalVatPrice,
      totalDeliveryPrice: order.totalDeliveryPrice,
      totalPrice:
        order.totalGoodsPrice +
        order.totalVatPrice +
        (order.totalDeliveryPrice ?? 0),

      // Refund summary
      refundedTotalGoodsPrice: order.refundedTotalGoodsPrice,
      refundedTotalVatPrice: order.refundedTotalVatPrice,
      refundedTotalDeliveryPrice: order.refundedTotalDeliveryPrice,
      totalRefundedAmount:
        (order.refundedTotalGoodsPrice ?? 0) +
        (order.refundedTotalVatPrice ?? 0) +
        (order.refundedTotalDeliveryPrice ?? 0),

      // Settlement summary
      settlementAmount:
        order.totalGoodsPrice +
        order.totalVatPrice +
        (order.totalDeliveryPrice ?? 0) -
        ((order.refundedTotalGoodsPrice ?? 0) +
          (order.refundedTotalVatPrice ?? 0) +
          (order.refundedTotalDeliveryPrice ?? 0)),

      // Detailed goods information
      orderedGoods: order.orderedGoods.map((goods) => ({
        orderedGoodsUuid: goods.orderedGoodsUuid,
        goodsUuid: goods.goodsUuid,
        goodsName: goods.goodsName,
        goodsPrice: goods.goodsPrice,
        vatPrice: goods.vatPrice,
        goodsCount: goods.goodsCount,
        ratio: goods.ratio,
        boothUuid: goods.boothUuid,
        boothName: goods.boothName,
        sellerUuid: goods.sellerUuid,
        deliveryStatus: goods.deliveryStatus,
        exchangeStatus: goods.exchangeStatus,
        goodsOrderStatus: goods.goodsOrderStatus,
        refundedGoodsPrice: goods.refundedGoodsPrice,
        refundedVatPrice: goods.refundedVatPrice,

        // Action availability
        canRefund:
          goods.goodsOrderStatus === 'ORDER_COMPLETE' &&
          !goods.refundedGoodsPrice,
        canExchange:
          goods.goodsOrderStatus === 'ORDER_COMPLETE' && !goods.exchangeStatus,
        canCancel: goods.goodsOrderStatus === 'ORDER_COMPLETE',

        // Exchange/Cancellation details
        exchangeRequestDate: goods.exchangeRequestDate,
        exchangeReason: goods.exchangeReason,
        exchangeConfirmationDate: goods.exchangeConfirmationDate,
        cancellationRequestDate: goods.cancellationRequestDate,
        cancellationReason: goods.cancellationReason,
        cancellationConfirmationDate: goods.cancellationConfirmationDate,

        // Financial calculations
        itemTotalPrice: (goods.goodsPrice + goods.vatPrice) * goods.goodsCount,
        itemSettlementAmount:
          (goods.goodsPrice + goods.vatPrice) * goods.goodsCount -
          ((goods.refundedGoodsPrice ?? 0) + (goods.refundedVatPrice ?? 0)),
        settlementPercentage: goods.ratio,
      })),

      // Order statistics
      totalItemCount: order.orderedGoods.reduce(
        (sum, goods) => sum + goods.goodsCount,
        0,
      ),
      uniqueGoodsCount: order.orderedGoods.length,
      hasRefunds: order.orderedGoods.some(
        (goods) => (goods.refundedGoodsPrice ?? 0) > 0,
      ),
      hasExchanges: order.orderedGoods.some((goods) => goods.exchangeStatus),
      hasCancellations: order.orderedGoods.some(
        (goods) => goods.cancellationRequestDate,
      ),

      // Overall status
      overallStatus: this.determineOverallStatus(order.orderedGoods),

      createdAt: uuidv7ToDate(order.orderUuid),

      // Link key for payment tracking
      linkKey: order.order.linkKey,
    };
  }

  private determineOverallStatus(orderedGoods: any[]): string {
    const statuses = orderedGoods.map((goods) => goods.goodsOrderStatus);
    const uniqueStatuses = Array.from(new Set(statuses));

    if (uniqueStatuses.length === 1) {
      return uniqueStatuses[0];
    }

    // Mixed statuses
    if (
      statuses.some(
        (status) =>
          status === 'ORDER_CANCEL' || status === 'PARTIAL_ORDER_CANCEL',
      )
    ) {
      return 'PARTIAL_ORDER_CANCEL';
    }

    if (statuses.some((status) => status === 'EXCHANGE_REQUEST')) {
      return 'MIXED_WITH_EXCHANGE';
    }

    return 'MIXED_STATUS';
  }

  // Additional method for joint goods settlement functionality
  async getJointGoodsSettlement(orderUuid: string, userUuid: string) {
    const order = await this.getSalesOrderHistory(orderUuid, userUuid);

    // TODO: 체크
    // Group goods by booth for settlement
    const boothSettlement = order.orderedGoods.reduce(
      (acc, goods) => {
        if (goods.boothUuid) {
          if (!acc[goods.boothUuid]) {
            acc[goods.boothUuid] = {
              boothUuid: goods.boothUuid,
              boothName: goods.boothName,
              items: [],
              totalAmount: 0,
              totalRefunded: 0,
              settlementAmount: 0,
            };
          }

          acc[goods.boothUuid].items.push(goods);
          acc[goods.boothUuid].totalAmount += goods.itemTotalPrice;
          acc[goods.boothUuid].totalRefunded +=
            (goods.refundedGoodsPrice ?? 0) + (goods.refundedVatPrice ?? 0);
          acc[goods.boothUuid].settlementAmount += goods.itemSettlementAmount;
        }

        return acc;
      },
      {} as Record<string, any>,
    );

    return {
      orderUuid: order.orderUuid,
      orderStatus: order.orderStatus,
      totalSettlementAmount: order.settlementAmount,
      boothSettlements: Object.values(boothSettlement),
      canProcessJointSettlement:
        order.payout === null && order.overallStatus === 'ORDER_COMPLETE',
    };
  }
}
