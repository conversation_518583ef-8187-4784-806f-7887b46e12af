import { DateRangeDto } from '@common/dto/date-range.dto';
import { PaginationDto } from '@common/dto/pagination.dto';
import { ApiPropertyOptional, IntersectionType } from '@nestjs/swagger';
import { PaymentStatusEnum } from '@prisma/client';
import { IsEnum, IsOptional, IsUUID } from 'class-validator';

export class FindOrdersDto extends IntersectionType(
  DateRangeDto,
  PaginationDto,
) {
  @ApiPropertyOptional({
    description: 'orderUuid to find',
  })
  @IsUUID(7)
  @IsOptional()
  orderUuid?: string;

  @ApiPropertyOptional({
    description: 'order status to find',
    enum: PaymentStatusEnum,
    enumName: 'PaymentStatusEnum',
  })
  @IsEnum(PaymentStatusEnum)
  @IsOptional()
  orderStatus?: PaymentStatusEnum;

  @ApiPropertyOptional({
    description: 'boothUuid list that order is created',
    type: [String],
  })
  @IsUUID(7, { each: true })
  @IsOptional()
  boothUuids?: string[];

  @ApiPropertyOptional({
    description: 'goodsUuid list that order is created',
    type: [String],
  })
  @IsUUID(7, { each: true })
  @IsOptional()
  goodsUuids?: string[];
}
