import { Modu<PERSON> } from '@nestjs/common';
import { OrderController } from './order.controller';
import { OrderService } from './order.service';
import { OrderCreatorController } from './sales-order.controller';
import { OrderCreatorService } from './sales-order.service';

@Module({
  controllers: [OrderController, OrderCreatorController],
  providers: [OrderService, OrderCreatorService],
})
export class OrderModule {}
