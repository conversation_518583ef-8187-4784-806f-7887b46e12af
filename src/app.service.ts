import { Injectable } from '@nestjs/common';
import { PrismaService } from '@provider/database/prisma.service';

@Injectable()
export class AppService {
  constructor(private readonly prismaService: PrismaService) {}

  getHello() {
    return 'https://drive.google.com/file/d/1vlgrBsY2IAiy7f0iAddA30y8IswaF-38/view?usp=drive_link';
  }

  async getLanding() {
    const goods = await this.prismaService.goods.findFirst({
      select: {
        goodsUuid: true,
        imageUrls: true,
        name: true,
      },
    });

    // 오류 대응 디폴트
    if (!goods) {
      return {
        goodsUuid: '01932651-c99c-7a40-8274-e5f37dbe0369',
        goodsName: '몰루 아카이브',
        imageUrl: 'https://static.sherry.gg/dodo-test-buck/boothinfo.webp',
      };
    }

    return {
      goodsUuid: goods.goodsUuid,
      goodsName: goods.name,
      imageUrl: 'https://static.sherry.gg/dodo-test-buck/boothinfo.webp',
    };
  }

  async landingCount() {
    return (await this.prismaService.user.count()) + 327;
  }
}
