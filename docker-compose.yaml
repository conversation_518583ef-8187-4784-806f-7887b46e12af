services:
  db:
    image: postgres:17-alpine
    ports:
      - '5432:5432'
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=postgres
    volumes:
      - ./postgres.conf:/opt/bitnami/postgresql/conf/postgresql.conf
      - db_data:/bitnami/postgresql
    user: root

  # db-test:
  #   image: postgres:16-alpine
  #   ports:
  #     - '5433:5432'
  #   restart: always
  #   environment:
  #     - POSTGRES_USER=postgres
  #     - POSTGRES_PASSWORD=postgres
  #     - POSTGRES_DB=postgres
  #   volumes:
  #     - ./postgres.conf:/opt/bitnami/postgresql/conf/postgresql.conf
  #     - test_db_data:/bitnami/postgresql
  #   user: root

  sherry:
    image: dony435/pnpm:22-alpine
    ports:
      - '4000:4000'
      - '9229:9229'
      - '5555:5555'
    volumes:
      - .:/app
    working_dir: /app
    # build:
    #   context: .
    #   dockerfile: Dockerfile
    command: pnpm start:debug
    restart: unless-stopped
    env_file:
      - development.env
    environment:
      - POD_NAME=service-name
    depends_on:
      db:
        condition: service_started
      minio:
        condition: service_started
    platform: linux/amd64

  minio:
    image: minio/minio:RELEASE.2025-04-22T22-12-26Z
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minio
      MINIO_ROOT_PASSWORD: miniopass
    restart: always
    ports:
      - '9000:9000'
      - '9001:9001'
    volumes:
      - data_minio:/data

  redis:
      image: bitnami/redis:7.0.12
      container_name: redis-1
      ports:
          - '26379:6379'
      restart: always
      volumes:
          - redis_data:/bitnami/redis/data
      environment:
          - REDIS_PASS=redis
      command: |
          bash -euxc "redis-server --requirepass $$REDIS_PASS"
      user: root

volumes:
  db_data:
  test_db_data:
  data_minio:
  redis_data:
