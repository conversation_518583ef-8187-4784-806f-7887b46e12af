# Booth Soft Delete Implementation

This document describes the implementation of soft delete functionality for the `Booth` model in the Sherry backend application.

## Overview

Soft delete allows records to be marked as deleted without actually removing them from the database. This provides the ability to restore deleted records and maintain data integrity for audit purposes.

## Implementation Details

### 1. Database Schema Changes

Added a `deletedAt` field to the `Booth` model in `prisma/booth.prisma`:

```prisma
model Booth {
  // ... existing fields
  deletedAt         DateTime?     @map("deleted_at") @db.Timestamptz(6) /// 삭제일시
  // ... relations
}
```

### 2. Migration

Generated migration file: `20250723122526_add_deleted_at_on_booth_table`

```sql
-- AlterTable
ALTER TABLE "booth" ADD COLUMN     "deleted_at" TIMESTAMP(6);
```

### 3. Middleware Configuration

Updated `src/provider/database/prisma.middleware.ts` to include `Booth` in the soft delete table list:

```typescript
function isSoftDeleteTable(modelName?: string) {
  return modelName && ['Goods', 'GoodsBoothPivot', 'Booth'].includes(modelName);
}
```

The middleware automatically:
- Converts `delete` operations to `update` operations that set `deletedAt`
- Filters out soft-deleted records in `find` operations by default
- Supports special `deletedAt: 'all'` value to include soft-deleted records

### 4. Service Layer Updates

#### BoothCreatorService

**Enhanced Methods:**
- `deleteBoothByBoothUuid()`: Now performs soft delete via middleware
- `getBoothWithSoftDeleted()`: Get booth with option to include soft-deleted records
- `restoreBoothByBoothUuid()`: Restore a soft-deleted booth

**Key Features:**
- Maintains ticket synchronization during delete/restore operations
- Proper permission checking (admin users only)
- Error handling for non-existent booths

#### BoothService

**Note:** The BoothService maintains its existing functionality with automatic soft delete filtering through middleware.

### 5. Backward Compatibility

The implementation maintains full backward compatibility:
- Existing API endpoints continue to work without changes
- Default behavior excludes soft-deleted records
- No breaking changes to existing functionality

## Usage Examples

### Soft Delete a Booth

```typescript
// This will set deletedAt timestamp instead of removing the record
await boothCreatorService.deleteBoothByBoothUuid(userUuid, boothUuid);
```

### Get Booth (Excluding Soft-Deleted)

```typescript
// Default behavior - excludes soft-deleted records
const booth = await boothService.getBoothByBoothUuid(boothUuid);
```

### Get Booth (Including Soft-Deleted)

```typescript
// Include soft-deleted records using BoothCreatorService
const booth = await boothCreatorService.getBoothWithSoftDeleted(userUuid, boothUuid, true);
```

### Restore a Soft-Deleted Booth

```typescript
// Restore by setting deletedAt to null
await boothCreatorService.restoreBoothByBoothUuid(userUuid, boothUuid);
```

## Testing

Comprehensive test suite in `src/module/booth/booth.soft-delete.spec.ts` covers:
- Soft delete operations
- Record retrieval with/without soft-deleted records
- Restoration functionality
- Error handling scenarios

## Next Steps

### Required Actions

1. **Apply Migration**: Run the migration to add the `deletedAt` column to the database
2. **Regenerate Prisma Client**: Update the Prisma client to include the new field
3. **Update API Documentation**: Document any new endpoints for restoration functionality

### Optional Enhancements

1. **Admin Endpoints**: Add REST endpoints for booth restoration
2. **Bulk Operations**: Implement bulk soft delete/restore operations
3. **Audit Logging**: Add logging for delete/restore operations
4. **Cleanup Jobs**: Implement scheduled jobs to permanently delete old soft-deleted records

## Migration Commands

To apply the migration:

```bash
# Generate and apply migration
docker exec -it sherry-backend-sherry-1 pnpm prisma migrate dev

# Regenerate Prisma client
docker exec -it sherry-backend-sherry-1 pnpm prisma generate
```

## Related Files

- `prisma/booth.prisma` - Schema definition
- `src/provider/database/prisma.middleware.ts` - Soft delete middleware
- `src/module/booth/booth.creator.service.ts` - Creator service with soft delete methods
- `src/module/booth/booth.service.ts` - Public service (unchanged, uses middleware for soft delete)
- `src/module/booth/booth.soft-delete.spec.ts` - Test suite
- `prisma/migrations/20250723122526_add_deleted_at_on_booth_table/` - Migration files

## Notes

- The implementation follows the existing patterns used for `Goods` and `GoodsBoothPivot` models
- Ticket synchronization is properly handled during delete and restore operations
- All existing functionality remains unchanged and backward compatible
- The middleware approach ensures consistent behavior across all Prisma operations
