{
  "editor.tabSize": 2,
  "editor.detectIndentation": false,
  "search.exclude": {
    "package-lock.json": true,
    "yarn.lock": true,
    "yarn-error.log": true,
    "pnpm-lock.yaml": true
  },
  "editor.defaultFormatter": "dbaeumer.vscode-eslint",
  "editor.formatOnSave": false,
  "editor.codeActionsOnSave": [
    "source.organizeImports",
    "source.addMissingImports",
    "source.fixAll.eslint"
  ],
  "eslint.experimental.useFlatConfig": true,
  // Multiple language settings for json and jsonc files
  "[json][jsonc]": {
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "typescript.tsdk": "node_modules/typescript/lib",
  "cSpell.words": [
    "authuser",
    "cust",
    "followee",
    "kakao",
    "Kakaopay",
    "Otmo",
    "Payple",
    "supertoss",
    "uuidv",
    "yukiflake"
  ],
  "prisma-smart-formatter.typescript.defaultFormatter": "esbenp.prettier-vscode",
  "prisma-smart-formatter.typescriptreact.defaultFormatter": "esbenp.prettier-vscode",
  "prisma-smart-formatter.prisma.defaultFormatter": "Prisma.prisma",
  "[prisma]": {
    "editor.defaultFormatter": "Prisma.prisma"
  }
}
