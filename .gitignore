*.env
!test*.env

# compiled output
/dist
/node_modules

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Yarn
.yarn/*
!.yarn/patches
!.yarn/releases
!.yarn/plugins
!.yarn/sdks
!.yarn/versions

.npmrc

/shuttle-infra-data-shuttle-postgresql-read-0-pvc-e37a6b73-a24d-4495-9c16-26f5708ee8e1
/shuttle-infra-data-shuttle-postgresql-primary-0-pvc-1d83a8f0-e770-4876-890d-2b61c5b41223
/sherry-infra-data-sherry-postgres-primary-0-pvc-93ff651b-e406-4a1f-a8bf-9f4a88ed8dd5

info-extract.tar.gz