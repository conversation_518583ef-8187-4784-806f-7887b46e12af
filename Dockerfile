FROM node:22.15-alpine
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable
ARG DEBIAN_FRONTEND=noninteractive
ENV TZ="Asia/Seoul"
RUN apk add --no-cache bash tzdata

COPY ./wait-for-it.sh /wait-for-it.sh
RUN chmod +x /wait-for-it.sh

# source
WORKDIR /app
COPY ./package.json .
COPY ./pnpm-lock.yaml .
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
RUN pnpm i --ignore-scripts

# docker 내에서도 동일하도록 file 수정
RUN sed -i 's/if (ext \&\& opts.module \&\& typeof opts.module === "object") {/if (false \&\& ext \&\& opts.module \&\& typeof opts.module === "object") {/g' /app/node_modules/@swc/cli/lib/swc/util.js

# 반드시 .dockerignore 에서 제외 파일 관리 해야함
COPY . .
RUN pnpm run build

CMD [ "bash", "-euxc", "/wait-for-it.sh $DB_HOSTNAME:$DB_PORT --strict -- pnpm start:prod"]