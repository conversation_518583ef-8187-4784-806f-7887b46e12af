{"name": "sherry-backend", "version": "1.0.0", "description": "Sherry 플랫폼 NestJS 백엔드 boilerplate", "author": "Sherry 백엔드팀", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "pnpm prisma:generate && nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "pnpm start --watch --type-check", "start:debug": "pnpm prisma:migrate && pnpm start:dev --debug 0.0.0.0", "start:prod": "node dist/main", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate deploy", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watchAll", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json --forceExit --silent", "test:e2e:watch": "pnpm prisma:migrate && prisma db seed && jest --config ./test/jest-e2e.json --watchAll --detect<PERSON><PERSON>Handles", "test:local": "pnpm prisma:migrate && prisma db seed && jest --config ./test/jest-local.js --watchAll --detect<PERSON><PERSON>Handles", "test:local:ext": "jest --config ./test/jest-local.js"}, "dependencies": {"@fastify/cookie": "^11.0.2", "@fastify/cors": "^11.0.1", "@fastify/static": "^8.1.1", "@golevelup/nestjs-modules": "^0.7.2", "@liaoliaots/nestjs-redis": "^10.0.0", "@nestjs/common": "^11.1.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/platform-fastify": "^11.1.0", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@prisma/client": "^6.9.0", "app-root-path": "^3.1.0", "axios": "^1.9.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.8.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "lodash": "^4.17.21", "minio": "^8.0.5", "nest-winston": "^1.10.2", "nodemailer": "^7.0.3", "redis": "^5.6.0", "reflect-metadata": "^0.2.2", "rimraf": "^6.0.1", "rxjs": "^7.8.2", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.26.0", "@golevelup/ts-jest": "^0.6.2", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.0", "@swc/cli": "^0.7.7", "@swc/core": "^1.11.31", "@swc/core-linux-x64-musl": "^1.11.31", "@swc/jest": "^0.2.38", "@types/app-root-path": "^3.1.0", "@types/compression": "^1.7.5", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "^22.15.16", "@types/nodemailer": "^6.4.17", "@types/redis": "^4.0.11", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "eslint": "^9.26.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^10.1.3", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-sonarjs": "^3.0.2", "fastify": "^5.3.3", "globals": "^16.1.0", "jest": "^29.7.0", "jest-mock-extended": "4.0.0-beta1", "prettier": "^3.5.3", "prisma": "^6.9.0", "prisma-markdown": "^3.0.1", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0", "webpack": "^5.99.8"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "moduleNameMapper": {"^@config/(.*)$": "<rootDir>/src/config/$1", "^@common/(.*)$": "<rootDir>/src/common/$1", "^@provider/(.*)$": "<rootDir>/src/provider/$1", "^@module/(.*)$": "<rootDir>/src/module/$1"}, "setupFiles": ["<rootDir>/test/jest-local.env.ts"], "preset": "ts-jest", "clearMocks": true}, "packageManager": "pnpm@10.9.0+sha512.0486e394640d3c1fb3c9d43d49cf92879ff74f8516959c235308f5a8f62e2e19528a65cdc2a3058f587cde71eba3d5b56327c8c33a97e4c4051ca48a10ca2d5f", "prisma": {"seed": "ts-node prisma/seed.ts", "schema": "./prisma/"}}